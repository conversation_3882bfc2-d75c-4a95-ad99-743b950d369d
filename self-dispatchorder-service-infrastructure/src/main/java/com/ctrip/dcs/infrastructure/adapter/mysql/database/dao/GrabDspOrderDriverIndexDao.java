package com.ctrip.dcs.infrastructure.adapter.mysql.database.dao;

import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderDriverIndexPO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderSnapshotPO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.DalTableDao;
import com.ctrip.platform.dal.dao.helper.DalDefaultJpaParser;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.sql.Types;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class GrabDspOrderDriverIndexDao {

    private DalTableDao<GrabDspOrderDriverIndexPO> client;

    public GrabDspOrderDriverIndexDao() throws SQLException {
        this.client = new DalTableDao<>(new DalDefaultJpaParser<>(GrabDspOrderDriverIndexPO.class));
    }

    public List<GrabDspOrderDriverIndexPO> queryByDspOrderId(String dspOrderId) throws SQLException {
        if (StringUtils.isBlank(dspOrderId)) {
            return Lists.newArrayList();
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.equal("dsp_order_id", dspOrderId, Types.VARCHAR, false);
        return client.query(builder, DalHints.createIfAbsent(null).inAllShards().sequentialExecute());
    }

    public List<GrabDspOrderDriverIndexPO> query(String dspOrderId, List<Long> driverIds) throws SQLException {
        if (StringUtils.isBlank(dspOrderId) || CollectionUtils.isEmpty(driverIds)) {
            return Lists.newArrayList();
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.equal("dsp_order_id", dspOrderId, Types.VARCHAR, false);
        builder.and();
        builder.in("driver_id", driverIds, Types.BIGINT, false);
        return client.query(builder, DalHints.createIfAbsent(null).tableShardBy("driver_id"));
    }

    public void insert(List<GrabDspOrderDriverIndexPO> list) throws SQLException {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        client.combinedInsert(hints, list);
    }

    public void update(List<GrabDspOrderDriverIndexPO> list) throws SQLException {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        client.batchUpdate(hints, list);
    }

    public List<GrabDspOrderDriverIndexPO> queryByDriverId(Long driverId, String categoryCode) throws SQLException {
        if (Objects.isNull(driverId)) {
            return Lists.newArrayList();
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.equal("driver_id", driverId, Types.BIGINT, false);
        builder.and();
        builder.equal("category_code", categoryCode, Types.VARCHAR, false);
        builder.and();
        builder.in("is_valid", Lists.newArrayList(0, 1), Types.INTEGER, false);
        return client.query(builder, DalHints.createIfAbsent(null));
    }

    public Long queryCountGrabPushTime(Date startTime, Date endTime) throws SQLException {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return 0L;
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        builder.greaterThan("grab_push_time_bj", startTime, Types.TIMESTAMP, false);
        builder.and();
        builder.lessThan("grab_push_time_bj", endTime, Types.TIMESTAMP, false);
        builder.and();
        builder.equal("is_valid", 1, Types.INTEGER, false);
        Number count = client.count(builder, DalHints.createIfAbsent(null).inAllShards().sequentialExecute());
        return count == null ? NumberUtils.LONG_ZERO : count.longValue();
    }

    public List<GrabDspOrderDriverIndexPO> queryGrabPushTime(Date startTime, Date endTime, Integer page, Integer size) throws SQLException {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return Lists.newArrayList();
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.greaterThan("grab_push_time_bj", startTime, Types.TIMESTAMP, false);
        builder.and();
        builder.lessThan("grab_push_time_bj", endTime, Types.TIMESTAMP, false);
        builder.and();
        builder.equal("is_valid", 1, Types.INTEGER, false);
        builder.atPage(page, size);
        return client.query(builder, DalHints.createIfAbsent(null).inAllShards().sequentialExecute());
    }

    public Long queryCountBroadcastPushTime(Date startTime, Date endTime) throws SQLException {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return 0L;
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        builder.greaterThan("broadcast_push_time_bj", startTime, Types.TIMESTAMP, false);
        builder.and();
        builder.lessThan("broadcast_push_time_bj", endTime, Types.TIMESTAMP, false);
        builder.and();
        builder.equal("is_valid", 1, Types.INTEGER, false);
        builder.and();
        builder.equal("is_broadcast", 1, Types.INTEGER, false);
        Number count = client.count(builder, DalHints.createIfAbsent(null).inAllShards().sequentialExecute());
        return count == null ? NumberUtils.LONG_ZERO : count.longValue();
    }

    public List<GrabDspOrderDriverIndexPO> queryBroadcastPushTime(Date startTime, Date endTime, Integer page, Integer size) throws SQLException {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return Lists.newArrayList();
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.greaterThan("broadcast_push_time_bj", startTime, Types.TIMESTAMP, false);
        builder.and();
        builder.lessThan("broadcast_push_time_bj", endTime, Types.TIMESTAMP, false);
        builder.and();
        builder.equal("is_valid", 1, Types.INTEGER, false);
        builder.and();
        builder.equal("is_broadcast", 1, Types.INTEGER, false);
        builder.atPage(page, size);
        return client.query(builder, DalHints.createIfAbsent(null).inAllShards().sequentialExecute());
    }

    public List<GrabDspOrderDriverIndexPO> queryByEstimatedUseTime(Long driverId, Date beginTime, Date endTime, String categoryCode) throws SQLException {
        if (Objects.isNull(driverId)) {
            return Lists.newArrayList();
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.equal("driver_id", driverId, Types.BIGINT, false);
        builder.and();
        builder.equal("category_code", categoryCode, Types.VARCHAR, false);
        builder.and();
        builder.in("is_valid", Lists.newArrayList(0, 1), Types.INTEGER, false);
        builder.and();
        builder.greaterThan("estimated_use_time_bj", beginTime, Types.TIMESTAMP, false);
        builder.and();
        builder.lessThan("estimated_use_time_bj", endTime, Types.TIMESTAMP, false);
        return client.query(builder, DalHints.createIfAbsent(null));
    }

    public List<GrabDspOrderDriverIndexPO> querySubmitIndex(String dspOrderId, Date submitTime) throws SQLException {
        if (StringUtils.isBlank(dspOrderId) || Objects.isNull(submitTime)) {
            return Lists.newArrayList();
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.equal("dsp_order_id", dspOrderId, Types.VARCHAR, false);
        builder.and();
        builder.equal("is_valid", 1, Types.INTEGER, false);
        builder.and();
        builder.equal("is_submit", 1, Types.INTEGER, false);
        builder.and();
        builder.greaterThan("submit_time_bj", submitTime, Types.TIMESTAMP, false);
        return client.query(builder, DalHints.createIfAbsent(null).inAllShards().sequentialExecute());
    }
}
