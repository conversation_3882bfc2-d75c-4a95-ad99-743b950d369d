package com.ctrip.dcs.infrastructure.service;

import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.MetricsTagConstants;
import com.ctrip.dcs.domain.common.enums.DspStage;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.OrderPreConfirmDTO;
import com.ctrip.dcs.domain.schedule.check.CheckChain;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.factory.CheckChainFactory;
import com.ctrip.dcs.domain.schedule.factory.SortFactory;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.Sorter;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.dianping.cat.Cat;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 运力推荐服务
 * 运力筛选与排序
 * <AUTHOR>
 */
@Component
public class RecommendServiceImpl implements RecommendService {
    private static final Logger logger = LoggerFactory.getLogger(RecommendServiceImpl.class);

    private static final String RESURGENCE_EVENT_KEY = "dcs.preconfirm.resurgence";

    @Autowired
    private DspContextService dspContextService;

    @Autowired
    private CheckChainFactory checkFactory;

    @Autowired
    private SortFactory sortFactory;

    @Autowired
    @Qualifier("checkConfig")
    private ConfigService checkConfig;

    @Autowired
    @Qualifier("sortConfig")
    private ConfigService sortConfig;

    @Autowired
    private CheckServiceImpl checkService;

    @Override
    public List<SortModel> recommend(DspOrderVO order, SubSkuVO subSku, DuidVO duid) {
        // 派单上下文
        DspContext context = new DspContext(dspContextService);
        List<CheckModel> checkModels = check(order, subSku, duid, context);
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            return sort(order, subSku, duid, context, checkModels);
        } finally {
            //增加排序链埋点
            Integer subSkuId = Optional.ofNullable(subSku.getSubSkuId()).orElse(0);
            Map<String, String> tags = Maps.newHashMap();
            tags.put(MetricsTagConstants.SUB_SKU_ID, subSkuId.toString());
            MetricsUtil.recordTime(MetricsConstants.PROCESS_SORT_CHAIN_TIME_KEY, stopwatch.elapsed(TimeUnit.MILLISECONDS), tags);
        }
    }


    /**
     * 执行检查项
     * @param order
     * @param subSku
     * @param duid
     * @param context
     * @return
     */
    private List<CheckModel> check(DspOrderVO order, SubSkuVO subSku, DuidVO duid, DspContext context) {
        // 创建检查链
        CheckChain checkChain = checkFactory.create(subSku.getCheck(), DspStage.DSP);
        // 检查上下文
        CheckContext checkContext = CheckContext.builder()
                .dspOrder(order)
                .subSku(subSku)
                .configService(checkConfig)
                .dspStage(DspStage.DSP)
                .context(context)
                .duid(duid)
                .build();
        Stopwatch stopwatch = Stopwatch.createStarted();
        // 派单检查
        List<CheckModel> check = checkChain.check(checkContext);
        // 埋点
        checkService.metrics(order, subSku, check, stopwatch);
        return check;
    }

    /**
     * 排序
     * @param order
     * @param subSku
     * @param duid
     * @param context
     * @param checkModels
     * @return
     */
    public List<SortModel> sort(DspOrderVO order, SubSkuVO subSku, DuidVO duid, DspContext context, List<CheckModel> checkModels) {
        List<SortModel> sortModels = checkModels.stream()
                .filter(Objects::nonNull)
                .filter(CheckModel::isPass)
                .map(m -> new SortModel(m.getModel()))
                .collect(Collectors.toList());
        logger.info("RecommendService_sort", "sort size: : " + sortModels.size(), Map.of("dspOrderId", Optional.ofNullable(order).map(DspOrderVO::getDspOrderId).orElse("")));
        // 特殊逻辑：为避免延后派预确认订单变成确认后无车，需要对调度运力组召回结果进行处理：如果全部可用为空则根据预确认结果选取一个运力组进单
        if (CollectionUtils.isEmpty(sortModels)) {
            logger.info("RecommendService_sort", "sortModels is empty, re-handling for pre-confirmed order.");
            sortModels = handlePreConfirmedOrder(order, subSku, checkModels);
        }
        //没有可派司机
        if (CollectionUtils.isEmpty(sortModels) || subSku.getSort() == null) {
            return Collections.emptyList();
        }
        // 创建排序
        Sorter sorter = sortFactory.create(subSku.getSort());
        // 排序
        sorter.sort(sortModels, new SortContext(order, subSku, duid, context, sortConfig));
        return sortModels;
    }

    /**
     * 为避免延后派预确认订单变成确认后无车，需要对运力组召回结果进行处理：如果全部可用为空则根据预确认结果选取一个运力组进单
     *
     * @param order  订单信息
     * @param subSku 派发子产品信息
     * @param checkModels 运力组检查结果列表
     * @return
     */
    private List<SortModel> handlePreConfirmedOrder(DspOrderVO order, SubSkuVO subSku, List<CheckModel> checkModels) {
        // 仅处理调度指派，其他不处理直接返回
        if (!DspType.DISPATCHER_ASSIGN.equals(subSku.getDspType())) {
            logger.info("RecommendService_handlePreConfirmedOrder", "the dspType is not DISPATCHER_ASSIGN, returning empty list");
            return new ArrayList<>();
        }

        // 非延后派立即确认订单，不处理直接返回
        if (!order.isPreConfirm()) {
            logger.info("RecommendService_handlePreConfirmedOrder", "the order is not pre-confirmed, returning empty list");
            return new ArrayList<>();
        }

        checkModels = checkModels.stream()
                .filter(item -> item != null && item.getModel() != null && item.getModel().getTransportGroup() != null)
                .filter(item -> !CheckCode.MOCK_ORDER_LIMIT.equals(item.getCheckCode()))
                .collect(Collectors.toList());
        if ( CollectionUtils.isEmpty(checkModels) ) {
            logger.info("RecommendService_handlePreConfirmedOrder_"+order.getDspOrderId(), "checkModels empty, returning empty list");
            return new ArrayList<>();
        }

        // 获取预确认检查通过的运力组信息
        OrderPreConfirmDTO orderPreConfirmResult = dspContextService.queryOrderPreConfirmDTO(order);
        if (orderPreConfirmResult == null || CollectionUtils.isEmpty(orderPreConfirmResult.getMatchedTransportGroupIds())) {
            logger.info("RecommendService_handlePreConfirmedOrder", "the orderPreConfirmResult is null or empty, returning empty list.");
            return new ArrayList<>();
        }

        logger.info("RecommendService_handlePreConfirmedOrder", "originalCheckModels : " + JacksonSerializer.INSTANCE().serialize(checkModels));

        // 优先复活匹配结果中进单数量不满足的运力组
        // 兜底 如果复活进单数量不满足的运力组后仍为空 则复活因rp过滤的运力组
        // 仍未空 最后复活因进单时间过滤的运力组
        List<Long> preMatchedTransportGroupIds = orderPreConfirmResult.getMatchedTransportGroupIds();

        List<SortModel> finalSortModels = Lists.newArrayList();
        for (CheckCode checkCode : getNoNeedCheckCodes()) {
            finalSortModels = reFilterModel(checkModels, checkCode, preMatchedTransportGroupIds);
            if (!CollectionUtils.isEmpty(finalSortModels)) {
                // 埋点 由于哪种检查项被复活
                Cat.logEvent(RESURGENCE_EVENT_KEY, String.format("checkCode:%s", checkCode.getCode()));
                logger.info("RecommendService_handlePreConfirmedOrder_" + order.getDspOrderId(), "reFilterModel by {}, finalSortModels: {}",
                        checkCode, JacksonSerializer.INSTANCE().serialize(finalSortModels));
                return finalSortModels;
            }
        }
        // 检查项均未复活成功 兜底
        if (CollectionUtils.isEmpty(finalSortModels)) {
            finalSortModels = checkModels.stream()
                    .map(m -> new SortModel(m.getModel()))
                    .collect(Collectors.toList());
            Cat.logEvent(RESURGENCE_EVENT_KEY, "checkCode:ALL_EXCEPT_MOCK_ORDER_LIMIT");
        }
        return finalSortModels;
    }

    private List<SortModel> reFilterModel(List<CheckModel> checkModels, CheckCode checkCode, List<Long> preMatchedTransportGroupIds) {
        return checkModels.stream()
                .filter(item -> item != null && item.getModel() != null && item.getModel().getTransportGroup() != null)
                .filter(item -> checkCode.equals(item.getCheckCode()) && preMatchedTransportGroupIds.contains(item.getModel().getTransportGroup().getTransportGroupId()))
                .map(m -> new SortModel(m.getModel()))
                .collect(Collectors.toList());
    }

    private List<CheckCode> getNoNeedCheckCodes() {
        List<CheckCode> noNeedCheckCodes = Lists.newArrayList();
        // 按顺序添加 影响最不大的放前面
        // I144 进单数量
        noNeedCheckCodes.add(CheckCode.OVER_TRANS_GROUP_ORDERS_NUM_LIMIT);
        // I107 RP
        noNeedCheckCodes.add(CheckCode.OVER_TRANS_GROUP_ORDERS_RATE_PLAN_ID_LIMIT);
        // I104 进单时间
        noNeedCheckCodes.add(CheckCode.OVER_TRANS_GROUP_TIME_LIMIT);
        return noNeedCheckCodes;
    }


}
