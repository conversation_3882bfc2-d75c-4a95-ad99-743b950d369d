package com.ctrip.dcs.infrastructure.repository;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.infrastructure.adapter.limiter.DspRateLimiter;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.dao.GrabDspOrderDriverIndexDao;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderDriverIndexPO;
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter;
import com.ctrip.dcs.infrastructure.common.converter.GrabDspOrderDriverIndexConvertor;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class GrabDspOrderDriverIndexRepositoryImpl implements GrabDspOrderDriverIndexRepository {

    private static final Logger logger = LoggerFactory.getLogger(GrabDspOrderDriverIndexRepositoryImpl.class);

    private static final String GRAB_DSP_ORDER_DRIVER_INDEX = "GrabDspOrderDriverIndex_Driver_Id_%s";

    private static final String GRAB_DSP_ORDER_DRIVER_INDEX_DETAIL = "GrabDspOrderDriverIndex_%s_%s";

    @Autowired
    private GrabDspOrderDriverIndexDao grabDspOrderDriverIndexDao;

    @Autowired
    private DspRateLimiter rateLimiter;

    @Autowired
    private TRocksProviderAdapter tRocksProviderAdapter;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Override
    public List<GrabDspOrderDriverIndexDO> query(String orderId) {
        try {
            List<GrabDspOrderDriverIndexPO> list = grabDspOrderDriverIndexDao.queryByDspOrderId(orderId);
            return GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesDO(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", "GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public List<GrabDspOrderDriverIndexDO> queryFomCache(String orderId) {
        try {
            List<GrabDspOrderDriverIndexPO> indexes = queryIndexFromCache(orderId);
            if (CollectionUtils.isNotEmpty(indexes)) {
                return GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesDO(indexes);
            }
            List<GrabDspOrderDriverIndexPO> list = grabDspOrderDriverIndexDao.queryByDspOrderId(orderId);
            saveIndexToCache(orderId, list);
            return GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesDO(list);
        } catch (SQLException e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", "GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public List<GrabDspOrderDriverIndexDO> query(String orderId, List<Long> driverIds) {
        try {
            List<GrabDspOrderDriverIndexPO> list = grabDspOrderDriverIndexDao.query(orderId, driverIds);
            return GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesDO(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", "GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public Long queryCountBroadcastPushTime(Date startTime, Date endTime) {
        try {
            return grabDspOrderDriverIndexDao.queryCountBroadcastPushTime(startTime, endTime);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", "GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public List<GrabDspOrderDriverIndexDO> queryBroadcastPushTime(Date startTime, Date endTime, Integer page, Integer size) {
        try {
            List<GrabDspOrderDriverIndexPO> list = grabDspOrderDriverIndexDao.queryBroadcastPushTime(startTime, endTime, page, size);
            return GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesDO(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", "GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public Long queryCountGrabPushTime(Date startTime, Date endTime) {
        try {
            return grabDspOrderDriverIndexDao.queryCountGrabPushTime(startTime, endTime);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public List<GrabDspOrderDriverIndexDO> queryGrabPushTime(Date startTime, Date endTime, Integer page, Integer size) {
        try {
            List<GrabDspOrderDriverIndexPO> list = grabDspOrderDriverIndexDao.queryGrabPushTime(startTime, endTime, page, size);
            return GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesDO(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public void save(List<GrabDspOrderDriverIndexDO> indexes) {
        List<GrabDspOrderDriverIndexPO> list = GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesPO(indexes);
        List<List<GrabDspOrderDriverIndexPO>> partition = Lists.partition(list, 200);
        for (List<GrabDspOrderDriverIndexPO> pos : partition) {
            try {
                double seconds = rateLimiter.acquire(DspRateLimiter.GRAB_DSP_ORDER_DRIVER_INDEX_RATE_LIMITER);
                logger.info("DspRateLimit_GrabDspOrderDriverIndex", "rate limit seconds: " + seconds);
                grabDspOrderDriverIndexDao.insert(pos);
                deleteIndexFromCache(pos);
            } catch (Exception e) {
                logger.error("GrabDspOrderDriverIndexRepositoryError", e);
            }
        }
    }

    @Override
    public void update(List<GrabDspOrderDriverIndexDO> indexes) {
        try {
            List<GrabDspOrderDriverIndexPO> list = GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesPO(indexes);
            batchUpdate(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public List<GrabDspOrderDriverIndexDO> query(Long driverId, String categoryCode) {
        try {
            String parentCategoryCode = Objects.equals(categoryCode, ParentCategoryEnum.DAY.getCode()) ? ParentCategoryEnum.DAY.getCode() : ParentCategoryEnum.JNT.getCode();
            List<GrabDspOrderDriverIndexPO> list = grabDspOrderDriverIndexDao.queryByDriverId(driverId, parentCategoryCode);
            return GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesDO(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public List<GrabDspOrderDriverIndexDO> query(Long driverId, Date beginTime, Date endTime, String categoryCode) {
        try {
            String parentCategoryCode = Objects.equals(categoryCode, ParentCategoryEnum.DAY.getCode()) ? ParentCategoryEnum.DAY.getCode() : ParentCategoryEnum.JNT.getCode();
            List<GrabDspOrderDriverIndexPO> list = grabDspOrderDriverIndexDao.queryByEstimatedUseTime(driverId, beginTime, endTime, parentCategoryCode);
            return GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesDO(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public List<GrabDspOrderDriverIndexDO> querySubmitIndex(String dspOrderId, Date submitTime) {
        try {
            List<GrabDspOrderDriverIndexPO> list = grabDspOrderDriverIndexDao.querySubmitIndex(dspOrderId, submitTime);
            return GrabDspOrderDriverIndexConvertor.toGrabDspOrderDriverIndexesDO(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public void updateBroadcastPushTime(List<GrabDspOrderDriverIndexDO> indexes) {
        try {
            List<GrabDspOrderDriverIndexPO> list = indexes.stream().map(index -> {
                GrabDspOrderDriverIndexPO po = new GrabDspOrderDriverIndexPO();
                po.setId(index.getId());
                po.setBroadcastPushTimeBj(Optional.ofNullable(index.getBroadcastPushTimeBj()).map(time -> new Timestamp(time.getTime())).orElse(null));
                po.setDriverId(index.getDriverId());
                return po;
            }).toList();

            batchUpdate(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    @Override
    public void updateSubmitDuid(List<GrabDspOrderDriverIndexDO> indexes) {
        try {
            List<GrabDspOrderDriverIndexPO> list = indexes.stream().map(index -> {
                GrabDspOrderDriverIndexPO po = new GrabDspOrderDriverIndexPO();
                po.setId(index.getId());
                po.setIsSubmit(index.getIsSubmit());
                po.setSubmitDuid(index.getSubmitDuid());
                po.setSubmitTimeBj(new Timestamp(index.getSubmitTimeBj().getTime()));
                po.setDriverId(index.getDriverId());
                return po;
            }).toList();

            batchUpdate(list);
        } catch (Exception e) {
            logger.error("GrabDspOrderDriverIndexRepositoryError", e);
            throw new BizException("GrabDspOrderDriverIndexRepositoryError");
        }
    }

    public void batchUpdate(List<GrabDspOrderDriverIndexPO> list) throws SQLException {
        List<List<GrabDspOrderDriverIndexPO>> partition = Lists.partition(list, 200);
        for (List<GrabDspOrderDriverIndexPO> pos : partition) {
            double seconds = rateLimiter.acquire(DspRateLimiter.GRAB_DSP_ORDER_DRIVER_INDEX_RATE_LIMITER);
            logger.info("DspRateLimit_GrabDspOrderDriverIndex", "rate limit seconds: " + seconds);
            grabDspOrderDriverIndexDao.update(pos);
        }
        deleteIndexFromCache(list);
    }

    public List<GrabDspOrderDriverIndexPO> queryIndexFromCache(String orderId) {
        try {
            String key = String.format(GRAB_DSP_ORDER_DRIVER_INDEX, orderId);
            String value = tRocksProviderAdapter.get(key);
            if (StringUtils.isBlank(value)) {
                return Collections.emptyList();
            }
            List<String> driverIds = Splitter.on(",").splitToList(value);
            List<String> keys = driverIds.stream().map(id -> String.format(GRAB_DSP_ORDER_DRIVER_INDEX_DETAIL, orderId, id)).distinct().toList();
            List<String> values = tRocksProviderAdapter.mget(keys);
            List<GrabDspOrderDriverIndexPO> result = Lists.newArrayList();
            for (String str : values) {
                if (StringUtils.isBlank(str)) {
                    continue;
                }
                GrabDspOrderDriverIndexPO po = JacksonSerializer.INSTANCE().deserialize(str, GrabDspOrderDriverIndexPO.class);
                result.add(po);
            }
            logger.info("QueryIndexFromCacheInfo_" + orderId, JacksonSerializer.INSTANCE().serialize(result));
            return result;
        } catch (Exception e) {
            logger.warn(e);
        }
        return Collections.emptyList();
    }

    public void saveIndexToCache(String orderId, List<GrabDspOrderDriverIndexPO> indexes) {
        try {
            Integer seconds = broadcastGrabConfig.getInteger("GRAB_DSP_ORDER_DRIVER_INDEX_EXPIRE_SECONDS", 60);
            String key = String.format(GRAB_DSP_ORDER_DRIVER_INDEX, orderId);
            List<Long> driverIds = indexes.stream().map(GrabDspOrderDriverIndexPO::getDriverId).distinct().toList();
            tRocksProviderAdapter.setex(key, Joiner.on(",").join(driverIds), seconds);
            List<Pair<String, String>> kvs = indexes.stream().map(index -> {
                String k = String.format(GRAB_DSP_ORDER_DRIVER_INDEX_DETAIL, orderId, index.getDriverId());
                String v = JacksonSerializer.INSTANCE().serialize(index);
                return Pair.of(k, v);
            }).toList();
            tRocksProviderAdapter.mset(kvs, seconds);
        } catch (Exception e) {
            logger.warn(e);
        }
    }

    public void deleteIndexFromCache(List<GrabDspOrderDriverIndexPO> list) {
        try {
            List<String> dspOrderIds = list.stream().map(GrabDspOrderDriverIndexPO::getDspOrderId).distinct().toList();
            for (String orderId : dspOrderIds) {
                String key = String.format(GRAB_DSP_ORDER_DRIVER_INDEX, orderId);
                tRocksProviderAdapter.del(key);
            }
            for (GrabDspOrderDriverIndexPO index : list) {
                String key = String.format(GRAB_DSP_ORDER_DRIVER_INDEX_DETAIL, index.getDspOrderId(), index.getDriverId());
                tRocksProviderAdapter.del(key);
            }
        } catch (Exception e) {
            logger.error("DeleteIndexFromCacheError", e);
        }
    }
}
