package com.ctrip.dcs.infrastructure.common.converter;

import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.infrastructure.adapter.mysql.database.po.GrabDspOrderDriverIndexPO;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class GrabDspOrderDriverIndexConvertor {

    public static List<GrabDspOrderDriverIndexDO> toGrabDspOrderDriverIndexesDO(List<GrabDspOrderDriverIndexPO> list) {
        return Optional.ofNullable(list)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(GrabDspOrderDriverIndexConvertor::toGrabDspOrderDriverIndexDO)
                .toList();
    }

    public static GrabDspOrderDriverIndexDO toGrabDspOrderDriverIndexDO(GrabDspOrderDriverIndexPO po) {
        GrabDspOrderDriverIndexDO index = new GrabDspOrderDriverIndexDO();
        index.setId(po.getId());
        index.setDspOrderId(po.getDspOrderId());
        index.setDriverId(po.getDriverId());
        index.setIsValid(po.getIsValid());
        index.setCityId(po.getCityId().longValue());
        index.setIsBroadcast(po.getIsBroadcast());
        index.setIsSubmit(po.getIsSubmit());
        index.setIsTaken(po.getIsTaken());
        index.setEstimatedUseTimeBj(po.getEstimatedUseTimeBj());
        index.setSubmitTimeBj(po.getSubmitTimeBj());
        index.setGrabPushTimeBj(po.getGrabPushTimeBj());
        index.setDuid(po.getDuid());
        index.setSubmitDuid(po.getSubmitDuid());
        index.setCategoryCode(ParentCategoryEnum.identify(Optional.of(po).map(GrabDspOrderDriverIndexPO::getCategoryCode).orElse(ParentCategoryEnum.JNT.getCode())));
        index.setBroadcastPushTimeBj(po.getBroadcastPushTimeBj());
        index.setDatachangeCreatetime(po.getDatachangeCreatetime());
        index.setDatachangeLasttime(po.getDatachangeLasttime());
        return index;
    }

    public static List<GrabDspOrderDriverIndexPO> toGrabDspOrderDriverIndexesPO(List<GrabDspOrderDriverIndexDO> indexes) {
        return Optional.ofNullable(indexes)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(GrabDspOrderDriverIndexConvertor::toGrabDspOrderDriverIndexPO)
                .toList();
    }

    public static GrabDspOrderDriverIndexPO toGrabDspOrderDriverIndexPO(GrabDspOrderDriverIndexDO index) {
        GrabDspOrderDriverIndexPO po = new GrabDspOrderDriverIndexPO();
        po.setId(index.getId());
        po.setDspOrderId(index.getDspOrderId());
        po.setDriverId(index.getDriverId());
        po.setIsValid(index.getIsValid());
        po.setCityId(index.getCityId().intValue());
        po.setIsBroadcast(index.getIsBroadcast());
        po.setIsSubmit(index.getIsSubmit());
        po.setIsTaken(index.getIsTaken());
        po.setDuid(index.getDuid());
        po.setCategoryCode(index.getCategoryCode().getCode());
        if (index.getBroadcastPushTimeBj() != null) {
            po.setBroadcastPushTimeBj(new Timestamp(index.getBroadcastPushTimeBj().getTime()));
        }
        if (index.getEstimatedUseTimeBj() != null) {
            po.setEstimatedUseTimeBj(new Timestamp(index.getEstimatedUseTimeBj().getTime()));
        }
        if (index.getSubmitTimeBj() != null) {
            po.setSubmitTimeBj(new Timestamp(index.getSubmitTimeBj().getTime()));
        }
        if (index.getGrabPushTimeBj() != null) {
            po.setGrabPushTimeBj(new Timestamp(index.getGrabPushTimeBj().getTime()));
        }
        if (index.getSubmitDuid() != null) {
            po.setSubmitDuid(index.getSubmitDuid());
        }
        return po;
    }
}
