package com.ctrip.dcs.infrastructure.repository;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.dsporder.value.TakenType;
import com.ctrip.dcs.domain.schedule.check.CheckConfig;
import com.ctrip.dcs.domain.schedule.check.source.CheckSourceConfig;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.sort.SortConfig;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.domain.schedule.value.carconfig.*;
import com.ctrip.dcs.infrastructure.adapter.carconfig.CheckCacheConfig;
import com.ctrip.dcs.infrastructure.adapter.carconfig.CheckChainConfig;
import com.ctrip.dcs.infrastructure.adapter.carconfig.SortChainConfig;
import com.ctrip.dcs.infrastructure.adapter.carconfig.SubSkuConfig;
import com.ctrip.dcs.infrastructure.adapter.qconfig.BaseConfigService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class SubSkuRepositoryImpl implements SubSkuRepository {

    private static final Logger logger = LoggerFactory.getLogger(ScheduleStrategyRepositoryImpl.class);

    @Autowired
    private SubSkuConfig subSkuConfig;

    @Autowired
    private CheckChainConfig checkChainConfig;

    @Autowired
    private CheckCacheConfig checkCacheConfig;

    @Autowired
    private SortChainConfig sortChainConfig;

    @Autowired
    @Qualifier("commonConfConfig")
    private BaseConfigService configService;

    @Override
    public SubSkuVO find(Integer subSkuId) {
        SubSkuValueVO vo = subSkuConfig.get(new SubSkuKeyVO(subSkuId));
        if (Objects.isNull(vo)) {
            logger.warn("SubSkuRepositoryWarn", "sub sku is empty. subSkuId:{}", subSkuId);
            MetricsUtil.recordValue(MetricsConstants.SUB_SKU_NULL_COUNT);
            return null;
        }
        return SubSkuVO.builder()
                .subSkuId(subSkuId)
                .subSkuName(vo.getSubSkuName())
                .dspType(DspType.of(vo.getDspType()))
                .takenType(TakenType.of(vo.getTakenType()))
                .retrySecond(vo.getRetry())
                .check(getCheckConfig(vo))
                .sort(getSortConfig(vo))
                .roundRetryFactor(getSubSkuRoundRetryFactor())
                .build();
    }

    private List<Pair<Integer, Double>> getSubSkuRoundRetryFactor() {
        String factor = configService.getString("sub_sku_round_retry_factor");
        if (StringUtils.isBlank(factor)) {
            return Collections.emptyList();
        }
        List<String> list = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(factor);
        return list.stream().map(item -> {
            List<String> kv = Splitter.on(":").trimResults().omitEmptyStrings().splitToList(item);
            return Pair.of(Integer.valueOf(kv.get(0)), Double.valueOf(kv.get(1)));
        }).sorted((p1, p2) -> p2.getKey().compareTo(p1.getKey())).toList();
    }

    private CheckConfig getCheckConfig(SubSkuValueVO subSkuValueVO) {
        CheckChainValueVO checkChainValueVO = checkChainConfig.get(new CheckChainKeyVO(subSkuValueVO.getCheckChainId()));
        Map<String, Integer> checkCacheValue = checkCacheConfig.get();
        return checkChainValueVO == null ? null : CheckConfig.builder()
                .checkId(subSkuValueVO.getCheckChainId())
                .checkSourceId(checkChainValueVO.getCheckSourceId())
                .checkSourceConfig(JsonUtil.fromJson(checkChainValueVO.getCheckSourceConfig(), CheckSourceConfig.class))
                .dspCheckItem(getCheckList(checkChainValueVO.getDspCheck()))
                .takenCheckItem(getCheckList(checkChainValueVO.getTakenCheck()))
                .cacheConfig(checkCacheValue)
                .build();
    }

    private SortConfig getSortConfig(SubSkuValueVO subSkuValueVO) {
        SortValueVO sortValueVO = sortChainConfig.get(new SortKeyVO(subSkuValueVO.getSortChainId()));
        return sortValueVO == null ? null : SortConfig.builder()
                .sortId(subSkuValueVO.getSortChainId())
                .score(sortValueVO.getScore())
                .features(getFeatureList(sortValueVO.getFeatures()))
                .build();
    }

    private List<String> getCheckList(String config) {
        return Splitter.on("_").omitEmptyStrings().trimResults().splitToList(config);
    }

    public Map<String, Double> getFeatureList(String config) {
        Map<String, Double> result = Maps.newHashMap();
        List<String> list = Splitter.on("_").omitEmptyStrings().trimResults().splitToList(config);
        for (String item : list) {
            List<String> kv = Splitter.on(":").trimResults().omitEmptyStrings().splitToList(item);
            result.put(kv.get(0), Double.valueOf(kv.get(1)));
        }
        return result;
    }
}
