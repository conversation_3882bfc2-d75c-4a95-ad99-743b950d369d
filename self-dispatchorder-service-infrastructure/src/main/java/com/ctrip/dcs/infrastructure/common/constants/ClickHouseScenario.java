package com.ctrip.dcs.infrastructure.common.constants;

/**
 * <AUTHOR>
 */
public interface ClickHouseScenario {

    // todo 申请scenario
    String SCHEDULE_TASK_RECORD = "dcs-self-dsp-schedule-task-record";

    String VBK_ORDER_OPERATION_RECORD = "dcs-dispatcher-order-operation-record";

    String SELF_TAKEN_CHECK_ITEM_RECORD = "dcs-self-dsp-check-item-record";

    String SELF_TAKEN_SORT_RECORD = "dcs-self-dsp-sort-newrecord";

    String SUPPLIER_ORDER_DIVERSION_RECORD = "dcs-order-diversion";

    //TODO 上线前需要申请线上scenario
    String ASSIGN_RES_LOG_ = "dcs-self-assign-res-log";
}
