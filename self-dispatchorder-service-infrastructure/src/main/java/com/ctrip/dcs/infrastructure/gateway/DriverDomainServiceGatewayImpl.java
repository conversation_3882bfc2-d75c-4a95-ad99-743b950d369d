package com.ctrip.dcs.infrastructure.gateway;

import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.RightsEnum;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.domain.common.value.BaseDetailVO;
import com.ctrip.dcs.domain.common.value.DriverOrderLocationVO;
import com.ctrip.dcs.domain.dsporder.value.DriverLeaveRightsVO;
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway;
import com.ctrip.dcs.domain.schedule.value.DriverLeaveRightsVo;
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO;
import com.ctrip.dcs.domain.schedule.value.DriverRightsVO;
import com.ctrip.dcs.driver.domain.rights.CheckRightsRequestType;
import com.ctrip.dcs.driver.domain.rights.CheckRightsResponseType;
import com.ctrip.dcs.driver.domain.rights.QueryRightsAndLevelResponseType;
import com.ctrip.dcs.driver.domain.rights.UseRightsResponseType;
import com.ctrip.dcs.driver.domain.setting.DriverPushConfigInfoDTO;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPushConfigRequestType;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPushConfigResponseType;
import com.ctrip.dcs.infrastructure.adapter.soa.DriverDomainServiceProxy;
import com.ctrip.dcs.infrastructure.adapter.soa.TmsTransportServiceProxy;
import com.ctrip.dcs.infrastructure.common.annotation.Retryable;
import com.ctrip.dcs.infrastructure.common.converter.DriverDomainConvertor;
import com.ctrip.dcs.infrastructure.common.util.CacheUtil;
import com.ctrip.dcs.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOAResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.util.concurrent.ListenableFuture;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Component
public class DriverDomainServiceGatewayImpl implements DriverDomainServiceGateway {

    private static final Logger logger = LoggerFactory.getLogger(DriverDomainServiceGatewayImpl.class);

    @Autowired
    private DriverDomainServiceProxy proxy;

    @Autowired
    private CacheUtil cacheUtil;

    private final String DRIVER_PUSH_CONFIG_CACHE_KEY_PREFIX = "pushConfig-";

    @Override
    public List<DriverPushConfigVO> query(List<Long> driverIdList) {

        List<String> driverIdKeyList = driverIdList.stream()
                .map(driverId -> DRIVER_PUSH_CONFIG_CACHE_KEY_PREFIX + driverId)
                .collect(Collectors.toList());

        List<DriverPushConfigVO> cacheRes = cacheUtil.mGet(driverIdKeyList);

        List<DriverPushConfigVO> result = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(cacheRes)) {
            if (cacheRes.size() == driverIdList.size()) {
                return cacheRes;
            }
            result.addAll(cacheRes);
            driverIdList = driverIdList.stream().filter(a -> result.stream().noneMatch(b -> b.getDriverId().longValue() == a.longValue())).collect(Collectors.toList());
        }

        //分批查询
        List<List<Long>> driverIds = LocalCollectionUtils.partition(driverIdList,100);
        List<ListenableFuture<QueryDriverPushConfigResponseType>> resultFutureList = new ArrayList<>();
        for (List<Long> driverId : driverIds) {
            QueryDriverPushConfigRequestType request = new QueryDriverPushConfigRequestType();
            request.setDriverIdList(driverId);
            resultFutureList.add(proxy.queryDriverPushConfigAsync(request));
        }

        for (ListenableFuture<QueryDriverPushConfigResponseType> future : resultFutureList) {
            result.addAll(getResultList(future));
        }

        if (CollectionUtils.isNotEmpty(result)) {
            driverIdKeyList = result.stream().map(r -> DRIVER_PUSH_CONFIG_CACHE_KEY_PREFIX + r.getDriverId()).collect(Collectors.toList());
            cacheUtil.mSet(3, driverIdKeyList, result);
        }
        logger.info("QueryDriverPushConfigInfo", JacksonUtil.serialize(result));
        return result;
    }

    @Override
    @Retryable // fixme 重试是否生效质疑
    public Boolean queryDriverDispatchRights(Long driverId) {
        CheckRightsResponseType response = proxy.checkRights(DriverDomainConvertor.buildCheckRightsReq(driverId, RightsEnum.RESEND));
        if (!ResponseResultUtil.checkResponseResult(response) || response.isCanUse() == null) {
            // todo 日志，打点
            throw ErrorCode.DISPATCH_DRIVER_RIGHT_ERROR.getBizException();
        }
        return response.isCanUse();
    }

    @Override
    @Retryable // fixme 重试是否生效质疑
    public Boolean useDriverDispatchRight(Long driverId, BaseDetailVO detailVO) {
        UseRightsResponseType response = proxy.useRights(DriverDomainConvertor.buildUseRightsReq(driverId,detailVO));
        if (!ResponseResultUtil.checkResponseResult(response) ) {
            throw ErrorCode.DISPATCH_DRIVER_RIGHT_ERROR.getBizException();
        }
        return true;
    }

    @Override
    @Retryable // fixme 重试是否生效质疑
    public DriverRightsVO queryDriverRightsInfo(Long driverId) {
        QueryRightsAndLevelResponseType response = proxy.queryRightsAndLevel(DriverDomainConvertor.buildQueryRightsReq(driverId));
        if (!ResponseResultUtil.checkResponseResult(response) || CollectionUtils.isEmpty(response.getRights())) {
            // todo 日志，打点
            throw ErrorCode.DISPATCH_DRIVER_RIGHT_ERROR.getBizException();
        }
        DriverRightsVO res = DriverDomainConvertor.buildDriverRightsVO(response.getRights().get(0));
        if (res.getReassignUsedByWeek() == null || res.getUseCount() == null || res.getUseLimit() == null) {
            // todo 日志，打点
            throw ErrorCode.DISPATCH_DRIVER_RIGHT_ERROR.getBizException();
        }
        return res;
    }

    @Override
    public DriverLeaveRightsVO queryDriverLeaveRight(Long driverId) {
        CheckRightsRequestType req = DriverDomainConvertor.buildCheckRightsReq(driverId, RightsEnum.LEAVE);
        CheckRightsResponseType response = proxy.checkRights(req);
        if (response == null) {
            response = proxy.checkRights(req);
        }
        Boolean canUse = Boolean.FALSE;
        Integer vacationLimit = 0;
        if (ResponseResultUtil.checkResponseResult(response)) {
            // 可以使用权益
            if (response.isCanUse()) {
                canUse = response.isCanUse();
                vacationLimit = response.getVacationLimit();
            }
        } else {
            // fixme 打点

            /**
             * 查询请假权益服务异常
             * 兜底：产品定义-请求请假权益异常 不能使用权益: 按有责进行改派处理 by <EMAIL>
             */
            logger.error("QueryDriverLeaveRightsError", "driverId:{}", ImmutableMap.of("driverId", String.valueOf(driverId)), driverId);
        }
        return new DriverLeaveRightsVO(driverId, canUse, vacationLimit);
    }

    /**
     * 获取异步查询结果
     * @param future
     * @return
     */
    private List<DriverPushConfigVO> getResultList(ListenableFuture<QueryDriverPushConfigResponseType> future){
        try{
            QueryDriverPushConfigResponseType responseType = future.get(1000, TimeUnit.MILLISECONDS);
            if (responseType == null || responseType.getResponseResult() == null || !responseType.getResponseResult().isSuccess()) {
                return new ArrayList<>();
            }
            if(LocalCollectionUtils.isEmpty(responseType.getPushInfoListList())){
                return new ArrayList<>();
            }
            return getResult(responseType.getPushInfoListList());
        }catch (Exception e){
            return new ArrayList<>();
        }
    }
    /**
     * 封装返回数据
     * @param configInfoDTOS
     * @return
     */
    private List<DriverPushConfigVO> getResult(List<DriverPushConfigInfoDTO> configInfoDTOS){
        List<DriverPushConfigVO> result = new ArrayList<>();
        for (DriverPushConfigInfoDTO configInfoDTO : configInfoDTOS) {
            result.add(getDriverPushConfigVO(configInfoDTO));
        }
        return result;
    }

    /**
     * 封装返回数据
     * @param configInfoDTO
     * @return
     */
    private DriverPushConfigVO getDriverPushConfigVO(DriverPushConfigInfoDTO configInfoDTO){
        DriverPushConfigVO configVO = new DriverPushConfigVO();
        configVO.setDriverId(configInfoDTO.getDriverId());
        configVO.setServiceTimeFrom(configInfoDTO.getServiceTimeFrom());
        configVO.setServiceTimeTo(configInfoDTO.getServiceTimeTo());
        configVO.setOrderTypes(configInfoDTO.getOrderTyps());
        configVO.setOrderPushStatus(configInfoDTO.isOrderPushStatus());
        configVO.setDrvOrderDistance(configInfoDTO.getDrvOrderDistance());
        configVO.setDrvIntendVehicleTypes(configInfoDTO.getDrvIntendVehicleTypes());
        return configVO;
    }

}