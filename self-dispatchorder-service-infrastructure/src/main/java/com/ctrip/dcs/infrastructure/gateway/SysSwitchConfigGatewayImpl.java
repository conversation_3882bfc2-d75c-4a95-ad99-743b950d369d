package com.ctrip.dcs.infrastructure.gateway;

import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway;
import com.ctrip.dcs.infrastructure.common.config.SysSwitchConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/4/28 14:29
 */
@Component
public class SysSwitchConfigGatewayImpl implements SysSwitchConfigGateway {
    
    @Resource
    SysSwitchConfig switchConfig;
    
    
    @Override
    public boolean getDriverQmqAddUcsSwitch() {
        return switchConfig.getDriverQmqAddUcsSwitch();
    }
    
    @Override
    public boolean getDspConfirmRecordNotWriteDriverInfoSwitch() {
        return switchConfig.getDspConfirmRecordNotWriteDriverInfoSwitch();
    }
}
