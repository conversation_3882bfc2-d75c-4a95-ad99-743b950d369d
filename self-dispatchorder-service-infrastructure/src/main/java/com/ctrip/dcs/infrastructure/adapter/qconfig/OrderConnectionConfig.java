package com.ctrip.dcs.infrastructure.adapter.qconfig;

import com.ctrip.igt.framework.common.base.GsonUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.Feature;
import qunar.tc.qconfig.client.TypedConfig;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;

@Component
public class OrderConnectionConfig {

    TypedConfig<FileStruct> file = TypedConfig.get("order.connection.sort.json", Feature.create().build(), e -> GsonUtil.fromJson(e, FileStruct.class));

    public double getDurationScore(Long cityId, Long durationMinutes) {
        return Optional.ofNullable(durationMinutes)
                .map(getCityScore(cityId))
                .orElse(getGlobalScore(durationMinutes));
    }

    protected Double getGlobalScore(Long durationMinutes) {
        return Optional.ofNullable(file)
                .map(TypedConfig::current)
                .map(FileStruct::getGlobalConfig)
                .map(GlobalStruct::getRange)
                .orElse(Collections.emptyList())
                .stream()
                .filter(checkRange(durationMinutes))
                .findFirst()
                .map(RangeStruct::getScore)
                .orElse(0.0);
    }

    protected Function<Long, Double> getCityScore(Long cityId) {
        return e -> Optional.ofNullable(file)
                .map(TypedConfig::current)
                .map(FileStruct::getCityConfig)
                .map(CityStruct::getList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(checkCity(cityId))
                .findFirst()
                .map(CityItem::getRange)
                .orElse(Collections.emptyList())
                .stream()
                .filter(checkRange(e))
                .findFirst()
                .map(RangeStruct::getScore)
                .orElse(null);
    }

    protected Predicate<CityItem> checkCity(Long cityId) {
        return e -> Objects.equals(e.getCityId(), cityId);
    }

    // 左闭右开
    protected Predicate<RangeStruct> checkRange(Long durationMinutes) {
        return e -> e.getLeft() <= durationMinutes && e.getRight() > durationMinutes;
    }

    @Getter
    @Setter
    protected class FileStruct {
        GlobalStruct globalConfig;
        CityStruct cityConfig;
    }

    @Getter
    @Setter
    protected class GlobalStruct {
        List<RangeStruct> range;
    }

    @Getter
    @Setter
    protected class CityStruct {
        List<CityItem> list;
    }

    @Getter
    @Setter
    protected class CityItem{
        Long cityId;
        List<RangeStruct> range;
    }

    @Getter
    @Setter
    protected class RangeStruct {
        Long left;
        Long right;
        Double score;
    }

}