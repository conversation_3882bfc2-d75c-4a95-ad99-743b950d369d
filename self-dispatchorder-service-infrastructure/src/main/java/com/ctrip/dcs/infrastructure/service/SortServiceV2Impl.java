package com.ctrip.dcs.infrastructure.service;

import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.check.command.DspSortCommand;
import com.ctrip.dcs.domain.schedule.context.DspContext;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.service.SortService;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.HierarchicalSorterV2;
import com.ctrip.dcs.domain.schedule.sort.v2.factory.SortFactoryV2;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 排序服务V2实现
 * 支持新旧排序系统的切换和A/B测试
 * 
 * <AUTHOR> Assistant
 */
@Component
public class SortServiceV2Impl implements SortService {
    
    private static final Logger logger = LoggerFactory.getLogger(SortServiceV2Impl.class);
    
    @Autowired
    private DspContextService dspContextService;
    
    @Autowired
    @Qualifier("sortConfig")
    private ConfigService sortConfig;
    
    // 原有的排序服务，用于降级
    @Autowired
    private SortServiceImpl fallbackSortService;
    
    private SortFactoryV2 sortFactoryV2;
    
    @Autowired
    public void init() {
        this.sortFactoryV2 = new SortFactoryV2(sortConfig);
    }
    
    @Override
    public List<SortModel> sort(DspSortCommand command) {
        // 检查是否启用V2排序系统
        if (!sortFactoryV2.isV2Enabled()) {
            logger.info("V2排序系统未启用，使用原有排序系统");
            return fallbackSortService.sort(command);
        }
        
        try {
            return sortWithV2(command);
        } catch (Exception e) {
            logger.error("V2排序系统执行失败，降级到原有排序系统", e);
            return fallbackSortService.sort(command);
        }
    }
    
    /**
     * 使用V2排序系统进行排序
     * 
     * @param command 排序命令
     * @return 排序结果
     */
    private List<SortModel> sortWithV2(DspSortCommand command) {
        // 转换为SortModel列表
        List<SortModel> sortModels = command.getDrivers().stream()
                .filter(Objects::nonNull)
                .map(driver -> new SortModel(new DspModelVO(command.getDspOrder(), driver)))
                .collect(Collectors.toList());
        
        logger.info("使用V2排序系统", "司机数量=" + sortModels.size() + 
                   ", 订单ID=" + command.getDspOrder().getDspOrderId());
        
        // 创建排序上下文
        SortContext context = new SortContext(
            command.getDspOrder(), 
            command.getSubSku(), 
            new DspContext(dspContextService), 
            sortConfig
        );
        
        // 创建分层排序器
        HierarchicalSorterV2 sorter = sortFactoryV2.createHierarchicalSorter();
        
        // 执行排序
        List<SortModel> sortedModels = sorter.sort(sortModels, context);
        
        logger.info("V2排序完成", "排序后司机数量=" + sortedModels.size());
        
        return sortedModels;
    }
    
    @Override
    public List<DriverVO> sort(DspOrderVO order, SubSkuVO subSku, List<DriverVO> drivers) {
        List<SortModel> sortModels = sort(new DspSortCommand(order, subSku, drivers));
        return sortModels.stream()
                .map(SortModel::getModel)
                .map(DspModelVO::getDriver)
                .collect(Collectors.toList());
    }
    
    /**
     * 检查V2系统是否可用
     * 
     * @return 是否可用
     */
    public boolean isV2Available() {
        try {
            return sortFactoryV2 != null && sortFactoryV2.isV2Enabled();
        } catch (Exception e) {
            logger.warn("检查V2系统可用性失败", e);
            return false;
        }
    }
    
    /**
     * 获取当前使用的排序系统版本
     * 
     * @return 版本信息
     */
    public String getCurrentVersion() {
        return isV2Available() ? "V2" : "V1";
    }
}
