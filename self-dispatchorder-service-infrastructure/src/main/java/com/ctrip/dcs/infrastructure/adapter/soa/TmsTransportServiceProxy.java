package com.ctrip.dcs.infrastructure.adapter.soa;

import com.ctrip.dcs.tms.transport.api.TmsTransportServiceClient;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOAResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.google.common.util.concurrent.ListenableFuture;

/**
 * <AUTHOR>
 */
@ServiceClient(value = TmsTransportServiceClient.class, format = "json")
public interface TmsTransportServiceProxy {

    DriverInfoSOAResponseType queryDriver(DriverInfoSOARequestType request);

    ListenableFuture<DriverInfoSOAResponseType> queryDriverAsync(DriverInfoSOARequestType request);

    QueryTransportGroupsSOAResponseType queryTransportGroups(QueryTransportGroupsSOARequestType request);

    QueryDrvLeaveDetailForDspSOAResponseType queryDrvLeaveDetailForDsp(QueryDrvLeaveDetailForDspSOARequestType requestType);

    QueryTransportKanbanForDspSOAResponseType queryTransportKanbanForDsp(QueryTransportKanbanForDspSOARequestType requestType);

    QueryTransportGroupDetailSOAResponseType queryTransportGroupDetail(QueryTransportGroupDetailSOARequestType request);

    VehicleDetailSOAResponseType queryVehicleDetail(VehicleDetailSOARequestType request);

    TmsDrvFreezeAddSOAResponseType addTmsDrvFreeze(TmsDrvFreezeAddSOARequestType request);

    //查询运力组支持的语言和主电话号或者备用电话号
    QueryTransportInformResponseType queryTransportInform(QueryTransportInformRequestType var1);

    ListenableFuture<QueryDrvIdByTransportGroupsResponseType> queryDrvIdByTransportGroupsAsync(QueryDrvIdByTransportGroupsRequestType request);

    QueryDrvDetailSOAResponseType queryDrvDetail(QueryDrvDetailSOARequestType request);

    /**
     * 查询车辆信息
     * @param request
     * @return
     */
    QueryVehicleBaseSOAResponseType queryVehicleListBySupplierId(QueryVehicleBaseSOARequestType request);

    QueryTransportGroupListByIdListSOAResponseType queryTransportGroupListByIdList(QueryTransportGroupListByIdListSOARequestType request);
    
    ListenableFuture<QueryTransportGroupListByIdListSOAResponseType> queryTransportGroupListByIdListAsync(QueryTransportGroupListByIdListSOARequestType request);
    
    QueryTransportGroupsForDspResponseType queryTransportGroupsForDsp(QueryTransportGroupsForDspRequestType request);
}