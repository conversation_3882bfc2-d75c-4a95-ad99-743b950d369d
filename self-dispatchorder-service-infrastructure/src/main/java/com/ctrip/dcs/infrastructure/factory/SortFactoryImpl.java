package com.ctrip.dcs.infrastructure.factory;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.schedule.factory.SortFactory;
import com.ctrip.dcs.domain.schedule.sort.SortConfig;
import com.ctrip.dcs.domain.schedule.sort.Sorter;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.feature.FeatureItem;
import com.ctrip.dcs.domain.schedule.sort.score.impl.WeightScorer;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Maps;
import org.reflections.Reflections;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Modifier;
import java.util.Map;
import java.util.Set;

/**
 * 打分器工厂类
 * <AUTHOR>
 */
@Component
public class SortFactoryImpl implements SortFactory, InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(SortFactoryImpl.class);

    private static final Reflections reflections = new Reflections("com.ctrip.dcs.domain.schedule.sort");

    /**
     * 特征容器
     * key:feature id
     * value:feature class
     */
    private static final Map<String, Class<? extends Feature>> featureMap = Maps.newHashMap();

    @Override
    public void afterPropertiesSet() throws Exception {
        load();
    }

    /**
     * 创建打分器
     * @param config
     * @return
     */
    @Override
    public Sorter create(SortConfig config) {
        Assert.notNull(config);
        // 创建排序器
        Sorter scorer = new Sorter(new WeightScorer(config.getFeatures()));
        try {
            // 加载特征项
            for (String featureId : config.getFeatures().keySet()) {
                Class<? extends Feature> clazz = featureMap.get(featureId);
                Feature feature = clazz.getDeclaredConstructor().newInstance();
                scorer.addFeature(feature);
            }
        } catch (Exception e) {
            logger.error(e);
            throw ErrorCode.BUILD_SCORE_ERROR.getBizException();
        }
        return scorer;
    }

    private static void load() {
        Set<Class<? extends Feature>> subTypes = reflections.getSubTypesOf(Feature.class);
        for (Class<? extends Feature> clazz : subTypes) {
            boolean isAbstract = Modifier.isAbstract(clazz.getModifiers());
            if (isAbstract) {
                continue;
            }
            FeatureItem annotation = clazz.getAnnotation(FeatureItem.class);
            featureMap.put(annotation.id().name(), clazz);
        }
    }

}
