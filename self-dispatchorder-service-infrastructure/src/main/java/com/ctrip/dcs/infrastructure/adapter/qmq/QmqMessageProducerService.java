package com.ctrip.dcs.infrastructure.adapter.qmq;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.value.MessageEventVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.qmq.producer.ProducerProviderSingleton;
import com.ctrip.igt.framework.qmq.producer.PropertiesMapCopier;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.producer.MessageProducerProvider;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class QmqMessageProducerService implements MessageProviderService {

    private static final Logger logger = LoggerFactory.getLogger(QmqMessageProducerService.class);

    @Autowired
    private MessageProducerProvider messageProducerProvider;

    @Override
    public void send(MessageEventVO event) {
        Assert.notNull(event);
        MapMessageEventDecorator decorator = new MapMessageEventDecorator(event);
        String topic = decorator.getTopic();
        long delay = decorator.getDelay();
        Map<String, String> data = decorator.toMap();
        Message message = messageProducerProvider.generateMessage(topic);
        data.forEach((k, v) -> PropertiesMapCopier.copy(message, k, v));
        if (delay > 0) {
            message.setDelayTime(delay, TimeUnit.MILLISECONDS);
        }
        messageProducerProvider.sendMessage(message);
    }

    @Override
    public void sendMessage(String subject, Map<String, Object> data) {
        sendDelayMessage(subject, data, 0);
    }

    @Override
    public void sendDelayMessage(String subject, Map<String, Object> data, long delay) {
        try {
            MessageProducerProvider producerProvider = ProducerProviderSingleton.getInstance();
            Message message = producerProvider.generateMessage(subject);
            data.forEach((k, v) -> PropertiesMapCopier.copy(message, k, v));
            if (delay > 0) {
                message.setDelayTime(delay, TimeUnit.MILLISECONDS);
            }
            logger.info("SendDelayMessage", "subject is {}, messageId is {},data is {}, delay is {}",
                    subject, message.getMessageId(), JacksonSerializer.INSTANCE().serialize(data), delay);
            producerProvider.sendMessage(message);
        } catch (Exception e) {
            logger.error("send_message_error", e);
        }
    }

    @Override
    public void sendMessage(String subject, Map<String, Object> data, Set<String> tags) {
        try {
            MessageProducerProvider producerProvider = ProducerProviderSingleton.getInstance();
            Message message = producerProvider.generateMessage(subject);
            data.forEach((k, v) -> PropertiesMapCopier.copy(message, k, v));
            if (CollectionUtils.isNotEmpty(tags)) {
                tags.forEach(message::addTag);
            }
            logger.info("qmq producer send message", "subject is {}, messageId is {},data is {}",
                    subject, message.getMessageId(), JsonUtils.toJson(data));
            producerProvider.sendMessage(message);
        } catch (Exception e) {
            logger.error("send_message_error",e);
        }
    }
}
