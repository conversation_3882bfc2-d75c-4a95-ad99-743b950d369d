package com.ctrip.dcs.domain.schedule.sort.v2;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.category.*;
import com.ctrip.dcs.domain.schedule.sort.v2.scorer.CategoryBasedScorer;
import com.ctrip.dcs.domain.schedule.sort.v2.weight.CategoryWeight;
import com.ctrip.dcs.domain.schedule.sort.v2.weight.DynamicWeightManager;
import com.ctrip.dcs.domain.schedule.sort.v2.weight.WeightStrategy;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 分层排序器V2
 * 基于四大类别的分层评分系统进行司机排序
 * 
 * <AUTHOR> Assistant
 */
public class HierarchicalSorterV2 {
    
    private static final Logger logger = LoggerFactory.getLogger(HierarchicalSorterV2.class);
    
    private final CategoryBasedScorer categoryBasedScorer;
    private final WeightStrategy weightStrategy;
    
    public HierarchicalSorterV2(WeightStrategy weightStrategy) {
        this.categoryBasedScorer = new CategoryBasedScorer();
        this.weightStrategy = weightStrategy != null ? weightStrategy : 
            new DynamicWeightManager(null); // 如果没有提供权重策略，使用默认的
        
        initializeCategoryScorers();
    }
    
    /**
     * 使用默认权重策略构造
     */
    public HierarchicalSorterV2() {
        this(null);
    }
    
    /**
     * 初始化大类别评分器
     */
    private void initializeCategoryScorers() {
        categoryBasedScorer.addCategoryScorer(new TimeSpaceEfficiencyCategory());
        categoryBasedScorer.addCategoryScorer(new ServiceQualityCategory());
        categoryBasedScorer.addCategoryScorer(new OrderMatchingCategory());
        categoryBasedScorer.addCategoryScorer(new GlobalEfficiencyCategory());
    }
    
    /**
     * 执行分层排序
     * 
     * @param models 待排序的司机模型列表
     * @param context 排序上下文
     * @return 排序后的司机模型列表
     */
    public List<SortModel> sort(List<SortModel> models, SortContext context) {
        if (CollectionUtils.isEmpty(models)) {
            return Collections.emptyList();
        }
        
        try {
            logger.info("开始分层排序", "司机数量=" + models.size());
            
            // 1. 获取权重配置
            CategoryWeight categoryWeight = weightStrategy.getWeight(
                context.getDspOrder(), 
                context.getSubSku()
            );
            
            logger.info("权重配置", categoryWeight.toString());
            
            // 2. 执行分层评分
            List<SortModelV2> sortModelsV2 = categoryBasedScorer.score(models, context, categoryWeight);
            
            // 3. 排序
            sortModelsV2.sort(createComparator());
            
            // 4. 记录排序结果
            recordSortingResults(sortModelsV2, context);
            
            // 5. 转换回原始模型格式
            return convertToOriginalModels(sortModelsV2, models);
            
        } catch (Exception e) {
            logger.error("分层排序失败", e);
            // 返回原始顺序
            return models;
        }
    }
    
    /**
     * 创建排序比较器
     * 
     * @return 比较器
     */
    private Comparator<SortModelV2> createComparator() {
        return Comparator
            // 1. 首先按最终得分排序（降序）
            .comparing(SortModelV2::getFinalScore, Comparator.reverseOrder())
            // 2. 如果最终得分相同，按加权GPA排序（降序）
            .thenComparing(SortModelV2::getWeightedGpa, Comparator.reverseOrder())
            // 3. 如果GPA也相同，按最低等级排序（升序，等级高的优先）
            .thenComparing(model -> model.getLowestGrade().getGpaValue(), Comparator.reverseOrder())
            // 4. 最后按司机ID排序，确保排序稳定性
            .thenComparing(SortModelV2::getDriverId);
    }
    
    /**
     * 记录排序结果
     * 
     * @param sortModelsV2 排序后的V2模型列表
     * @param context 排序上下文
     */
    private void recordSortingResults(List<SortModelV2> sortModelsV2, SortContext context) {
        try {
            for (int i = 0; i < sortModelsV2.size(); i++) {
                SortModelV2 modelV2 = sortModelsV2.get(i);
                
                // 记录排序信息到上下文（用于后续分析）
                // 这里可以扩展记录更多的排序细节
                logger.debug("排序结果", 
                    "排名=" + (i + 1) + 
                    ", 司机ID=" + modelV2.getDriverId() + 
                    ", 最终得分=" + String.format("%.4f", modelV2.getFinalScore()) +
                    ", GPA=" + String.format("%.4f", modelV2.getWeightedGpa()) +
                    ", 等级=" + modelV2.getAllCategoryGrades());
            }
        } catch (Exception e) {
            logger.warn("记录排序结果失败", e);
        }
    }
    
    /**
     * 转换回原始模型格式
     * 
     * @param sortModelsV2 排序后的V2模型列表
     * @param originalModels 原始模型列表
     * @return 排序后的原始模型列表
     */
    private List<SortModel> convertToOriginalModels(List<SortModelV2> sortModelsV2, List<SortModel> originalModels) {
        // 创建司机ID到原始模型的映射
        var driverIdToModelMap = originalModels.stream()
            .collect(java.util.stream.Collectors.toMap(
                model -> model.getModel().getDriver().getDriverId(),
                model -> model
            ));
        
        // 按V2模型的排序结果重新排列原始模型
        return sortModelsV2.stream()
            .map(modelV2 -> driverIdToModelMap.get(modelV2.getDriverId()))
            .filter(model -> model != null)
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取排序器信息
     * 
     * @return 排序器信息
     */
    public String getSorterInfo() {
        return String.format("HierarchicalSorterV2{categoryScorers=%d, weightStrategy=%s}",
            categoryBasedScorer.getCategoryScorerCount(),
            weightStrategy.getStrategyName());
    }
}
