package com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl;

import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;

import java.util.*;
import java.util.stream.IntStream;

/**
 * 排名归一化策略
 * 基于数据的排名进行归一化，适用于处理异常值
 * 公式：x' = rank(x) / N
 * 
 * <AUTHOR> Assistant
 */
public class RankNormalizer implements NormalizationStrategy {
    
    private final boolean positive;
    
    /**
     * 构造函数
     * 
     * @param positive true表示正向归一化，false表示逆向归一化
     */
    public RankNormalizer(boolean positive) {
        this.positive = positive;
    }
    
    /**
     * 默认构造函数，使用正向归一化
     */
    public RankNormalizer() {
        this(true);
    }
    
    @Override
    public List<Double> normalize(List<Double> values) {
        if (values == null || values.isEmpty()) {
            return Collections.emptyList();
        }
        
        if (values.size() == 1) {
            return Collections.singletonList(positive ? 1.0 : 0.0);
        }
        
        int n = values.size();
        
        // 创建索引数组并按值排序
        Integer[] indices = IntStream.range(0, n).boxed().toArray(Integer[]::new);
        Arrays.sort(indices, (i, j) -> Double.compare(values.get(i), values.get(j)));
        
        // 计算排名（处理相同值的情况）
        double[] ranks = new double[n];
        for (int i = 0; i < n; i++) {
            int currentIndex = indices[i];
            double currentValue = values.get(currentIndex);
            
            // 找到相同值的范围
            int start = i;
            int end = i;
            while (end < n - 1 && Double.compare(values.get(indices[end + 1]), currentValue) == 0) {
                end++;
            }
            
            // 计算平均排名（从1开始）
            double averageRank = (start + end + 2) / 2.0;
            
            // 为所有相同值分配相同的平均排名
            for (int j = start; j <= end; j++) {
                ranks[indices[j]] = averageRank;
            }
            
            i = end; // 跳过已处理的相同值
        }
        
        // 归一化排名到[0,1]区间
        List<Double> normalized = new ArrayList<>();
        for (double rank : ranks) {
            double normalizedRank = (rank - 1) / (n - 1);
            
            // 如果是逆向归一化，则取反
            if (!positive) {
                normalizedRank = 1.0 - normalizedRank;
            }
            
            normalized.add(normalizedRank);
        }
        
        return normalized;
    }
    
    @Override
    public double normalize(double value, double min, double max, Double mean, Double stdDev) {
        // 排名归一化需要完整的数据集，单个值无法计算排名
        // 这里提供一个简化的实现
        if (max == min) {
            return positive ? 1.0 : 0.0;
        }
        
        // 使用线性插值估算排名位置
        double position = (value - min) / (max - min);
        
        if (!positive) {
            position = 1.0 - position;
        }
        
        return Math.max(0.0, Math.min(1.0, position));
    }
    
    @Override
    public String getStrategyName() {
        return positive ? "Rank" : "Rank_Inverse";
    }
    
    @Override
    public String getDescription() {
        return positive ? 
            "排名归一化：基于数据排名进行归一化，排名越高归一化后越大，适用于处理异常值" :
            "排名逆向归一化：基于数据排名进行归一化，排名越高归一化后越小";
    }
    
    @Override
    public boolean isPositive() {
        return positive;
    }
}
