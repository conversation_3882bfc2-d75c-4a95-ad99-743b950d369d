package com.ctrip.dcs.domain.schedule.value.carconfig;

import com.ctrip.dcs.domain.common.value.CarConfigValueVO;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class SubSkuValueVO implements CarConfigValueVO {

    private String subSkuName;

    private Integer dspType;

    private Integer takenType;

    private String checkChainId;

    private String sortChainId;

    private Long retry;

    @JsonCreator
    public SubSkuValueVO(@JsonProperty("subSkuName") String subSkuName, @JsonProperty("dspType") Integer dspType, @JsonProperty("takenType") Integer takenType, @JsonProperty("checkChainId") String checkChainId, @JsonProperty("sortChainId") String sortChainId, @JsonProperty("retry") Long retry) {
        this.subSkuName = subSkuName;
        this.dspType = dspType;
        this.takenType = takenType;
        this.checkChainId = checkChainId;
        this.sortChainId = sortChainId;
        this.retry = retry;
    }
}
