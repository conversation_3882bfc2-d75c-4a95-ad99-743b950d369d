package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.timespace;

import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;
import com.ctrip.dcs.domain.schedule.value.TimeIntervalVO;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverRelateOrderVisitor;

import java.util.List;

/**
 * 局部空驶距离特征
 * 对应现有的F10特征，评估司机的局部空驶距离效率
 * 
 * <AUTHOR> Assistant
 */
public class LocalEmptyDistanceFeature extends AbstractFeatureV2 {
    
    private static final int TIME_INTERVAL_THRESHOLD = 105 * 60; // 105分钟转换为秒
    private static final double MAX_EMPTY_DISTANCE_SCORE = 20.0; // 最大空驶距离得分
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DspOrderVO order = model.getModel().getOrder();
        DriverVO driver = model.getModel().getDriver();
        
        // 获取司机相关订单信息
        DriverRelateOrderVO relateOrder = context.getRelateOrderNoEmptyDriveLbs(driver.getDriverId());
        if (relateOrder == null) {
            return DEFAULT_VALUE;
        }
        
        // 创建时间间隔计算对象
        TimeIntervalVO timeIntervalVO = new TimeIntervalVO(
            order.estimatedUseTime(), 
            order.predictServiceStopTime(), 
            driver.getWorkTimes()
        );
        
        // 计算前向和后向时间间隔
        double forwardInterval = timeIntervalVO.interval(
            relateOrder.getFrowardPredictServiceStopTime(), 
            TIME_INTERVAL_THRESHOLD
        );
        
        double backwardInterval = timeIntervalVO.interval(
            relateOrder.getBackwardEstimatedUseTime(), 
            TIME_INTERVAL_THRESHOLD
        );
        
        // 获取前向和后向空驶距离
        double forwardEmptyDistance = safeGetDouble(
            relateOrder.getFrowardEmptyDrivingInfo() != null ? 
            relateOrder.getFrowardEmptyDrivingInfo().getEmptyDistance() : null, 
            35.0
        );
        
        double backwardEmptyDistance = safeGetDouble(
            relateOrder.getBackwardEmptyDrivingInfo() != null ? 
            relateOrder.getBackwardEmptyDrivingInfo().getEmptyDistance() : null, 
            35.0
        );
        
        // 根据时间间隔条件计算空驶距离得分
        double score = 0.0;
        
        if (forwardInterval <= TIME_INTERVAL_THRESHOLD && backwardInterval > TIME_INTERVAL_THRESHOLD) {
            // 前向时间间隔<=105 && 后向时间间隔>105
            score = Math.min(0, MAX_EMPTY_DISTANCE_SCORE - forwardEmptyDistance);
        } else if (forwardInterval > TIME_INTERVAL_THRESHOLD && backwardInterval <= TIME_INTERVAL_THRESHOLD) {
            // 前向时间间隔>105 && 后向时间间隔<=105
            score = Math.min(0, MAX_EMPTY_DISTANCE_SCORE - backwardEmptyDistance);
        } else if (forwardInterval <= TIME_INTERVAL_THRESHOLD && backwardInterval <= TIME_INTERVAL_THRESHOLD) {
            // 前向时间间隔<=105 && 后向时间间隔<=105
            score = Math.min(0, MAX_EMPTY_DISTANCE_SCORE - forwardEmptyDistance) + 
                   Math.min(0, MAX_EMPTY_DISTANCE_SCORE - backwardEmptyDistance);
        }
        // 其他情况（都>105）得分为0
        
        // 转换为正向得分（原逻辑中负值表示惩罚，这里转换为正向得分）
        return Math.max(0, score + MAX_EMPTY_DISTANCE_SCORE * 2);
    }
    
    @Override
    public void loadData(List<SortModel> models, SortContext context) {
        DspOrderVO order = context.getDspOrder();
        List<DriverVO> drivers = models.stream()
            .map(model -> model.getModel().getDriver())
            .toList();
        
        // 加载司机相关订单数据
        context.accept(new DriverRelateOrderVisitor(order, drivers, 12, false));
    }
    
    @Override
    public String getFeatureName() {
        return "LocalEmptyDistance";
    }
    
    @Override
    public String getDescription() {
        return "局部空驶距离特征：评估司机的局部空驶距离效率，空驶距离越小得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，值越大得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 0.8; // 对应原F10的权重
    }
}
