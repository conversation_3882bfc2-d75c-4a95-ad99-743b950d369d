package com.ctrip.dcs.domain.schedule.sort.v2.grade;

import com.ctrip.dcs.domain.schedule.sort.v2.weight.CategoryWeight;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * GPA计算器
 * 负责根据各大类别的等级和权重计算加权GPA
 * 
 * <AUTHOR> Assistant
 */
public class GpaCalculator {
    
    private static final int SCALE = 4; // 保留4位小数
    
    /**
     * 计算加权GPA
     * 
     * @param categoryGrades 各大类别的等级
     * @param weights 各大类别的权重
     * @return 加权GPA (0-4之间的小数)
     */
    public double calculateWeightedGpa(Map<String, Grade> categoryGrades, CategoryWeight weights) {
        if (categoryGrades == null || categoryGrades.isEmpty()) {
            return 0.0;
        }
        
        double totalWeightedScore = 0.0;
        double totalWeight = 0.0;
        
        for (Map.Entry<String, Grade> entry : categoryGrades.entrySet()) {
            String categoryName = entry.getKey();
            Grade grade = entry.getValue();
            double weight = weights.getWeight(categoryName);
            
            if (weight > 0) {
                totalWeightedScore += grade.getGpaValue() * weight;
                totalWeight += weight;
            }
        }
        
        if (totalWeight == 0) {
            return 0.0;
        }
        
        double gpa = totalWeightedScore / totalWeight;
        
        // 使用BigDecimal进行精确计算，避免浮点数精度问题
        return BigDecimal.valueOf(gpa)
                .setScale(SCALE, RoundingMode.HALF_UP)
                .doubleValue();
    }
    
    /**
     * 计算简单平均GPA（不考虑权重）
     * 
     * @param categoryGrades 各大类别的等级
     * @return 平均GPA
     */
    public double calculateAverageGpa(Map<String, Grade> categoryGrades) {
        if (categoryGrades == null || categoryGrades.isEmpty()) {
            return 0.0;
        }
        
        double totalGpaValue = categoryGrades.values().stream()
                .mapToInt(Grade::getGpaValue)
                .sum();
        
        double averageGpa = totalGpaValue / categoryGrades.size();
        
        return BigDecimal.valueOf(averageGpa)
                .setScale(SCALE, RoundingMode.HALF_UP)
                .doubleValue();
    }
    
    /**
     * 获取最低等级（短板效应）
     * 
     * @param categoryGrades 各大类别的等级
     * @return 最低等级
     */
    public Grade getLowestGrade(Map<String, Grade> categoryGrades) {
        if (categoryGrades == null || categoryGrades.isEmpty()) {
            return Grade.D;
        }
        
        return categoryGrades.values().stream()
                .min((g1, g2) -> Integer.compare(g1.getGpaValue(), g2.getGpaValue()))
                .orElse(Grade.D);
    }
    
    /**
     * 检查是否存在D级（短板检查）
     * 
     * @param categoryGrades 各大类别的等级
     * @return 是否存在D级
     */
    public boolean hasPoorGrade(Map<String, Grade> categoryGrades) {
        if (categoryGrades == null || categoryGrades.isEmpty()) {
            return true;
        }
        
        return categoryGrades.values().stream()
                .anyMatch(Grade::isPoor);
    }
    
    /**
     * 计算等级分布权重
     * 用于分析各等级在总体中的占比
     * 
     * @param categoryGrades 各大类别的等级
     * @param weights 各大类别的权重
     * @return 等级分布权重
     */
    public Map<Grade, Double> calculateGradeWeightDistribution(Map<String, Grade> categoryGrades, CategoryWeight weights) {
        Map<Grade, Double> distribution = new java.util.HashMap<>();
        
        // 初始化
        for (Grade grade : Grade.values()) {
            distribution.put(grade, 0.0);
        }
        
        double totalWeight = 0.0;
        for (Map.Entry<String, Grade> entry : categoryGrades.entrySet()) {
            String categoryName = entry.getKey();
            Grade grade = entry.getValue();
            double weight = weights.getWeight(categoryName);
            
            if (weight > 0) {
                distribution.put(grade, distribution.get(grade) + weight);
                totalWeight += weight;
            }
        }
        
        // 归一化为百分比
        if (totalWeight > 0) {
            for (Grade grade : Grade.values()) {
                double percentage = distribution.get(grade) / totalWeight;
                distribution.put(grade, BigDecimal.valueOf(percentage)
                        .setScale(SCALE, RoundingMode.HALF_UP)
                        .doubleValue());
            }
        }
        
        return distribution;
    }
}
