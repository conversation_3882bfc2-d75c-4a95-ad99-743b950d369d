package com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl;

import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 对数归一化策略
 * 适用于长尾分布的数据，先取对数再进行Min-Max归一化
 * 公式：x' = log(x) / log(max)
 * 
 * <AUTHOR> Assistant
 */
public class LogNormalizer implements NormalizationStrategy {
    
    private final boolean positive;
    private final double base; // 对数底数，默认为自然对数e
    
    /**
     * 构造函数
     * 
     * @param positive true表示正向归一化，false表示逆向归一化
     * @param base 对数底数
     */
    public LogNormalizer(boolean positive, double base) {
        this.positive = positive;
        this.base = base;
    }
    
    /**
     * 默认构造函数，使用正向归一化和自然对数
     */
    public LogNormalizer() {
        this(true, Math.E);
    }
    
    /**
     * 构造函数，指定归一化方向
     * 
     * @param positive true表示正向归一化，false表示逆向归一化
     */
    public LogNormalizer(boolean positive) {
        this(positive, Math.E);
    }
    
    @Override
    public List<Double> normalize(List<Double> values) {
        if (values == null || values.isEmpty()) {
            return Collections.emptyList();
        }
        
        if (values.size() == 1) {
            return Collections.singletonList(positive ? 1.0 : 0.0);
        }
        
        // 找到最小值和最大值，确保所有值都为正数
        double min = Collections.min(values);
        double max = Collections.max(values);
        
        // 如果存在非正数，需要进行偏移
        double offset = min <= 0 ? Math.abs(min) + 1 : 0;
        
        List<Double> normalized = new ArrayList<>();
        for (Double value : values) {
            normalized.add(normalize(value, min, max, null, null));
        }
        
        return normalized;
    }
    
    @Override
    public double normalize(double value, double min, double max, Double mean, Double stdDev) {
        if (max == min) {
            return positive ? 1.0 : 0.0;
        }
        
        // 确保值为正数
        double offset = min <= 0 ? Math.abs(min) + 1 : 0;
        double adjustedValue = value + offset;
        double adjustedMax = max + offset;
        
        if (adjustedValue <= 0 || adjustedMax <= 0) {
            return positive ? 0.0 : 1.0;
        }
        
        // 计算对数归一化
        double logValue = Math.log(adjustedValue) / Math.log(base);
        double logMax = Math.log(adjustedMax) / Math.log(base);
        
        double normalized;
        if (logMax == 0) {
            normalized = positive ? 1.0 : 0.0;
        } else {
            normalized = logValue / logMax;
        }
        
        // 如果是逆向归一化，则取反
        if (!positive) {
            normalized = 1.0 - normalized;
        }
        
        // 确保结果在[0,1]范围内
        return Math.max(0.0, Math.min(1.0, normalized));
    }
    
    @Override
    public String getStrategyName() {
        String baseName = base == Math.E ? "Ln" : "Log" + (int)base;
        return positive ? baseName : baseName + "_Inverse";
    }
    
    @Override
    public String getDescription() {
        String baseName = base == Math.E ? "自然对数" : "以" + (int)base + "为底的对数";
        return positive ? 
            baseName + "归一化：适用于长尾分布，先取对数再归一化，值越大归一化后越大" :
            baseName + "逆向归一化：适用于长尾分布，先取对数再归一化，值越大归一化后越小";
    }
    
    @Override
    public boolean isPositive() {
        return positive;
    }
}
