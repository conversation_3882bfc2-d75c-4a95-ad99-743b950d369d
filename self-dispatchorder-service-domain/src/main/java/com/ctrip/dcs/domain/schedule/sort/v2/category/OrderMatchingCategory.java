package com.ctrip.dcs.domain.schedule.sort.v2.category;

import com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.matching.*;

/**
 * 订单匹配度类别
 * 包含与订单匹配相关的特征
 * 
 * <AUTHOR> Assistant
 */
public class OrderMatchingCategory extends AbstractCategory {
    
    @Override
    protected void initializeFeatures() {
        // 车型匹配度特征
        addFeature(new VehicleTypeMatchFeature());
        
        // 顺路性特征
        addFeature(new RouteCompatibilityFeature());
        
        // 司机偏好特征
        addFeature(new DriverPreferenceFeature());
        
        // 订单里程价值特征（对应现有F14）
        addFeature(new OrderMileageValueFeature());
    }
    
    @Override
    public String getCategoryName() {
        return "OrderMatching";
    }
    
    @Override
    public String getDescription() {
        return "订单匹配度类别：评估司机与订单的匹配程度，包括车型匹配、顺路性、司机偏好、里程价值等指标";
    }
}
