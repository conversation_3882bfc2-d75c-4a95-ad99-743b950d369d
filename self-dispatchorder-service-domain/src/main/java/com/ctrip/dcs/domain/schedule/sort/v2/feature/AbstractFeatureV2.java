package com.ctrip.dcs.domain.schedule.sort.v2.feature;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抽象特征计算器V2
 * 提供特征计算的通用实现
 * 
 * <AUTHOR> Assistant
 */
public abstract class AbstractFeatureV2 implements FeatureCalculator {
    
    private static final Logger logger = LoggerFactory.getLogger(AbstractFeatureV2.class);
    
    protected static final double DEFAULT_VALUE = 0.0;
    protected static final double DEFAULT_WEIGHT = 1.0;
    
    @Override
    public Map<Long, Double> calculate(List<SortModel> models, SortContext context) {
        Map<Long, Double> featureValues = new HashMap<>();
        
        try {
            // 加载数据
            loadData(models, context);
            
            // 计算每个司机的特征值
            for (SortModel model : models) {
                try {
                    Long driverId = model.getModel().getDriver().getDriverId();
                    double value = calculateSingle(model, context);
                    featureValues.put(driverId, value);
                } catch (Exception e) {
                    logger.warn("计算司机特征值失败", "feature=" + getFeatureName() + 
                               ", driverId=" + model.getModel().getDriver().getDriverId(), e);
                    featureValues.put(model.getModel().getDriver().getDriverId(), DEFAULT_VALUE);
                }
            }
            
            // 归一化处理
            normalizeValues(featureValues);
            
        } catch (Exception e) {
            logger.error("特征计算失败", "feature=" + getFeatureName(), e);
            // 返回默认值
            for (SortModel model : models) {
                featureValues.put(model.getModel().getDriver().getDriverId(), DEFAULT_VALUE);
            }
        }
        
        return featureValues;
    }
    
    /**
     * 归一化特征值
     * 
     * @param featureValues 特征值映射
     */
    protected void normalizeValues(Map<Long, Double> featureValues) {
        if (featureValues.isEmpty()) {
            return;
        }
        
        try {
            NormalizationStrategy strategy = getNormalizationStrategy();
            List<Double> values = featureValues.values().stream().toList();
            List<Double> normalizedValues = strategy.normalize(values);
            
            // 更新归一化后的值
            int index = 0;
            for (Map.Entry<Long, Double> entry : featureValues.entrySet()) {
                if (index < normalizedValues.size()) {
                    entry.setValue(normalizedValues.get(index));
                }
                index++;
            }
        } catch (Exception e) {
            logger.warn("特征值归一化失败", "feature=" + getFeatureName(), e);
        }
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 默认使用Min-Max正向归一化
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return DEFAULT_WEIGHT;
    }
    
    @Override
    public boolean isEnabled(SortContext context) {
        // 默认启用，子类可以重写此方法实现动态开关
        String configKey = "sort.v2.feature." + getFeatureName().toLowerCase() + ".enabled";
        return context.getProperties(configKey, "true").equals("true");
    }
    
    @Override
    public void loadData(List<SortModel> models, SortContext context) {
        // 默认不需要加载额外数据，子类可以重写
    }
    
    /**
     * 获取配置值
     * 
     * @param context 排序上下文
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    protected String getConfig(SortContext context, String configKey, String defaultValue) {
        return context.getProperties(configKey, defaultValue);
    }
    
    /**
     * 获取配置值（整数）
     * 
     * @param context 排序上下文
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    protected Integer getConfigInt(SortContext context, String configKey, Integer defaultValue) {
        return context.getProperties(configKey, defaultValue);
    }
    
    /**
     * 获取配置值（双精度浮点数）
     * 
     * @param context 排序上下文
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    protected Double getConfigDouble(SortContext context, String configKey, Double defaultValue) {
        return context.getProperties(configKey, defaultValue);
    }
    
    /**
     * 安全地获取数值，避免空指针异常
     * 
     * @param value 原始值
     * @param defaultValue 默认值
     * @return 安全的数值
     */
    protected double safeGetDouble(Double value, double defaultValue) {
        return value != null ? value : defaultValue;
    }
    
    /**
     * 安全地获取数值，避免空指针异常
     * 
     * @param value 原始值
     * @param defaultValue 默认值
     * @return 安全的数值
     */
    protected int safeGetInt(Integer value, int defaultValue) {
        return value != null ? value : defaultValue;
    }
    
    /**
     * 限制数值在指定范围内
     * 
     * @param value 原始值
     * @param min 最小值
     * @param max 最大值
     * @return 限制后的值
     */
    protected double clamp(double value, double min, double max) {
        return Math.max(min, Math.min(max, value));
    }
}
