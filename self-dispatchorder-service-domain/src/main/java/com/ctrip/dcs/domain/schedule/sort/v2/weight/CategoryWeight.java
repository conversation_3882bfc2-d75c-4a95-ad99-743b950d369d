package com.ctrip.dcs.domain.schedule.sort.v2.weight;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 大类别权重配置
 * 管理四大类别的权重分配
 * 
 * <AUTHOR> Assistant
 */
public class CategoryWeight {
    
    // 四大类别名称常量
    public static final String TIME_SPACE_EFFICIENCY = "TimeSpaceEfficiency";
    public static final String SERVICE_QUALITY = "ServiceQuality";
    public static final String ORDER_MATCHING = "OrderMatching";
    public static final String GLOBAL_EFFICIENCY = "GlobalEfficiency";
    
    // 默认权重配置
    private static final double DEFAULT_TIME_SPACE_WEIGHT = 0.45; // 时空效率 45%
    private static final double DEFAULT_SERVICE_QUALITY_WEIGHT = 0.25; // 服务质量 25%
    private static final double DEFAULT_ORDER_MATCHING_WEIGHT = 0.20; // 订单匹配度 20%
    private static final double DEFAULT_GLOBAL_EFFICIENCY_WEIGHT = 0.10; // 全局效率 10%
    
    private final Map<String, Double> weights;
    
    /**
     * 使用默认权重构造
     */
    public CategoryWeight() {
        this.weights = new HashMap<>();
        this.weights.put(TIME_SPACE_EFFICIENCY, DEFAULT_TIME_SPACE_WEIGHT);
        this.weights.put(SERVICE_QUALITY, DEFAULT_SERVICE_QUALITY_WEIGHT);
        this.weights.put(ORDER_MATCHING, DEFAULT_ORDER_MATCHING_WEIGHT);
        this.weights.put(GLOBAL_EFFICIENCY, DEFAULT_GLOBAL_EFFICIENCY_WEIGHT);
    }
    
    /**
     * 使用自定义权重构造
     * 
     * @param timeSpaceWeight 时空效率权重
     * @param serviceQualityWeight 服务质量权重
     * @param orderMatchingWeight 订单匹配度权重
     * @param globalEfficiencyWeight 全局效率权重
     */
    public CategoryWeight(double timeSpaceWeight, double serviceQualityWeight, 
                         double orderMatchingWeight, double globalEfficiencyWeight) {
        this.weights = new HashMap<>();
        this.weights.put(TIME_SPACE_EFFICIENCY, timeSpaceWeight);
        this.weights.put(SERVICE_QUALITY, serviceQualityWeight);
        this.weights.put(ORDER_MATCHING, orderMatchingWeight);
        this.weights.put(GLOBAL_EFFICIENCY, globalEfficiencyWeight);
        
        // 自动归一化权重
        normalizeWeights();
    }
    
    /**
     * 使用权重映射构造
     * 
     * @param weightMap 权重映射
     */
    public CategoryWeight(Map<String, Double> weightMap) {
        this.weights = new HashMap<>(weightMap);
        normalizeWeights();
    }
    
    /**
     * 获取指定类别的权重
     * 
     * @param categoryName 类别名称
     * @return 权重值
     */
    public double getWeight(String categoryName) {
        return weights.getOrDefault(categoryName, 0.0);
    }
    
    /**
     * 设置指定类别的权重
     * 
     * @param categoryName 类别名称
     * @param weight 权重值
     */
    public void setWeight(String categoryName, double weight) {
        weights.put(categoryName, weight);
    }
    
    /**
     * 获取时空效率权重
     * 
     * @return 时空效率权重
     */
    public double getTimeSpaceEfficiencyWeight() {
        return getWeight(TIME_SPACE_EFFICIENCY);
    }
    
    /**
     * 获取服务质量权重
     * 
     * @return 服务质量权重
     */
    public double getServiceQualityWeight() {
        return getWeight(SERVICE_QUALITY);
    }
    
    /**
     * 获取订单匹配度权重
     * 
     * @return 订单匹配度权重
     */
    public double getOrderMatchingWeight() {
        return getWeight(ORDER_MATCHING);
    }
    
    /**
     * 获取全局效率权重
     * 
     * @return 全局效率权重
     */
    public double getGlobalEfficiencyWeight() {
        return getWeight(GLOBAL_EFFICIENCY);
    }
    
    /**
     * 获取所有权重
     * 
     * @return 权重映射的副本
     */
    public Map<String, Double> getAllWeights() {
        return new HashMap<>(weights);
    }
    
    /**
     * 归一化权重，确保所有权重之和为1
     */
    public void normalizeWeights() {
        double totalWeight = weights.values().stream().mapToDouble(Double::doubleValue).sum();
        
        if (totalWeight > 0) {
            weights.replaceAll((k, v) -> BigDecimal.valueOf(v / totalWeight)
                    .setScale(4, RoundingMode.HALF_UP)
                    .doubleValue());
        }
    }
    
    /**
     * 检查权重配置是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        // 检查是否包含所有必需的类别
        if (!weights.containsKey(TIME_SPACE_EFFICIENCY) ||
            !weights.containsKey(SERVICE_QUALITY) ||
            !weights.containsKey(ORDER_MATCHING) ||
            !weights.containsKey(GLOBAL_EFFICIENCY)) {
            return false;
        }
        
        // 检查权重是否为非负数
        for (Double weight : weights.values()) {
            if (weight == null || weight < 0) {
                return false;
            }
        }
        
        // 检查权重之和是否接近1（允许小的浮点误差）
        double totalWeight = weights.values().stream().mapToDouble(Double::doubleValue).sum();
        return Math.abs(totalWeight - 1.0) < 0.001;
    }
    
    /**
     * 创建默认权重配置
     * 
     * @return 默认权重配置
     */
    public static CategoryWeight createDefault() {
        return new CategoryWeight();
    }
    
    /**
     * 创建均等权重配置
     * 
     * @return 均等权重配置
     */
    public static CategoryWeight createEqual() {
        return new CategoryWeight(0.25, 0.25, 0.25, 0.25);
    }
    
    /**
     * 创建注重时空效率的权重配置
     * 
     * @return 注重时空效率的权重配置
     */
    public static CategoryWeight createTimeSpaceFocused() {
        return new CategoryWeight(0.60, 0.20, 0.15, 0.05);
    }
    
    /**
     * 创建注重服务质量的权重配置
     * 
     * @return 注重服务质量的权重配置
     */
    public static CategoryWeight createServiceQualityFocused() {
        return new CategoryWeight(0.25, 0.50, 0.15, 0.10);
    }
    
    @Override
    public String toString() {
        return String.format("CategoryWeight{时空效率=%.2f, 服务质量=%.2f, 订单匹配度=%.2f, 全局效率=%.2f}",
                getTimeSpaceEfficiencyWeight(),
                getServiceQualityWeight(),
                getOrderMatchingWeight(),
                getGlobalEfficiencyWeight());
    }
}
