package com.ctrip.dcs.domain.schedule.sort;

import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.sort.feature.impl.AbstractFeature;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

/**
 * 检查记录
 * <AUTHOR>
 */
@Getter
public class SortRecord {

    @JsonProperty("dsp_order_id")
    private String dspOrderId;

    @JsonProperty("user_order_id")
    private String userOrderId;

    @JsonProperty("driver_id")
    private Long driverId;

    @JsonProperty("transport_group_id")
    private Long transportGroupId;

    @JsonProperty("sub_sku_id")
    private Integer subSkuId;

    @JsonProperty("city_id")
    private Integer cityId;

    @JsonProperty("task_id")
    private Long taskId;

    @JsonProperty("task_round")
    private Integer taskRound;

    @JsonProperty("schedule_id")
    private Long scheduleId;

    @JsonProperty("sort_score")
    private Double sortScore;

    @JsonProperty("trace_id")
    private String traceId;

    @JsonProperty("details")
    private String details;
    @JsonProperty("value_detail_json")
    private String valueDetailJson;

    public SortRecord(SortModel model, SortContext context) {
        if (context != null) {
            if (context.getDspOrder() != null) {
                this.dspOrderId = context.getDspOrder().getDspOrderId();
                this.userOrderId = context.getDspOrder().getUserOrderId();
                this.cityId = context.getDspOrder().getCityId();
            }
            if (context.getDuid() != null) {
                this.taskId = context.getDuid().getTaskId();
                this.taskRound = context.getDuid().getRound();
                this.scheduleId = context.getDuid().getScheduleId();
                this.subSkuId = context.getDuid().getSubSkuId();
            }
        }
        if (model != null && model.getModel() != null) {
            if (model.getModel().getDriver() != null) {
                this.driverId = model.getModel().getDriver().getDriverId();
            }
            if (model.getModel().getTransportGroup() != null) {
                this.transportGroupId = model.getModel().getTransportGroup().getTransportGroupId();
            }
            this.sortScore = model.getScore();
            if (CollectionUtils.isNotEmpty(model.getValues())) {
                StringBuilder sb = new StringBuilder();
                Map<String,Object> valueDetailMap = new TreeMap<>();
                for (Value value : model.getValues()) {
                    if (value != null) {
                        sb.append(value.getId()).append(":").append(value.getValue()).append(";");
                    }
                    if (value != null && MapUtils.isNotEmpty(value.getValueDetails())) {
                        valueDetailMap.put(value.getId(),value.getValueDetails());
                    }
                }
                this.details = sb.toString();
                this.valueDetailJson = JacksonSerializer.INSTANCE().serialize(valueDetailMap);
            }
        }
        this.traceId = getGlobalTraceId();
    }

    private String getGlobalTraceId() {
        LoggerContext current = LoggerContext.getCurrent();
        if (current == null || StringUtils.isEmpty(current.getGlobalTraceId())) {
            current = LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        }
        return current.getGlobalTraceId();
    }

    public Map<String, String> toMap() {
        ImmutableMap.Builder<String, String> builder = ImmutableMap.builder();
        if (this.getDspOrderId() != null) {
            builder.put("dsp_order_id", this.getDspOrderId());
        }
        if (this.getUserOrderId() != null) {
            builder.put("user_order_id", this.getUserOrderId());
        }
        if (this.getTraceId() != null) {
            builder.put("trace_id", this.getTraceId());
        }
        if (this.getDriverId() != null) {
            builder.put("driver_id", this.getDriverId().toString());
        }
        if (this.getTransportGroupId() != null) {
            builder.put("transport_group_id", this.getTransportGroupId().toString());
        }
        if (this.getSubSkuId() != null) {
            builder.put("sub_sku_id", this.getSubSkuId().toString());
        }
        if (this.getCityId() != null) {
            builder.put("city_id", this.getCityId().toString());
        }
        if (this.getTaskId() != null) {
            builder.put("task_id", this.getTaskId().toString());
        }
        if (this.getTaskRound() != null) {
            builder.put("task_round", this.getTaskRound().toString());
        }
        if (this.getScheduleId() != null) {
            builder.put("schedule_id", this.getScheduleId().toString());
        }
        if (this.getSortScore() != null) {
            builder.put("sort_score", this.getSortScore().toString());
        }
        if (this.getDetails() != null) {
            builder.put("details", this.getDetails());
        }
        if (this.getValueDetailJson() != null) {
            builder.put("value_detail_json", this.getValueDetailJson());
        }
        return builder.build();
    }
}
