package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.service;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;
import com.ctrip.dcs.domain.schedule.value.CityPointVO;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;
import com.ctrip.dcs.domain.schedule.visitor.impl.CityPointsVisitor;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverPointsVisitor;

import java.util.List;
import java.util.Optional;

/**
 * 司机分特征
 * 对应现有的F2特征，评估司机的服务评分
 * 
 * <AUTHOR> Assistant
 */
public class DriverPointsFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        Integer cityId = model.getModel().getOrder().getCityId();
        
        // 获取司机分数
        Optional<DriverPointsVO> driverPointsOpt = context.getDriverPoints(driver.getDriverId());
        if (!driverPointsOpt.isPresent()) {
            return DEFAULT_VALUE;
        }
        
        double driverPoints = safeGetDouble(driverPointsOpt.get().getPoints(), 0.0);
        
        // 获取城市最高分数
        Optional<CityPointVO> cityPointsOpt = context.getCityPoints(cityId);
        double maxPoints;
        
        if (cityPointsOpt.isPresent()) {
            maxPoints = safeGetDouble(cityPointsOpt.get().getMaxPoints(), 1.0);
        } else {
            // 如果没有城市分数信息，使用当前批次的最高分数
            maxPoints = context.getTopDriverPoints(1).stream()
                .findFirst()
                .map(DriverPointsVO::getPoints)
                .map(points -> safeGetDouble(points, 1.0))
                .orElse(1.0);
        }
        
        // 避免除零
        if (maxPoints <= 0) {
            maxPoints = 1.0;
        }
        
        // 计算司机分占比
        double ratio = driverPoints / maxPoints;
        
        // 转换为0-100分制
        return Math.max(0.0, ratio * 100);
    }
    
    @Override
    public void loadData(List<SortModel> models, SortContext context) {
        Integer cityId = context.getDspOrder().getCityId();
        List<DriverVO> drivers = models.stream()
            .map(model -> model.getModel().getDriver())
            .toList();
        
        // 加载司机分数和城市分数数据
        context.accept(
            new DriverPointsVisitor(drivers), 
            new CityPointsVisitor(cityId)
        );
    }
    
    @Override
    public String getFeatureName() {
        return "DriverPoints";
    }
    
    @Override
    public String getDescription() {
        return "司机分特征：基于司机分数与城市最高分数的比值评估司机服务质量，分数越高得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，司机分越高得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 1.0; // 对应原F2的权重
    }
}
