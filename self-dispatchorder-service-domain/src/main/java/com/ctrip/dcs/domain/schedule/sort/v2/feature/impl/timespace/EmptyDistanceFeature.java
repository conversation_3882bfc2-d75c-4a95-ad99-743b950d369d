package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.timespace;

import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverRelateOrderVisitor;

import java.util.List;

/**
 * 空驶距离特征
 * 评估司机的空驶距离效率
 * 
 * <AUTHOR> Assistant
 */
public class EmptyDistanceFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        
        // 获取司机相关订单信息
        DriverRelateOrderVO relateOrder = context.getDriverRelateOrder(driver.getDriverId());
        if (relateOrder == null) {
            return DEFAULT_VALUE;
        }
        
        // 获取前向空驶距离
        double emptyDistance = DEFAULT_VALUE;
        if (relateOrder.getFrowardEmptyDrivingInfo() != null) {
            emptyDistance = safeGetDouble(
                relateOrder.getFrowardEmptyDrivingInfo().getEmptyDistance(), 
                35.0 // 默认空驶距离
            );
        }
        
        // 空驶距离越小，得分越高（使用逆向计算）
        // 假设最大合理空驶距离为100公里
        double maxEmptyDistance = getConfigDouble(context, "feature.empty.distance.max", 100.0);
        return Math.max(0, maxEmptyDistance - emptyDistance);
    }
    
    @Override
    public void loadData(List<SortModel> models, SortContext context) {
        DspOrderVO order = context.getDspOrder();
        List<DriverVO> drivers = models.stream()
            .map(model -> model.getModel().getDriver())
            .toList();
        
        // 加载司机相关订单数据
        context.accept(new DriverRelateOrderVisitor(order, drivers, 12, true));
    }
    
    @Override
    public String getFeatureName() {
        return "EmptyDistance";
    }
    
    @Override
    public String getDescription() {
        return "空驶距离特征：评估司机的空驶距离效率，空驶距离越小得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，值越大得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 1.0;
    }
}
