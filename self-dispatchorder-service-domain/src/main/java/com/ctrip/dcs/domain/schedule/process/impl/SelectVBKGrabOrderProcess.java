package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabOrderCode;
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.exception.OrderStatusException;
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.OperatorVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.SortService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.GrabOrderResultVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 抢单轮选
 * <AUTHOR>
 */
@Component
public class SelectVBKGrabOrderProcess extends SelectGrabOrderProcess {

    private static final Logger logger = LoggerFactory.getLogger(SelectVBKGrabOrderProcess.class);

    @Autowired
    private SelectGrabOrderRepository selectGrabOrderRepository;

    @Autowired
    private ScheduleTaskRepository scheduleTaskRepository;

    @Autowired
    private SortService sortService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private DriverOrderFactory driverOrderFactory;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private ConfirmDspOrderService confirmDspOrderService;

    @Autowired
    @Qualifier("confirmSaasDspOrderServiceImpl")
    private ConfirmDspOrderService confirmSaasDspOrderService;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;

    @Override
    public void execute(DspOrderVO dspOrder, SubSkuVO subSku) {
        // 查询已抢订单
        List<GrabOrderDO> grabOrders = selectGrabOrderRepository.find(dspOrder.getDspOrderId(), subSku.getSubSkuId());
        if (CollectionUtils.isEmpty(grabOrders)) {
            logger.info("SelectVBKGrabOrderProcessInfo", "no grab order found! dsp order id:{}, sub sku id:{}", dspOrder.getDspOrderId(), subSku.getSubSkuId());
            return;
        }
        List<GrabOrderResultVO> results = grabOrders.stream().map(GrabOrderResultVO::new).collect(Collectors.toList());
        try {
            Set<Long> driverIds = grabOrders.stream().map(GrabOrderDO::getDriverId).collect(Collectors.toSet());
            logger.info("SelectVBKGrabOrderProcess_content", "dsp order id:{}, driver ids:{}", dspOrder.getDspOrderId(), JsonUtils.toJson(driverIds));
            // 查询司机
            Long supplierId = dspOrder.getSupplierId() != null ? dspOrder.getSupplierId().longValue() : null;
            List<DriverVO> drivers = queryDriverService.queryDriver(driverIds, CategoryUtils.selfGetParentType(dspOrder), supplierId);
            // 排序
            drivers = sortService.sort(dspOrder, subSku, drivers);
            Map<Long /* driver id */, GrabOrderResultVO> map = results.stream().collect(Collectors.toMap(o -> o.getGrabOrder().getDriverId(), o -> o, (o1, o2) -> o2));
            for (DriverVO driver : drivers) {
                try {
                    if (!Objects.equals(dspOrder.getOrderStatus(), OrderStatusEnum.DISPATCH_CONFIRMED.getCode())) {
                        logger.info("SelectVBKGrabOrderProcessInfo", "dsp order status is not DISPATCH_CONFIRMED, dsp order id:{}, sub sku id:{}, status:{}", dspOrder.getDspOrderId(), subSku.getSubSkuId(), dspOrder.getOrderStatus());
                        continue;
                    }
                    Long driverId = driver.getDriverId();
                    GrabOrderResultVO result = map.get(driverId);
                    if (Objects.isNull(result)) {
                        continue;
                    }
                    GrabOrderDO grabOrder = result.getGrabOrder();
                    // 订单接单检查
                    CheckModel checkModel = checkService.check(new TakenCheckCommand(dspOrder, subSku, driver, DuidVO.of(grabOrder.getDuid())));
                    if (!checkModel.isPass()) {
                        logger.info("SelectVBKGrabOrderProcessInfo", "driver taken check fail! dsp order id:{}, driver id:{}", grabOrder.getDspOrderId(), driver.getDriverId());
                        GrabOrderCode code = GrabOrderCode.valueOf(checkModel.getCheckCode());
                        result.setCode(code);
                        continue;
                    }
                    // 派发任务
                    ScheduleTaskDO scheduleTask = scheduleTaskRepository.query(grabOrder.getDuid());
                    if (scheduleTask == null) {
                        logger.info("SelectVBKGrabOrderProcessInfo", "schedule task is null! duid:{}", grabOrder.getDuid());
                        continue;
                    }
                    // 查询司机车辆信息
                    VehicleVO vehicle = queryVehicle(driver,CategoryUtils.selfGetParentType(dspOrder));
                    // 查询订单运力组
                    TransportGroupVO transportGroup = queryTransportGroup(dspOrder);
                    // 下司机单
                    DriverOrderVO driverOrder = createDriverOrder(checkModel.getModel(), transportGroup, scheduleTask);
                    if (driverOrder == null) {
                        continue;
                    }
                    // 应单
                    GrabOrderCode code = confirmOrder(dspOrder, driver, vehicle, transportGroup, driverOrder, scheduleTask);
                    result.setCode(code);
                    if (code != null && code.isSuccess()) {
                        // 司机应单成功
                        break;
                    }
                } catch (Exception e) {
                    logger.warn(e);
                }
            }
        } catch (Exception e) {
            logger.error(e);
        } finally {
            // 通知抢单结果
            notice(results);
            // 清空轮选信息
            clear(dspOrder, subSku);
        }
    }

    public VBKDriverGrabOrderDO queryVBKDriverGrabOrder(DspOrderVO dspOrder) {
        VBKDriverGrabOrderDO grabOrder = vbkDriverGrabOrderRepository.queryBySupplierId(dspOrder.getDspOrderId(), dspOrder.getSupplierId());
        if (grabOrder == null) {
            throw ErrorCode.GRAB_ORDER_NULL_ERROR.getBizException();
        }
        return grabOrder;
    }

    public TransportGroupVO queryTransportGroup(DspOrderVO dspOrder) {
        return queryTransportGroupService.queryTransportGroup(dspOrder.getTransportGroupId());
    }

    public DriverOrderVO createDriverOrder(DspModelVO model, TransportGroupVO transportGroup, ScheduleTaskDO scheduleTask) {
        // vbk抢单
        VBKDriverGrabOrderDO vbkDriverGrabOrder = queryVBKDriverGrabOrder(model.getOrder());
        DriverOrderVO driverOrder = driverOrderFactory.create(model, transportGroup, scheduleTask, vbkDriverGrabOrder, null);
        if (driverOrder == null) {
            logger.warn("vbkGrabOrder_create_driver_order_error", "create failed");
        }
        return driverOrder;
    }

    /**
     * 应单
     * @return
     */
    public GrabOrderCode confirmOrder(DspOrderVO dspOrder, DriverVO driver, VehicleVO vehicle, TransportGroupVO transportGroup, DriverOrderVO driverOrder, ScheduleTaskDO task) {
        try {

            DriverAndCarConfirmVO confirmVO = DriverAndCarConfirmVO.builder()
                    .dspOrder(dspOrder)
                    .serviceProvider(new ServiceProviderVO(dspOrder.getSpId()))
                    .supplier(new SupplierVO(dspOrder.getSupplierId().longValue()))
                    .transportGroup(transportGroup)
                    .driver(driver)
                    .vehicle(vehicle)
                    .duid(DuidVO.of(task))
                    .driverOrderId(driverOrder == null ? StringUtils.EMPTY : driverOrder.getDriverOrderId())
                    .rewardAmount(task.getReward().toString())
                    .vbkRewardAmount(task.getVbkReward().toString())
                    .event(OrderStatusEvent.VBK_DRIVER_GRAB)
                    .operator(OperatorVO.systemOperator())
                    .build();
            // 应单
            confirm(dspOrder, confirmVO);
            return GrabOrderCode.SUCCESS;
        } catch (OrderStatusException e) {
            logger.warn(e);
            sendDriverOrderConfirmFailMessage(driverOrder);
        } catch (Exception e) {
            logger.error(e);
            sendDriverOrderConfirmFailMessage(driverOrder);
        }
        return GrabOrderCode.ORDER_TAKEN_FAIL;
    }

    public void confirm(DspOrderVO dspOrder, DriverAndCarConfirmVO confirmVO) throws OrderStatusException {
        // 新流程应单
        if (OrderSourceCodeEnum.isTrip(dspOrder.getOrderSourceCode())) {
            // 携程订单
            confirmDspOrderService.confirm(confirmVO);
        } else {
            // 非携程订单
            confirmSaasDspOrderService.confirm(confirmVO);
        }
    }
}
