package com.ctrip.dcs.domain.schedule.sort.v2.category;

import com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.service.*;

/**
 * 服务质量类别
 * 包含与司机服务质量相关的特征
 * 
 * <AUTHOR> Assistant
 */
public class ServiceQualityCategory extends AbstractCategory {
    
    @Override
    protected void initializeFeatures() {
        // 司机分特征（对应现有F2）
        addFeature(new DriverPointsFeature());
        
        // 投诉率特征
        addFeature(new ComplaintRateFeature());
        
        // 活跃度特征
        addFeature(new ActivityFeature());
        
        // 安全记录特征
        addFeature(new SafetyRecordFeature());
        
        // 完单率特征
        addFeature(new OrderCompleteRateFeature());
    }
    
    @Override
    public String getCategoryName() {
        return "ServiceQuality";
    }
    
    @Override
    public String getDescription() {
        return "服务质量类别：评估司机的服务水平，包括司机分、投诉率、活跃度、安全记录、完单率等指标";
    }
}
