package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.matching;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;

/**
 * 司机偏好特征
 * 评估订单与司机偏好的匹配程度
 * 
 * <AUTHOR> Assistant
 */
public class DriverPreferenceFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        DspOrderVO order = model.getModel().getOrder();
        
        // 计算司机偏好匹配度
        double preferenceScore = calculatePreferenceMatch(driver, order, context);
        
        return preferenceScore * 100;
    }
    
    /**
     * 计算偏好匹配度
     * 
     * @param driver 司机信息
     * @param order 订单信息
     * @param context 排序上下文
     * @return 匹配度 (0-1)
     */
    private double calculatePreferenceMatch(DriverVO driver, DspOrderVO order, SortContext context) {
        // 实际实现中需要考虑司机的各种偏好：
        // 1. 距离偏好（短途/长途）
        // 2. 时间偏好（高峰/非高峰）
        // 3. 区域偏好（熟悉的区域）
        // 4. 订单类型偏好
        
        double totalScore = 0.0;
        int factorCount = 0;
        
        // 距离偏好
        double distancePreference = calculateDistancePreference(driver, order);
        totalScore += distancePreference;
        factorCount++;
        
        // 时间偏好
        double timePreference = calculateTimePreference(driver, order);
        totalScore += timePreference;
        factorCount++;
        
        // 区域偏好
        double areaPreference = calculateAreaPreference(driver, order);
        totalScore += areaPreference;
        factorCount++;
        
        return factorCount > 0 ? totalScore / factorCount : 0.5;
    }
    
    /**
     * 计算距离偏好匹配度
     */
    private double calculateDistancePreference(DriverVO driver, DspOrderVO order) {
        // 简化实现：基于司机ID判断距离偏好
        long driverId = driver.getDriverId();
        boolean preferShortDistance = (driverId % 2) == 0;
        
        // 假设订单距离（实际需要从订单信息中获取）
        double orderDistance = (order.getDspOrderId().hashCode() % 50) + 5; // 5-54公里
        
        if (preferShortDistance) {
            // 偏好短途，距离越短得分越高
            return orderDistance <= 20 ? 0.9 : 0.5;
        } else {
            // 偏好长途，距离适中得分最高
            return orderDistance >= 20 && orderDistance <= 40 ? 0.9 : 0.6;
        }
    }
    
    /**
     * 计算时间偏好匹配度
     */
    private double calculateTimePreference(DriverVO driver, DspOrderVO order) {
        // 简化实现：基于司机ID判断时间偏好
        long driverId = driver.getDriverId();
        boolean preferPeakHours = (driverId % 3) == 0;
        
        // 简化判断是否为高峰时间
        boolean isPeakHour = order.estimatedUseTime() != null && 
            (order.estimatedUseTime().getHour() >= 7 && order.estimatedUseTime().getHour() <= 9) ||
            (order.estimatedUseTime().getHour() >= 17 && order.estimatedUseTime().getHour() <= 19);
        
        return (preferPeakHours && isPeakHour) || (!preferPeakHours && !isPeakHour) ? 0.8 : 0.6;
    }
    
    /**
     * 计算区域偏好匹配度
     */
    private double calculateAreaPreference(DriverVO driver, DspOrderVO order) {
        // 简化实现：基于城市ID和司机ID计算区域熟悉度
        long driverId = driver.getDriverId();
        Integer cityId = order.getCityId();
        
        // 假设司机对某些城市更熟悉
        boolean familiarWithArea = (driverId + cityId) % 5 < 3;
        
        return familiarWithArea ? 0.9 : 0.7;
    }
    
    @Override
    public String getFeatureName() {
        return "DriverPreference";
    }
    
    @Override
    public String getDescription() {
        return "司机偏好特征：评估订单与司机偏好的匹配程度，包括距离、时间、区域等偏好";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 0.8;
    }
}
