package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.matching;

import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverRelateOrderVisitor;

import java.util.List;

/**
 * 订单里程价值特征
 * 对应现有的F14特征，评估订单的里程价值效率
 * 
 * <AUTHOR> Assistant
 */
public class OrderMileageValueFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        DspOrderVO order = model.getModel().getOrder();
        
        // 获取司机相关订单信息
        DriverRelateOrderVO relateOrder = context.getDriverRelateOrder(driver.getDriverId());
        if (relateOrder == null) {
            return DEFAULT_VALUE;
        }
        
        // 计算订单里程价值
        double mileageValue = calculateMileageValue(order, relateOrder, context);
        
        return mileageValue;
    }
    
    /**
     * 计算里程价值
     * 
     * @param order 当前订单
     * @param relateOrder 相关订单信息
     * @param context 排序上下文
     * @return 里程价值
     */
    private double calculateMileageValue(DspOrderVO order, DriverRelateOrderVO relateOrder, SortContext context) {
        // 获取订单收益
        double orderRevenue = safeGetDouble(order.getOrderAmount(), 0.0);
        
        // 获取订单行驶里程
        double orderMileage = safeGetDouble(order.getOrderMileage(), 10.0);
        
        // 获取前向空驶距离
        double forwardEmptyDistance = safeGetDouble(
            relateOrder.getFrowardEmptyDrivingInfo() != null ? 
            relateOrder.getFrowardEmptyDrivingInfo().getEmptyDistance() : null, 
            35.0
        );
        
        // 检查是否有后向单
        boolean hasBackwardOrder = relateOrder.getBackwardOrderInfo() != null;
        
        double mileageValue;
        
        if (!hasBackwardOrder) {
            // 没有后向单：订单里程价值 = 待派订单收益 / (前向空驶 + 待派订单行驶里程)
            double totalDistance = forwardEmptyDistance + orderMileage;
            mileageValue = totalDistance > 0 ? orderRevenue / totalDistance : 0.0;
        } else {
            // 有后向单：订单里程价值 = (待派订单收益 + 后向订单收益) / (前向空驶 + 后向空驶 + 待派订单行驶里程 + 后向订单行驶里程)
            double backwardOrderRevenue = safeGetDouble(
                relateOrder.getBackwardOrderInfo().getOrderAmount(), 0.0
            );
            
            double backwardEmptyDistance = safeGetDouble(
                relateOrder.getBackwardEmptyDrivingInfo() != null ? 
                relateOrder.getBackwardEmptyDrivingInfo().getEmptyDistance() : null, 
                35.0
            );
            
            double backwardOrderMileage = safeGetDouble(
                relateOrder.getBackwardOrderInfo().getOrderMileage(), 10.0
            );
            
            double totalRevenue = orderRevenue + backwardOrderRevenue;
            double totalDistance = forwardEmptyDistance + backwardEmptyDistance + orderMileage + backwardOrderMileage;
            
            mileageValue = totalDistance > 0 ? totalRevenue / totalDistance : 0.0;
        }
        
        // 转换为合适的分数范围
        return Math.min(100.0, mileageValue * 10); // 假设里程价值在0-10之间，转换为0-100分
    }
    
    @Override
    public void loadData(List<SortModel> models, SortContext context) {
        DspOrderVO order = context.getDspOrder();
        List<DriverVO> drivers = models.stream()
            .map(model -> model.getModel().getDriver())
            .toList();
        
        // 加载司机相关订单数据
        context.accept(new DriverRelateOrderVisitor(order, drivers, 12, true));
    }
    
    @Override
    public String getFeatureName() {
        return "OrderMileageValue";
    }
    
    @Override
    public String getDescription() {
        return "订单里程价值特征：评估订单的里程价值效率，考虑订单收益与总行驶距离的比值";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，里程价值越高得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 1.0; // 对应原F14的权重
    }
}
