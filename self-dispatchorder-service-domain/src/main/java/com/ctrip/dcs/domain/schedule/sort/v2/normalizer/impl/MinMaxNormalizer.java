package com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl;

import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Min-Max归一化策略
 * 将数据线性缩放到[0,1]区间
 * 公式：x' = (x - min) / (max - min)
 * 
 * <AUTHOR> Assistant
 */
public class MinMaxNormalizer implements NormalizationStrategy {
    
    private final boolean positive;
    
    /**
     * 构造函数
     * 
     * @param positive true表示正向归一化，false表示逆向归一化
     */
    public MinMaxNormalizer(boolean positive) {
        this.positive = positive;
    }
    
    /**
     * 默认构造函数，使用正向归一化
     */
    public MinMaxNormalizer() {
        this(true);
    }
    
    @Override
    public List<Double> normalize(List<Double> values) {
        if (values == null || values.isEmpty()) {
            return Collections.emptyList();
        }
        
        if (values.size() == 1) {
            return Collections.singletonList(positive ? 1.0 : 0.0);
        }
        
        double min = Collections.min(values);
        double max = Collections.max(values);
        
        List<Double> normalized = new ArrayList<>();
        for (Double value : values) {
            normalized.add(normalize(value, min, max, null, null));
        }
        
        return normalized;
    }
    
    @Override
    public double normalize(double value, double min, double max, Double mean, Double stdDev) {
        if (max == min) {
            return positive ? 1.0 : 0.0;
        }
        
        double normalized = (value - min) / (max - min);
        
        // 如果是逆向归一化，则取反
        if (!positive) {
            normalized = 1.0 - normalized;
        }
        
        // 确保结果在[0,1]范围内
        return Math.max(0.0, Math.min(1.0, normalized));
    }
    
    @Override
    public String getStrategyName() {
        return positive ? "MinMax" : "MinMax_Inverse";
    }
    
    @Override
    public String getDescription() {
        return positive ? 
            "Min-Max归一化：将数据线性缩放到[0,1]区间，值越大归一化后越大" :
            "Min-Max逆向归一化：将数据线性缩放到[0,1]区间，值越大归一化后越小";
    }
    
    @Override
    public boolean isPositive() {
        return positive;
    }
}
