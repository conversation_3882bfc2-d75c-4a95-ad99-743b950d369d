package com.ctrip.dcs.domain.schedule.event;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.value.MessageEventVO;
import com.google.common.base.Joiner;

import java.util.List;

/**
 * <AUTHOR>
 */
public class DspOrderGrabCentrePushEvent extends MessageEventVO {

    private String dspOrderId;

    private String driverIds;

    public DspOrderGrabCentrePushEvent(String dspOrderId, List<Long> driverIds, long delay) {
        super(EventConstants.DCS_DSP_ORDER_GRAB_CENTRE_PUSH_EXECUTE, delay);
        this.dspOrderId = dspOrderId;
        this.driverIds = Joiner.on(",").join(driverIds);
    }

    public String getDspOrderId() {
        return dspOrderId;
    }

    public String getDriverIds() {
        return driverIds;
    }
}
