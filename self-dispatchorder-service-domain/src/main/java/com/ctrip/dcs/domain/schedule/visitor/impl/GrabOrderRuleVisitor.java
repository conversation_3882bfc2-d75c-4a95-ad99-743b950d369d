package com.ctrip.dcs.domain.schedule.visitor.impl;

import com.ctrip.dcs.domain.common.service.QueryDriverLocationService;
import com.ctrip.dcs.domain.common.util.LocalCollectionUtils;
import com.ctrip.dcs.domain.common.value.DriverOrderLocationVO;
import com.ctrip.dcs.domain.schedule.check.CheckContext;
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway;
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 抢单播报检查 查询数据
 */
public class GrabOrderRuleVisitor extends AbstractVisitor{
    /**
     * 查询参数 司机点位 信息
     */
    private List<DriverOrderLocationVO> driverOrderLocationVOS;
    /**
     * 司机端服务
     */
    private DriverDomainServiceGateway gateway;

    public GrabOrderRuleVisitor(List<DriverOrderLocationVO> driverOrderLocationList){
        this.driverOrderLocationVOS = driverOrderLocationList;
        this.gateway = getInstance(DriverDomainServiceGateway.class);
    }

    public GrabOrderRuleVisitor(List<DriverOrderLocationVO> driverOrderLocationVOS, DriverDomainServiceGateway gateway) {
        this.driverOrderLocationVOS = driverOrderLocationVOS;
        this.gateway = gateway;
    }

    @Override
    public void visit(CheckContext context) {
        //查询抢单配置
        queryDriverPushConfig(context);
    }

    /**
     * 查询抢单配置
     * @param context
     */
    private void queryDriverPushConfig(CheckContext context){
        if(LocalCollectionUtils.isEmpty(driverOrderLocationVOS)){
            return;
        }
        List<Long> driverIds = driverOrderLocationVOS.stream()
                .map(DriverOrderLocationVO::getDriverId)
                .filter(id -> !context.getDriverPushConfigMap().containsKey(id))
                .collect(Collectors.toList());
        List<DriverPushConfigVO> driverPushConfigVOList = gateway.query(driverIds);
        if(LocalCollectionUtils.isEmpty(driverPushConfigVOList)){
            return;
        }
        Map<Long,DriverPushConfigVO> configVOMap = new HashMap<>();
        for (DriverPushConfigVO configVO : driverPushConfigVOList) {
            configVOMap.put(configVO.getDriverId(),configVO);
        }
        context.getDriverPushConfigMap().putAll(configVOMap);
    }
}
