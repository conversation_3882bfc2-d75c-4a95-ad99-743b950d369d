package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.service;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;

/**
 * 活跃度特征
 * 评估司机的活跃度水平
 * 
 * <AUTHOR> Assistant
 */
public class ActivityFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        
        // 简化实现：基于司机ID生成模拟活跃度数据
        long driverId = driver.getDriverId();
        double activityScore = 50 + (driverId % 50); // 生成50-99之间的活跃度分数
        
        return activityScore;
    }
    
    @Override
    public String getFeatureName() {
        return "Activity";
    }
    
    @Override
    public String getDescription() {
        return "活跃度特征：评估司机的活跃度水平，活跃度越高得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 0.8;
    }
}
