package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.timespace;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * ETA特征（预估到达时间）
 * 评估司机到达订单起点的预估时间效率
 * 
 * <AUTHOR> Assistant
 */
public class EtaFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        DspOrderVO order = model.getModel().getOrder();
        
        // 获取司机当前位置到订单起点的预估到达时间
        LocalDateTime estimatedArrivalTime = getEstimatedArrivalTime(driver, order, context);
        LocalDateTime orderStartTime = order.estimatedUseTime();
        
        if (estimatedArrivalTime == null || orderStartTime == null) {
            return DEFAULT_VALUE;
        }
        
        // 计算时间差（分钟）
        long timeDifferenceMinutes = ChronoUnit.MINUTES.between(estimatedArrivalTime, orderStartTime);
        
        // 如果司机到达时间晚于订单开始时间，给予惩罚
        if (timeDifferenceMinutes < 0) {
            // 迟到惩罚：每迟到1分钟扣除一定分数
            return Math.max(0, 100 + timeDifferenceMinutes * 2); // 每分钟扣2分
        }
        
        // 如果司机到达时间早于订单开始时间，根据提前时间给分
        // 提前5-15分钟为最佳，给满分
        if (timeDifferenceMinutes >= 5 && timeDifferenceMinutes <= 15) {
            return 100.0;
        }
        
        // 提前时间过短（<5分钟）或过长（>15分钟）都会扣分
        if (timeDifferenceMinutes < 5) {
            return 80.0 + timeDifferenceMinutes * 4; // 提前时间越短扣分越多
        } else {
            // 提前时间过长也会扣分，但扣分较少
            double penalty = (timeDifferenceMinutes - 15) * 0.5;
            return Math.max(60, 100 - penalty);
        }
    }
    
    /**
     * 获取司机到达订单起点的预估时间
     * 
     * @param driver 司机信息
     * @param order 订单信息
     * @param context 排序上下文
     * @return 预估到达时间
     */
    private LocalDateTime getEstimatedArrivalTime(DriverVO driver, DspOrderVO order, SortContext context) {
        // 这里需要调用路径规划服务获取实际的ETA
        // 暂时使用简化逻辑：基于距离和平均速度估算
        
        // 获取司机当前位置和订单起点位置
        // 实际实现中需要从driver和order中获取经纬度信息
        
        // 简化计算：假设平均速度30km/h，根据距离估算时间
        double distanceKm = getDistanceKm(driver, order, context);
        double averageSpeedKmh = getConfigDouble(context, "eta.average.speed", 30.0);
        
        // 计算预估行驶时间（小时）
        double estimatedHours = distanceKm / averageSpeedKmh;
        
        // 当前时间加上预估行驶时间
        LocalDateTime now = LocalDateTime.now();
        return now.plusMinutes((long) (estimatedHours * 60));
    }
    
    /**
     * 计算司机当前位置到订单起点的距离
     * 
     * @param driver 司机信息
     * @param order 订单信息
     * @param context 排序上下文
     * @return 距离（公里）
     */
    private double getDistanceKm(DriverVO driver, DspOrderVO order, SortContext context) {
        // 实际实现中需要调用地图服务计算实际距离
        // 这里使用简化逻辑
        
        // 可以从现有的空驶距离数据中获取
        var relateOrder = context.getDriverRelateOrder(driver.getDriverId());
        if (relateOrder != null && relateOrder.getFrowardEmptyDrivingInfo() != null) {
            Double emptyDistance = relateOrder.getFrowardEmptyDrivingInfo().getEmptyDistance();
            return safeGetDouble(emptyDistance, 10.0); // 默认10公里
        }
        
        return 10.0; // 默认距离
    }
    
    @Override
    public String getFeatureName() {
        return "ETA";
    }
    
    @Override
    public String getDescription() {
        return "ETA特征：评估司机到达订单起点的预估时间效率，提前5-15分钟到达为最佳";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，值越大得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 1.2; // ETA是时空效率的重要指标，给予较高权重
    }
}
