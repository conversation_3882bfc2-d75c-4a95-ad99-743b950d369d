package com.ctrip.dcs.domain.common.constants;

/**
 * <AUTHOR>
 */
public interface EventConstants {

    /**
     * QMQ幂等器
     */
    String IDEMPOTENT_CHECKER = "redisIdempotentChecker";

    String CONSUMER_GROUP = "100041593";
    /**
     * 创建更换司机记录消费组
     */
    String DRIVER_CHANGE_RECORD_CONSUMER_GROUP = "100041593-changeDriverRecord";

    /**
     * 创建推送计费中心消费组
     */
    String PUSH_SETTLEMENT_CONSUMER_GROUP = "100041593-pushSettlement";

    Integer QMQ_RETRY_TIMES = 3;

    /**
     * 下单成功
     */
    String DSP_ORDER_BOOK_TOPIC = "dcs.self.dispatchorder.create";

    /**
     * 派发单取消
     */
    String DSP_ORDER_CANCEL_TOPIC = "dcs.self.dispatchorder.cancel";

    String OLD_DSP_ORDER_CANCEL_TOPIC = "car.qbest.order.orderstate.SUPPLY_ORDER_CANCEL";


    /**
     * 派发单改派
     */
    String DSP_ORDER_REDISPATCH_SUBMIT_TOPIC = "dcs.self.dispatchorder.redispatch.submit";

    /**
     * 判罚通知
     */
    String DSP_ORDER_PUNISH_NOTICE_TOPIC = "dcs.self.dispatchorder.punish.notice";

    /**
     * 车辆故障改派
     */
    String DSP_ORDER_CARFAULTFREEZE_DISPATCH_TOPIC = "dcs.self.dispatchorder.carfaultfreeze.dispatch";

    /**
     * 派发单完成
     */
    String DSP_ORDER_DONE_TOPIC = "dcs.self.dispatchorder.done";
    /**
     * 司机单完成
     */
    String DRIVER_ORDER_FINISH_TOPIC="dcs.self.driver.order.finale";

    /**
     * 司机单Sop消息-非携程订单发送该消息
     */
    String DRIVER_ORDER_SOP_EVENT_SUBJECT="dcs.driver.order.sop.event";


    /**
     * 司机单取消
     */
    String DRIVER_ORDER_CANCEL_TOPIC="dcs.self.driver.order.canceled";

    /**
     * 服务商确认
     */
    String DSP_ORDER_SERVICE_PROVIDER_CONFIRM_TOPIC = "dcs.self.dispatchorder.service.provider.confirm";

    /**
     * 调度确认
     */
    String DSP_ORDER_DISPATCHER_CONFIRM_TOPIC = "dcs.self.dispatchorder.dispatcher.confirm";

    /**
     * 司机确认
     */
    String DSP_ORDER_DRIVER_CONFIRM_TOPIC = "dcs.self.dispatchorder.driver.confirm";

    /**
     * 司机车辆确认
     */
    String DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC = "dcs.self.dispatchorder.drivercar.confirm";

    /**
     * 司机单单状态延迟检查
     */
    String DRIVER_ORDER_STATUS_DELAY_CHECK_TOPIC = "dcs.self.dispatchorder.driverorder.status.delay.check";

    String DSP_ORDER_DRIVER_CAR_CONFIRM_SYNC_PURCHASE = "dcs.self.dispatchorder.drivercar.confirm.sync.purchase";

    String SAAS_DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC = "dcs.saas.dispatchorder.drivercar.confirm";
    
    /**
     * IVR重试消息
     */
    String IVR_RETRY = "dcs.self.dispatchorder.ivr.retry";

    String CREATED_SCHEDULE_TOPIC = "dcs.dsp.dsp.order.schedule.created";

    String EXECUTED_SCHEDULE_TOPIC = "dcs.dsp.dsp.order.schedule.executed";

    String WAIT_EXECUTED_TASK_TOPIC = "dcs.dsp.dsp.order.schedule.task.wait.executed";

    String WAIT_EXECUTED_DISPATCH_TASK_TOPIC = "dcs.dsp.dsp.order.schedule.dispatch.task.wait.executed";

    String COMPLETE_TASK_TOPIC = "dcs.dsp.dsp.order.schedule.task.complete";

    /**
     * 系统播报消息
     */
    String BROADCAST_TOPIC = "dcs.dsp.dsp.order.crtip.push_order_to_drivers";

    /**
     * 抢单大厅消息
     */
    String GRAB_CENTRE_TOPIC = "dcs.dsp.grab.order.crtip.push_order_to_drivers";

    /**
     * 司导抢单大厅消息
     */
    String GUIDE_GRAB_CENTRE_TOPIC = "dcs.grab.order.guide.push";

    /**
     * 司导播报消息
     */
    String GUIDE_BROADCAST_TOPIC = "dcs.grab.order.guide.broadcast";

    /**
     * 待抢订单失效
     */
    String EXPIRE_GRAB_ORDER_TOPIC = "dcs.dsp.grab.order.expire";

    /**
     * 司机抢单消息
     */
    String SUBMIT_GRAB_ORDER_TOPIC = "dcs.driver.graborder.submit";

    /**
     * 包车司导抢单消息
     */
    String SUBMIT_GUIDE_GRAB_ORDER_TOPIC = "dcs.grab.order.guide.submit";

    /**
     * 司机抢单轮选消息
     */
    String SELECT_GRAB_ORDER_TOPIC = "dcs.dsp.graborder.select";

    /**
     * 司机抢单成功
     */
    String DRIVER_GRAB_ORDER_SUCCESS_TOPIC = "dcs.dsp.drivselect.order.grab_order_success";

    /**
     * 司机抢单失败
     */
    String DRIVER_GRAB_ORDER_FAIL_TOPIC = "dcs.dsp.drivselect.order.grab_order_fail";

    /**
     * 司导抢单失败
     */
    String GUIDE_GRAB_ORDER_FAIL_TOPIC = "dcs.grab.order.guide.fail";

    /**
     * vbk工作台日志（C侧消费）
     */
    String DCS_ORDER_WORK_BENCH_LOG_CREATED = "dcs.order.work.bench.log.created";

    /**
     * 司机单应单失败-废弃
     */
    String DRIVER_ORDER_CONFIRM_FAIL_TOPIC = "dcs.dsp.dsp.order.schedule.driver.confirm.fail";

    /**
     * 司机单应单失败
     */
    String DRIVER_ORDER_CONFIRM_FAIL_CANCEL_TOPIC = "dcs.self.dsp.order.schedule.driver.confirm.fail.cancel";
    /**
     * 通知司机单取消车辆确认（本地数据库更新失败的情况使用）
     * */
    String DRIVER_ORDER_CONFIRM_ROLLBACK_TOPIC = "dcs.dsp.driver.order.confirm.car.rollback";

    String DISPATCHER_OVERTIME_REMIND_TOPIC = "dcs.dsp.order.dispatcher.overtime.remind";

    //ivr回调处理
    String IVR_CALLBACK = "bbz.outbound.result.car.timeoutnotassign";

    //ivr回调消息通知-境外
    String IVR_OUT_MSG_NOTICE = "bbz.implus.outbound.result.data.to.domesticcar";
    /**
     * 司机请假
     */
    String DRIVER_LEAVE_TOPIC = "dcs.tms.transport.driver.leave.confirmed";
    /**
     * 司机状态变更 冻结 下线
     */
    String DRIVER_STATUS_MODIFY_TOPIC = "dcs.tms.transport.driver.state.changed";
    /**
     * 司机车辆信息变更
     */
    String DRIVER_VEHICLE_MODIFY_TOPIC = "dcs.tms.transport.driver.vehicle.changed";
    /**
     * 司机信息变更
     */
    String DRIVER_MODIFY_TOPIC="dcs.tms.transport.driver.info.changed";

    /**
     * 超时未派遣，app语音播报消息
     */
    String QMQ_TIMEOUT_NO_DISPATCH_PUSH ="dcs.dsp.order.timeout.no.dispatch.push";


    /**
     * 超时未派遣，app语音播报消息
     */
    String SUPPLY_ORDER_REDISPATCH_CANCEL ="dcs.purchase.supplyorder.selforder.redispatch.canceled";

    /**
     * 调度抢单创建
     */
    String CREATED_DISPATCHER_GRAB_ORDER_TOPIC = "dcs.dsp.dispatch.grab.order.created";

    /**
     * 司机订单预估用车时间变更
     */
    String DRIVER_ORDER_ESTIMATE_TIME_CHANGE = "dcs.self.driver.order.estimate.time.change";

    /**
     * Q侧预估用车时间变更MQ
     */
    String QMQ_ORDER_SYS_EXPECT_BOOK_TIME_CHANGE = "car.qbest.order.SYS_EXPECT_BOOK_TIME_CHANGE";

    /**
     * 司机使用请假权益
     */
    String DRIVER_RIGHTS_VACATION_TOPIC = "dcs.driver.rights.vacation";

    /**
     * 司机请假接力
     */
    String DRIVER_LEAVE_CONTINUE_TOPIC = "dcs.self.dsp.order.driver.leave.continue";
    
    String EMPTY_AND_DUR_TOPIC = "car.qbest.drivinventory.snapshot";

    /**
     * 急单IVR消息创建
     */
    String DCS_DSP_DISPATCH_GRAB_ORDER_IVR_CREATE ="dcs.dsp.dispatch.grab.order.ivr.create";

    //司机接单，司机车辆已确认消息
    String CAR_QBEST_ORDER_ORDERSTATE_ORDER_TAKEN = "car.qbest.order.orderstate.ORDER_TAKEN";
    /**
     * 老单用户订单取消
     */
    String CAR_QBEST_ORDER_ORDERSTATE_ORDER_CANCEL = "car.qbest.order.orderstate.ORDER_CANCEL";

    String CAR_QBEST_ORDER_ORDERSTATE_ORDER_DISPATCHER_CONFIRM = "car.qbest.order.orderstate.DISPATCHER_CONFIRM_MSG";
    /**
     * 应单消息主题
     */
    String DCS_SUPPLYORDER_CONFIRM_EVENT = "dcs.purchase.supplyorder.confirmed";

    /**
     * 司机单司机信息变更
     */
    String DRIVER_ORDER_DRIVER_INFO_MODIFY = "dcs.self.driver.order.driver.info.modify";
    /**
     * 司机单司机信息变更
     */
    String DRIVER_ORDER_VEHICLE_INFO_MODIFY = "dcs.self.driver.order.vehicle.info.modify";

    /**
     * 	im通知
     */
    String ORDER_EVENT_IM_TOPIC = "dcs.self.dispatchorder.event.im.notice";

    /**
     * 司导平台供应链：司导基础信息变更消息
     */
    String TOUR_DRIVER_PLATFORM_DRIVER_INFO_CHANGED = "tour.driver.platform.driver.info.changed";

    /**
     * 司导平台供应链：司导状态变化消息
     */
    String TOUR_DRIVER_PLATFORM_DRIVER_STATUS_CHANGED = "tour.driver.platform.driver.status.changed";

    /**
     * 司导平台供应链：司导关联的车辆的信息发生变化
     */
    String TOUR_DRIVER_PLATFORM_VEHICLE_INFO_CHANGED = "tour.driver.platform.vehicle.info.changed";

    /**
     * 创建抢单任务，构建抢单调度
     */
    String VBK_GRAB_DSP_TASK_TOPIC = "dcs.dsp.dispatch.vbk.grab.dsp.task";

    /**
     * 定时任务取消抢单中的任务
     */
    String VBK_GRAB_ORDER_CANCEL_TASK_BY_SCHEDULER_TOPIC = "dcs.dsp.dispatch.vbk.grab.order.cancel.by.scheduler.task";

    String VBK_GRAB_TASK_FINISH_BY_SCHEDULER_TOPIC = "dcs.dsp.dispatch.vbk.grab.task.finish.by.scheduler.task";

    /**
     * 中止调度任务
     */
    String SHUT_DOWN_GRAB_SCHEDULE_TOPIC = "dcs.dsp.dispatch.shut.down.grab.schedule.task";
    /**
     * 操作记录
     */
    String VBK_GRAB_TASK_OPERATE_TOPIC = "dcs.dsp.dispatch.vbk.grab.task.operate.task";
    /**
     * 无待抢订单触发任务完成
     */
    String VBK_GRAB_FINISH_TASK_OPERATE_TOPIC = "dcs.dsp.dispatch.vbk.grab.finish.task";
    /**
     * 创建抢单任务时取消其他任务中重复的抢单中的订单
     */
    String CANCEL_ORDER_IN_OTHER_TASK_TOPIC = "dcs.dsp.dispatch.cancel.order.in.other.task";

    /**
     * 保存司机结算标识完成
     */
    String COMPLETE_SETTLE_TO_DRIVER_TOPIC = "dcs.dsp.dsp.order.settle_to_driver.complete";

    String BIND_CAR_AND_TAKEN_TOPIC = "dcs.dsp.dsp.order.bind_car_and_taken";
    /**
     * 司机出现迟到风险消息
     */
    String DRIVER_ORDER_ARISE_LATE_RISK = "dcs.self.driver.order.arise.late.risk";
    /**
     * 老单 司机出现迟到风险消息
     */
    String OLD_DRIVER_ORDER_ARISE_LATE_RISK = "car.qbest.order.ARISE_LATE_RISK";

    /**
     * 线路包车订单，修改通知MQ:100041593生产，100041595消费
     */
    String MODIFY_CHARTERED_LINE_ORDER_TOPIC = "dcs.dsp.modify.chartered.lineOrder";

    /**
     * 线路包车派发订单变更通知，修改通知MQ:100041593生产，100041593消费
     */
    String CHANGE_CHARTERED_LINE_DSP_ORDER_TOPIC = "dcs.dsp.order.chartered.line.order.change";

    /**
     * 线路包车订单，支付状态变更MQ：100041593生产，结算消费
     */
    String MODIFY_CHARTERED_LINE_ORDER_PRICE_STATUS_TOPIC = "dcs.self.chartered.lineOrder.order.amount.change";

    /**
     * 用户支付款MQ
     */
    String USER_ORDER_PAYMENT_EVENT = "igt.payment.event.changed";

    String CAR_QBEST_ORDER_ORDERSTATE_ORDER_FINISH = "car.qbest.order.orderstate.SERVICE_FINISH";


    /**
     * 抢单映射创建
     */
    String CREATE_GRAB_DRIVER_ORDER_INDEX = "dcs.dsp.dsp.order.grab.driver.index.create";

    /**
     * 发单规则变更
     */
    String CHANGE_GRAB_PUSH_RULE = "dcs.dsp.dsp.order.grab.push.rule.change";

    /**
     * 司机抢单配置变更
     */
    String CHANGE_DRIVER_PUSH_CONFIG_TOPIC = "dcs.driver.graborder.config.change";

    /**
     * 司导抢单配置变更
     */
    String CHANGE_GUIDE_PUSH_CONFIG_TOPIC = "tour.driver.guide.graborder.config.change";

    /**
     * 司机抢单配置变更
     * 100041952消息，更新抢单信息到ES
     */
    String CHANGE_GRAB_DSP_ORDER_SNAPSHOT = "dcs.dsp.order.grab.snapshot.change";

    /**
     * 航变保护消息处理
     */
    String DCS_FLIGHT_CHG_PROTECT_DEAL = "dcs.flight.chg.protect.deal";

    String DCS_DSP_ORDER_REWARD_STRATEGY_ACTIVE = "dcs.dsp.order.reward.strategy.active";

    String DCS_DSP_ORDER_BROADCAST_PUSH_EXECUTE = "dcs.dsp.order.broadcast.push.execute";

    String DCS_DSP_ORDER_GRAB_CENTRE_PUSH_EXECUTE = "dcs.dsp.order.grab.centre.push.execute";
    /**
     * 数据修改binlog
     */
    String DCS_DCS_SELF_DISPATCH_DB_BINLOG = "dcs.dcsselfdispatchdb.binlog";

    String DCS_DSP_ORDER_NO_COMMISSION_AMOUNT_COMPENSATION = "dcs.dsp.order.no.commission.amount.compensation";

    //用车时间变更
    String DCS_PURCHASE_DISPATCH_USETIME_CHANGE = "dcs.purchase.dispatch.usetime.change";
    /**
     * 冲突改派
     */
    String QMQ_SUBJECT_CONFLICT_REDISPATCH  ="dcs.self.order.conflict.redispatch";
    /**
     * 冲突改派结果通知
     */
    String QMQ_SUBJECT_CONFLICT_REDISPATCH_RESULT  ="dcs.self.order.conflict.redispatch.result";

    /**
     * 原单修改派发单取消延迟检查处理
     */
    String ORI_MODIFY_ORDER_CANCEL_DELAY_CHECK_TOPIC = "dcs.self.dispatchorder.orimodify.cancel.delay.check";
}
