package com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl;

import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Z-Score标准化策略
 * 将数据转化为均值为0、标准差为1的分布
 * 公式：x' = (x - μ) / σ
 * 
 * <AUTHOR> Assistant
 */
public class ZScoreNormalizer implements NormalizationStrategy {
    
    private final boolean positive;
    private final boolean mapToZeroOne; // 是否将结果映射到[0,1]区间
    
    /**
     * 构造函数
     * 
     * @param positive true表示正向归一化，false表示逆向归一化
     * @param mapToZeroOne 是否将结果映射到[0,1]区间
     */
    public ZScoreNormalizer(boolean positive, boolean mapToZeroOne) {
        this.positive = positive;
        this.mapToZeroOne = mapToZeroOne;
    }
    
    /**
     * 默认构造函数，使用正向归一化，不映射到[0,1]区间
     */
    public ZScoreNormalizer() {
        this(true, false);
    }
    
    /**
     * 构造函数，指定归一化方向
     * 
     * @param positive true表示正向归一化，false表示逆向归一化
     */
    public ZScoreNormalizer(boolean positive) {
        this(positive, false);
    }
    
    @Override
    public List<Double> normalize(List<Double> values) {
        if (values == null || values.isEmpty()) {
            return Collections.emptyList();
        }
        
        if (values.size() == 1) {
            return Collections.singletonList(mapToZeroOne ? 0.5 : 0.0);
        }
        
        // 计算均值
        double sum = values.stream().mapToDouble(Double::doubleValue).sum();
        double mean = sum / values.size();
        
        // 计算标准差
        double variance = values.stream()
                .mapToDouble(v -> Math.pow(v - mean, 2))
                .sum() / values.size();
        double stdDev = Math.sqrt(variance);
        
        List<Double> normalized = new ArrayList<>();
        for (Double value : values) {
            normalized.add(normalize(value, 0, 0, mean, stdDev));
        }
        
        // 如果需要映射到[0,1]区间，使用Min-Max归一化
        if (mapToZeroOne && !normalized.isEmpty()) {
            double min = Collections.min(normalized);
            double max = Collections.max(normalized);
            
            if (max != min) {
                for (int i = 0; i < normalized.size(); i++) {
                    double value = normalized.get(i);
                    double mappedValue = (value - min) / (max - min);
                    if (!positive) {
                        mappedValue = 1.0 - mappedValue;
                    }
                    normalized.set(i, mappedValue);
                }
            }
        }
        
        return normalized;
    }
    
    @Override
    public double normalize(double value, double min, double max, Double mean, Double stdDev) {
        if (mean == null || stdDev == null || stdDev == 0) {
            return mapToZeroOne ? 0.5 : 0.0;
        }
        
        // Z-Score标准化
        double zScore = (value - mean) / stdDev;
        
        // 如果是逆向归一化，则取反
        if (!positive) {
            zScore = -zScore;
        }
        
        // 如果需要映射到[0,1]区间，使用Sigmoid函数
        if (mapToZeroOne) {
            return 1.0 / (1.0 + Math.exp(-zScore));
        }
        
        return zScore;
    }
    
    @Override
    public String getStrategyName() {
        String baseName = "ZScore";
        if (mapToZeroOne) {
            baseName += "_Mapped";
        }
        return positive ? baseName : baseName + "_Inverse";
    }
    
    @Override
    public String getDescription() {
        String baseDesc = "Z-Score标准化：将数据转化为均值为0、标准差为1的分布";
        if (mapToZeroOne) {
            baseDesc += "，并映射到[0,1]区间";
        }
        return positive ? 
            baseDesc + "，值越大归一化后越大" :
            baseDesc + "，值越大归一化后越小";
    }
    
    @Override
    public boolean isPositive() {
        return positive;
    }
    
    /**
     * 是否映射到[0,1]区间
     * 
     * @return 是否映射
     */
    public boolean isMapToZeroOne() {
        return mapToZeroOne;
    }
}
