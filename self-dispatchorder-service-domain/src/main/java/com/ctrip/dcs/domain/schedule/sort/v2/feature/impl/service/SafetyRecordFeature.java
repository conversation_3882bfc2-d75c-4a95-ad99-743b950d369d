package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.service;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;

/**
 * 安全记录特征
 * 评估司机的安全记录
 * 
 * <AUTHOR> Assistant
 */
public class SafetyRecordFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        
        // 简化实现：基于司机ID生成模拟安全记录数据
        long driverId = driver.getDriverId();
        double safetyScore = 70 + (driverId % 30); // 生成70-99之间的安全分数
        
        return safetyScore;
    }
    
    @Override
    public String getFeatureName() {
        return "SafetyRecord";
    }
    
    @Override
    public String getDescription() {
        return "安全记录特征：评估司机的安全记录，安全记录越好得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 1.5; // 安全是最重要的指标
    }
}
