package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.matching;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;

/**
 * 顺路性特征
 * 评估订单与司机当前路线的顺路程度
 * 
 * <AUTHOR> Assistant
 */
public class RouteCompatibilityFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        DspOrderVO order = model.getModel().getOrder();
        
        // 简化实现：基于地理位置计算顺路性
        double compatibility = calculateRouteCompatibility(driver, order, context);
        
        return compatibility * 100;
    }
    
    /**
     * 计算路线兼容性
     * 
     * @param driver 司机信息
     * @param order 订单信息
     * @param context 排序上下文
     * @return 兼容性 (0-1)
     */
    private double calculateRouteCompatibility(DriverVO driver, DspOrderVO order, SortContext context) {
        // 实际实现中需要考虑：
        // 1. 司机当前位置和目标位置
        // 2. 订单起点和终点
        // 3. 路线重叠度
        // 4. 绕路距离和时间
        
        // 简化实现：基于司机和订单的ID生成模拟兼容性
        long driverId = driver.getDriverId();
        String orderId = order.getDspOrderId();
        
        // 使用哈希值生成0-1之间的兼容性分数
        int hash = (driverId + orderId.hashCode()) % 100;
        return (50 + hash % 50) / 100.0; // 生成0.5-0.99之间的兼容性
    }
    
    @Override
    public String getFeatureName() {
        return "RouteCompatibility";
    }
    
    @Override
    public String getDescription() {
        return "顺路性特征：评估订单与司机当前路线的顺路程度，顺路性越高得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 1.0;
    }
}
