package com.ctrip.dcs.domain.schedule.sort.feature.impl;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.*;
import com.ctrip.dcs.domain.schedule.value.CityPointVO;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;
import com.ctrip.dcs.domain.schedule.visitor.impl.CityPointsVisitor;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverPointsVisitor;

import java.util.List;
import java.util.Optional;

/**
 * DRIVER_POINT_RATIO_FEATURE
 */
@FeatureItem(id = FeatureItemId.F2)
public class DriverPointRatioFeature extends AbstractFeature {

    @Override
    public void load(List<SortModel> models, SortContext context) {
        Integer cityId = context.getDspOrder().getCityId();
        List<DriverVO> drivers = getDrivers(models);
        context.accept(new DriverPointsVisitor(drivers), new CityPointsVisitor(cityId));
    }

    @Override
    protected Value value(SortModel model, SortContext context) {
        double value = 0.0;
        Optional<DriverPointsVO> driverPointsInfo = context.getDriverPoints(model.getModel().getDriver().getDriverId());
        Optional<CityPointVO> cityPoints = context.getCityPoints(model.getModel().getOrder().getCityId());
        double max = Integer.MAX_VALUE;
        Double driverPoints = driverPointsInfo.map(DriverPointsVO::getPoints).orElse(ZERO_VALUE);
        if(!cityPoints.isPresent()){
            max = Math.max(max,driverPoints);
        }else {
            max = cityPoints.map(CityPointVO::getMaxPoints).orElse(ZERO_VALUE);
        }
        if (max > 0) {
            value = Math.max(driverPoints /max, 0.0);
        }
        return new Value(FeatureItemId.F2.name(), value);
    }

    @Override
    protected Normalizer normalizer() {
        return Normalizer.NORMALIZE;
    }
}
