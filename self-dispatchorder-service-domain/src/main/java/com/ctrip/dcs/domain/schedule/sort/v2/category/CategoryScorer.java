package com.ctrip.dcs.domain.schedule.sort.v2.category;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;

import java.util.List;
import java.util.Map;

/**
 * 大类别评分器接口
 * 负责计算某个大类别的综合得分
 * 
 * <AUTHOR> Assistant
 */
public interface CategoryScorer {
    
    /**
     * 计算大类别得分
     * 
     * @param models 待评分的司机模型列表
     * @param context 排序上下文
     * @return 大类别得分映射，key为司机ID，value为得分(0-100)
     */
    Map<Long, Double> calculateCategoryScore(List<SortModel> models, SortContext context);
    
    /**
     * 获取大类别名称
     * 
     * @return 大类别名称
     */
    String getCategoryName();
    
    /**
     * 获取大类别描述
     * 
     * @return 大类别描述
     */
    String getDescription();
    
    /**
     * 获取该大类别包含的特征数量
     * 
     * @return 特征数量
     */
    int getFeatureCount();
    
    /**
     * 是否启用该大类别
     * 
     * @param context 排序上下文
     * @return 是否启用
     */
    boolean isEnabled(SortContext context);
}
