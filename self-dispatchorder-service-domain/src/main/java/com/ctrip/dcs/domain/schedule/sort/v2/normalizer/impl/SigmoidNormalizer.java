package com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl;

import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Sigmoid归一化策略
 * 使用Sigmoid函数进行归一化，输出范围为(0,1)
 * 公式：x' = 1 / (1 + e^(-x))
 * 
 * <AUTHOR> Assistant
 */
public class SigmoidNormalizer implements NormalizationStrategy {
    
    private final boolean positive;
    private final double scale; // 缩放因子，控制Sigmoid函数的陡峭程度
    
    /**
     * 构造函数
     * 
     * @param positive true表示正向归一化，false表示逆向归一化
     * @param scale 缩放因子，默认为1.0
     */
    public SigmoidNormalizer(boolean positive, double scale) {
        this.positive = positive;
        this.scale = scale;
    }
    
    /**
     * 默认构造函数，使用正向归一化和默认缩放因子
     */
    public SigmoidNormalizer() {
        this(true, 1.0);
    }
    
    /**
     * 构造函数，指定归一化方向
     * 
     * @param positive true表示正向归一化，false表示逆向归一化
     */
    public SigmoidNormalizer(boolean positive) {
        this(positive, 1.0);
    }
    
    @Override
    public List<Double> normalize(List<Double> values) {
        if (values == null || values.isEmpty()) {
            return Collections.emptyList();
        }
        
        if (values.size() == 1) {
            return Collections.singletonList(0.5); // Sigmoid(0) = 0.5
        }
        
        // 计算均值和标准差用于标准化
        double sum = values.stream().mapToDouble(Double::doubleValue).sum();
        double mean = sum / values.size();
        
        double variance = values.stream()
                .mapToDouble(v -> Math.pow(v - mean, 2))
                .sum() / values.size();
        double stdDev = Math.sqrt(variance);
        
        List<Double> normalized = new ArrayList<>();
        for (Double value : values) {
            normalized.add(normalize(value, 0, 0, mean, stdDev));
        }
        
        return normalized;
    }
    
    @Override
    public double normalize(double value, double min, double max, Double mean, Double stdDev) {
        // 如果没有提供均值和标准差，使用简单的Sigmoid
        double input;
        if (mean != null && stdDev != null && stdDev > 0) {
            // 先进行Z-score标准化，再应用Sigmoid
            input = (value - mean) / stdDev * scale;
        } else {
            // 直接使用原值
            input = value * scale;
        }
        
        double sigmoid = 1.0 / (1.0 + Math.exp(-input));
        
        // 如果是逆向归一化，则取反
        if (!positive) {
            sigmoid = 1.0 - sigmoid;
        }
        
        return sigmoid;
    }
    
    @Override
    public String getStrategyName() {
        return positive ? "Sigmoid" : "Sigmoid_Inverse";
    }
    
    @Override
    public String getDescription() {
        return positive ? 
            "Sigmoid归一化：使用Sigmoid函数将数据映射到(0,1)区间，具有平滑的S型曲线特性" :
            "Sigmoid逆向归一化：使用Sigmoid函数将数据映射到(0,1)区间，值越大归一化后越小";
    }
    
    @Override
    public boolean isPositive() {
        return positive;
    }
    
    /**
     * 获取缩放因子
     * 
     * @return 缩放因子
     */
    public double getScale() {
        return scale;
    }
}
