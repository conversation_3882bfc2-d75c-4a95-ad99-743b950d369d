package com.ctrip.dcs.domain.helper;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;

import java.util.function.Supplier;

public class Cats {

	public static final String PREFIX = "App.";

	public static void runOrCatch(String type, String name, Runnable task) {
		Transaction transaction = Cat.newTransaction(PREFIX + type, name);
		try {
			task.run();
		} catch (Throwable e) {
			transaction.setStatus(e);
		} finally {
			transaction.complete();
		}
	}

	public static void runOrThrow(String type, String name, Runnable task) {
		Transaction transaction = Cat.newTransaction(PREFIX + type, name);
		try {
			task.run();
		} catch (Throwable e) {
			transaction.setStatus(e);
			throw e;
		} finally {
			transaction.complete();
		}
	}

	public static <T> T runOrThrow(String type, String name, Supplier<T> task) {
		Transaction transaction = Cat.newTransaction(PREFIX + type, name);
		T out = null;
		try {
			out = task.get();
		} catch (Throwable e) {
			transaction.setStatus(e);
			throw e;
		} finally {
			transaction.complete();
			transaction.addData("Return", String.valueOf(out));
			return out;
		}
	}

	public static <T> T runOrDefault(String type, String name, Supplier<T> task, T defaultValue) {
		Transaction transaction = Cat.newTransaction(PREFIX + type, name);
		T out = null;
		try {
			out = task.get();
		} catch (Throwable e) {
			transaction.setStatus(e);
			out = defaultValue;
		} finally {
			transaction.complete();
			transaction.addData("Return", String.valueOf(out));
		}
		return out;
	}

}