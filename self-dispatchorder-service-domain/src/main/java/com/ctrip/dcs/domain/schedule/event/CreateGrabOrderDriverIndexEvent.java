package com.ctrip.dcs.domain.schedule.event;

import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.value.MessageEventVO;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public class CreateGrabOrderDriverIndexEvent extends MessageEventVO {

    private String dspOrderId;

    private List<Long> driverIds;

    public CreateGrabOrderDriverIndexEvent(String dspOrderId, List<Long> driverIds) {
        super(EventConstants.CREATE_GRAB_DRIVER_ORDER_INDEX, 0);
        this.dspOrderId = dspOrderId;
        this.driverIds = driverIds;
    }


}
