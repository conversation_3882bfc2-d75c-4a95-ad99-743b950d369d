package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.global;

import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverRelateOrderVisitor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 未来接单能力特征
 * 对应现有的F11特征，评估司机未来的接单能力
 * 
 * <AUTHOR> Assistant
 */
public class FutureOrderCapacityFeature extends AbstractFeatureV2 {
    
    private static final int STANDARD_ORDER_DURATION = 105; // 标准订单时长（分钟）
    private static final int BUFFER_TIME = 60; // 缓冲时间（分钟）
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        DspOrderVO order = model.getModel().getOrder();
        
        // 获取司机相关订单信息
        DriverRelateOrderVO relateOrder = context.getDriverRelateOrder(driver.getDriverId());
        if (relateOrder == null) {
            return DEFAULT_VALUE;
        }
        
        // 计算未来接单能力
        double capacity = calculateFutureCapacity(driver, order, relateOrder);
        
        return capacity;
    }
    
    /**
     * 计算未来接单能力
     * 
     * @param driver 司机信息
     * @param order 当前订单
     * @param relateOrder 相关订单信息
     * @return 未来接单能力
     */
    private double calculateFutureCapacity(DriverVO driver, DspOrderVO order, DriverRelateOrderVO relateOrder) {
        // 获取司机工作时间
        LocalDateTime workStartTime = driver.getWorkTimes() != null ? 
            driver.getWorkTimes().getStartTime() : LocalDateTime.now();
        LocalDateTime workEndTime = driver.getWorkTimes() != null ? 
            driver.getWorkTimes().getEndTime() : LocalDateTime.now().plusHours(8);
        
        // 模拟将待派订单插入到司机接单列表后的时间安排
        LocalDateTime currentTime = workStartTime;
        
        // 如果有前向订单，从前向订单结束时间开始
        if (relateOrder.getFrowardOrderInfo() != null && 
            relateOrder.getFrowardOrderInfo().getPredicServiceStopTime() != null) {
            currentTime = relateOrder.getFrowardOrderInfo().getPredicServiceStopTime();
        }
        
        // 加上当前订单的时间
        LocalDateTime currentOrderEndTime = currentTime.plus(
            safeGetInt(order.getEstimatedMin(), STANDARD_ORDER_DURATION), 
            ChronoUnit.MINUTES
        );
        
        // 计算从当前订单结束到工作结束的剩余时间
        long remainingMinutes = ChronoUnit.MINUTES.between(currentOrderEndTime, workEndTime);
        
        if (remainingMinutes <= 0) {
            return 0.0; // 没有剩余时间
        }
        
        // 计算可以插入的订单数量
        // 每段空驶能插入的订单数量 = max((后向时间-前向时间-1小时) / 105分钟, 0)
        double availableTime = remainingMinutes - BUFFER_TIME; // 减去缓冲时间
        double possibleOrders = Math.max(0, availableTime / STANDARD_ORDER_DURATION);
        
        // 转换为0-100分制
        return Math.min(100.0, possibleOrders * 25); // 假设最多4个订单为满分
    }
    
    @Override
    public void loadData(List<SortModel> models, SortContext context) {
        DspOrderVO order = context.getDspOrder();
        List<DriverVO> drivers = models.stream()
            .map(model -> model.getModel().getDriver())
            .toList();
        
        // 加载司机相关订单数据
        context.accept(new DriverRelateOrderVisitor(order, drivers, 12, true));
    }
    
    @Override
    public String getFeatureName() {
        return "FutureOrderCapacity";
    }
    
    @Override
    public String getDescription() {
        return "未来接单能力特征：评估司机在完成当前订单后的未来接单能力，能力越强得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，能力越强得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 0.4; // 对应原F11的权重
    }
}
