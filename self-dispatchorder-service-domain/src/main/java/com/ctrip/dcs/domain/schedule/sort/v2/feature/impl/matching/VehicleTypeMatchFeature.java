package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.matching;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;

/**
 * 车型匹配度特征
 * 评估司机车型与订单需求的匹配程度
 * 
 * <AUTHOR> Assistant
 */
public class VehicleTypeMatchFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        DspOrderVO order = model.getModel().getOrder();
        
        // 获取司机车型和订单需求车型
        Long driverCarTypeId = driver.getCarTypeId();
        Long orderCarTypeId = order.getCarTypeId();
        
        if (driverCarTypeId == null || orderCarTypeId == null) {
            return 50.0; // 默认中等匹配度
        }
        
        // 完全匹配
        if (driverCarTypeId.equals(orderCarTypeId)) {
            return 100.0;
        }
        
        // 部分匹配（可以根据车型兼容性规则进行更复杂的匹配）
        // 这里简化为基于车型ID的相似度
        double similarity = calculateCarTypeSimilarity(driverCarTypeId, orderCarTypeId);
        return similarity * 100;
    }
    
    /**
     * 计算车型相似度
     * 
     * @param driverCarTypeId 司机车型ID
     * @param orderCarTypeId 订单车型ID
     * @return 相似度 (0-1)
     */
    private double calculateCarTypeSimilarity(Long driverCarTypeId, Long orderCarTypeId) {
        // 简化实现：基于ID差值计算相似度
        long diff = Math.abs(driverCarTypeId - orderCarTypeId);
        
        if (diff == 0) return 1.0;
        if (diff == 1) return 0.8;
        if (diff == 2) return 0.6;
        if (diff <= 5) return 0.4;
        
        return 0.2;
    }
    
    @Override
    public String getFeatureName() {
        return "VehicleTypeMatch";
    }
    
    @Override
    public String getDescription() {
        return "车型匹配度特征：评估司机车型与订单需求的匹配程度，匹配度越高得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 1.5; // 车型匹配是订单匹配的重要指标
    }
}
