package com.ctrip.dcs.domain.schedule.sort.feature.impl;

import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.value.DriverMileageProfitVO;
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.*;
import com.ctrip.dcs.domain.schedule.value.DriverAttendanceVO;
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO;
import com.ctrip.dcs.domain.schedule.visitor.impl.*;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 司机分层
 * GRADIENT_DRIVER_NEW_FEATURE
 * <AUTHOR>
 */
@FeatureItem(id = FeatureItemId.F19)
public class GradientDriverNewFeature extends AbstractFeature {

    private static final int ABSENCE_DAY_NUM = 15;

    // 每小时收入的中位数
    private static final int INCOME_PER_HOUR_MEDIAN = 113;

    // 每小时司机行驶距离的中位数
    private static final int KILO_PER_HOUR_MEDIAN = 40;

    private static final double DEFAULT_LBS_DISTANCE = 35;

    /**
     * 油费
     */
    private static final double OIL_COST = 0.5;

    private static final Logger logger = LoggerFactory.getLogger(GradientDriverNewFeature.class);

    @Override
    public void load(List<SortModel> models, SortContext context) {
        DspOrderVO order = context.getDspOrder();
        List<DriverVO> drivers = getDrivers(models);
        Integer cityId = context.getDspOrder().getCityId();
        context.accept(
                new DriverPointsAndRankVisitor(drivers, cityId.longValue()),
                new DriverRelateOrderVisitor(order, drivers, 12,true),
                new TodayDriverMileageProfitVisitor(order, drivers),
                new ExpectDriverMileageProfitVisitor(order, drivers),
                new DriverOrderListVisitor(order, drivers),
                new DriverAttendanceVisitor(drivers)
        );
    }

    @Override
    public Value value(SortModel model, SortContext context) {
        DspOrderVO order = model.getModel().getOrder();
        DriverVO driver = model.getModel().getDriver();
        Integer baselineDay = context.getProfitBaselineDay(order.getCityId(), driver.getCar().getCarTypeId());
        DriverMileageProfitVO todayProfit = context.getTodayDriverMileageProfit(driver.getDriverId());
        DriverMileageProfitVO expectProfit = context.getExpectDriverMileageProfit(driver.getDriverId());
        DriverRelateOrderVO relateOrder = context.getDriverRelateOrder(driver.getDriverId());
        Map<String,Object> valueDetailMap = new HashMap<>();
        double value = value(order, driver, context, relateOrder, todayProfit, expectProfit,   baselineDay,valueDetailMap);
        return new Value(FeatureItemId.F19.name(), value,valueDetailMap);
    }

    private double value(DspOrderVO order, DriverVO driver, SortContext context, DriverRelateOrderVO relateOrder, DriverMileageProfitVO todayProfit, DriverMileageProfitVO expectProfit,  Integer baselineDay,Map<String,Object> valueDetailMap) {
        double value = 1.0;
        if (Objects.isNull(order)) {
            valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_DEFAULT, value);
            return value;
        }
        //司机分排名是否在配置之前
        Optional<DriverPointsVO> optional = context.getDriverPoints(driver.getDriverId());
        Long driverPointsRank = optional.map(DriverPointsVO::getCityRanking).orElse(0L);
        Integer topN = queryDriverPointRank(context);
        if (driverPointsRank == null || driverPointsRank > topN.longValue()) {
            return value;
        }
        value *= 10;
        Optional<DriverAttendanceVO> attendanceOpt = context.getDriverAttendance(driver.getDriverId());
        // 司机缺勤天数超过阈值
        Double attendance = attendanceOpt.map(DriverAttendanceVO::getAttendance).orElse(1.0D).doubleValue();
        valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_W, attendance);
        Date min = DateUtil.getMinMonthDate();
        long day = DateUtil.getDiffDay(min, new Date()) + 1;
        if (day - attendance > ABSENCE_DAY_NUM) {
            return value;
        }
        value *= 10;
        // highProfitDay
        Double today = 0D;
        if (Objects.nonNull(todayProfit)) {
            if (Objects.isNull(relateOrder)) {
                today = todayProfit.expect(order, DEFAULT_LBS_DISTANCE, DEFAULT_LBS_DISTANCE, DEFAULT_LBS_DISTANCE);
            }else {
                today = todayProfit.expect(order, relateOrder.getFrowardEmptyDistance(), relateOrder.getBackwardEmptyDistance(), relateOrder.getOriginalEmptyDistance());
            }
        }
        valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_TP, today);
        Integer highProfitDay = context.getHighProfitLineDay(order.getCityId().longValue(), driver.getCar().getCarTypeId());
        if (today >= highProfitDay) {
            return value;
        }
        value *= 10;
        if (!virtualProfitOver(order, driver, context, expectProfit, baselineDay,valueDetailMap)) {
            return value;
        }

        value *= 10;
        return value;
    }

    public Integer queryDriverPointRank(SortContext context) {
        String properties = context.getProperties("driver_point_rank_list", StringUtils.EMPTY);
        Integer cityId = context.getDspOrder().getCityId();
        List<String> list = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(properties);
        Map<Integer, Integer> res = new HashMap<>();
        for (String s : list) {
            if (StringUtils.isNotBlank(s) && s.split(":").length == 2) {
                String[] rank = s.split(":");
                res.put(Integer.valueOf(rank[0]), Integer.valueOf(rank[1]));
            }
        }
        return res.get(cityId) == null ? 200 : res.get(cityId);
    }

    public boolean virtualProfitOver(DspOrderVO order, DriverVO driver, SortContext context, DriverMileageProfitVO expectProfit, Integer baselineDay,Map<String,Object> valueDetailMap) {
        List<String> workTime = driver.getWorkTimes().getPeriodStrs();
        if (CollectionUtils.isEmpty(workTime)) {
            return false;
        }
        double actualProfit = 0.0;
        if (expectProfit != null && expectProfit.getProfit() != null) {
            actualProfit = expectProfit.getProfit();
        }
        valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_EP, actualProfit);
        //接单列表
        List<DspOrderVO> orderVOList = context.getOrderList(driver);
        if (CollectionUtils.isEmpty(orderVOList)) {
            double workTimeLength = workTimeLength(workTime) / 60;    // 工作时长 小时
            double virtualProfit = calculateVirtualProfit(workTimeLength);
            return actualProfit + virtualProfit >= baselineDay;
        }
        orderVOList = orderVOList.stream().sorted((Comparator.comparing(DspOrderVO::getEstimatedUseTime))).collect(Collectors.toList());
        String day = DateUtil.formatDate(order.getEstimatedUseTime(), DateUtil.DATE_FORMAT);
        double timeInterval = 0.0;    // 订单间隔 小时
        for (String segment : workTime) {
            if (CollectionUtils.isEmpty(orderVOList)) {
                break;
            }
            List<DspOrderVO> list = Lists.newArrayList();
            List<String> startAndEndWorkTime = Splitter.on("~").splitToList(segment);
            Date startTime = getDate(day, startAndEndWorkTime.get(0));
            Date endTime = getDate(day, startAndEndWorkTime.get(1));
            if (startTime.after(endTime)) { // 跨天
                Date tempBegin = getDate(day, "00:00");
                Date tempEnd = getDate(day, "24:00");
                List<DspOrderVO> l1 = inWorkTimeSegment(orderVOList, startTime, tempEnd);
                timeInterval += calculateTimeInterval(l1, startTime, tempEnd);
                list.addAll(l1);
                List<DspOrderVO> l2 = inWorkTimeSegment(orderVOList, tempBegin, endTime);
                timeInterval += calculateTimeInterval(l2, tempBegin, endTime);
                list.addAll(l2);
            } else {
                list.addAll(inWorkTimeSegment(orderVOList, startTime, endTime));
                timeInterval += calculateTimeInterval(list, startTime, endTime);
            }
            Set<String> set = list.stream().map(DspOrderVO::getDspOrderId).collect(Collectors.toSet());
            orderVOList = orderVOList.stream().filter(o -> !set.contains(o.getDspOrderId())).collect(Collectors.toList());
        }
        // 虚拟收益
        double virtualProfit = calculateVirtualProfit(timeInterval);
        valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_VP, virtualProfit);
        valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_ET, timeInterval);
        return actualProfit + virtualProfit >= baselineDay;
    }

    public double calculateTimeInterval(List<DspOrderVO> orders, Date startTime, Date endTime) {
        if (CollectionUtils.isEmpty(orders)) {
            return (double) DateUtil.getDiffMinute(startTime, endTime) / 60;
        }
        double length = 0.0;
        for (int i = 0; i < orders.size(); i++) {
            if (i == 0) {
                DspOrderVO order = orders.get(i);
                length += Math.max(DateUtil.getDiffMinute(startTime, order.getEstimatedUseTime()), 0.0);
            } else {
                DspOrderVO prev = orders.get(i - 1);
                DspOrderVO after = orders.get(i);
                length += Math.max(DateUtil.getDiffMinute(prev.getPredicServiceStopTime(), after.getEstimatedUseTime()), 0.0);
            }

        }
        DspOrderVO order = orders.get(orders.size() - 1);
        length += Math.max(DateUtil.getDiffMinute(order.getPredicServiceStopTime(), endTime), 0.0);
        return length / 60;
    }

    private Date getDate(String day, String time) {
        return DateUtil.parseDateStr2Date(day + " " + time + ":00");
    }

    private double calculateVirtualProfit(double timeLength) {
        return timeLength * INCOME_PER_HOUR_MEDIAN - timeLength * KILO_PER_HOUR_MEDIAN * OIL_COST;
    }

    public double workTimeLength(List<String> segWorkTimes) {
        if (CollectionUtils.isEmpty(segWorkTimes)) {
            return 0.0;
        }
        double ans = 0.0;
        for (String workTime : segWorkTimes) {
            try {
                List<String> startAndEndWorkTime = Splitter.on("~").splitToList(workTime);
                Date startTime = org.apache.commons.lang3.time.DateUtils.parseDate(startAndEndWorkTime.get(0), "HH:mm");
                Date endTime = org.apache.commons.lang3.time.DateUtils.parseDate(startAndEndWorkTime.get(1), "HH:mm");
                if (startTime.after(endTime)) {
                    endTime = DateUtil.addDays(endTime, 1);
                }
                ans += DateUtil.getDiffMinute(startTime, endTime);
            } catch (ParseException e) {
                logger.error("abnormal working hours of the driver", e);
            }
        }
        return ans;
    }

    public List<DspOrderVO> inWorkTimeSegment(List<DspOrderVO> orders, Date start, Date end) {
        List<DspOrderVO> list = Lists.newArrayList();
        for (DspOrderVO order : orders) {
            if (start.before(order.getEstimatedUseTime()) && end.after(order.getEstimatedUseTime())) {
                list.add(order);
            }
        }
        return list;
    }

    @Override
    public Normalizer normalizer() {
        return Normalizer.EMPTY;
    }
}
