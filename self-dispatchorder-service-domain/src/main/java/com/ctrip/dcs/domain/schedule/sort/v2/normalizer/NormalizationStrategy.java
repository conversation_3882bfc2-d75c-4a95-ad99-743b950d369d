package com.ctrip.dcs.domain.schedule.sort.v2.normalizer;

import java.util.List;

/**
 * 归一化策略接口
 * 定义不同的数据归一化方法
 * 
 * <AUTHOR> Assistant
 */
public interface NormalizationStrategy {
    
    /**
     * 对数据进行归一化处理
     * 
     * @param values 原始数据列表
     * @return 归一化后的数据列表
     */
    List<Double> normalize(List<Double> values);
    
    /**
     * 对单个数值进行归一化处理
     * 需要提供数据的统计信息
     * 
     * @param value 原始数值
     * @param min 最小值
     * @param max 最大值
     * @param mean 平均值（可选，某些策略需要）
     * @param stdDev 标准差（可选，某些策略需要）
     * @return 归一化后的数值
     */
    double normalize(double value, double min, double max, Double mean, Double stdDev);
    
    /**
     * 获取归一化策略的名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 获取归一化策略的描述
     * 
     * @return 策略描述
     */
    String getDescription();
    
    /**
     * 判断是否为正向归一化
     * 正向：值越大，归一化后的值越大
     * 逆向：值越大，归一化后的值越小
     * 
     * @return true表示正向，false表示逆向
     */
    boolean isPositive();
}
