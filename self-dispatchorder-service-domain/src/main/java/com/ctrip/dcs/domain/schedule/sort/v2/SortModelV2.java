package com.ctrip.dcs.domain.schedule.sort.v2;

import com.ctrip.dcs.domain.schedule.sort.v2.grade.Grade;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;

import java.util.HashMap;
import java.util.Map;

/**
 * 排序模型V2
 * 扩展原有排序模型，支持分层评分和等级制度
 * 
 * <AUTHOR> Assistant
 */
public class SortModelV2 {
    
    private final DspModelVO model;
    
    // 各大类别的原始得分 (0-100)
    private final Map<String, Double> categoryScores;
    
    // 各大类别的等级
    private final Map<String, Grade> categoryGrades;
    
    // 加权GPA
    private double weightedGpa;
    
    // 最终排序分数
    private double finalScore;
    
    // 调试信息
    private final Map<String, Object> debugInfo;
    
    public SortModelV2(DspModelVO model) {
        this.model = model;
        this.categoryScores = new HashMap<>();
        this.categoryGrades = new HashMap<>();
        this.debugInfo = new HashMap<>();
        this.weightedGpa = 0.0;
        this.finalScore = 0.0;
    }
    
    /**
     * 设置大类别得分
     * 
     * @param categoryName 大类别名称
     * @param score 得分 (0-100)
     */
    public void setCategoryScore(String categoryName, double score) {
        categoryScores.put(categoryName, score);
        // 自动计算等级
        categoryGrades.put(categoryName, Grade.fromScore(score));
    }
    
    /**
     * 获取大类别得分
     * 
     * @param categoryName 大类别名称
     * @return 得分
     */
    public double getCategoryScore(String categoryName) {
        return categoryScores.getOrDefault(categoryName, 0.0);
    }
    
    /**
     * 获取大类别等级
     * 
     * @param categoryName 大类别名称
     * @return 等级
     */
    public Grade getCategoryGrade(String categoryName) {
        return categoryGrades.getOrDefault(categoryName, Grade.D);
    }
    
    /**
     * 获取所有大类别得分
     * 
     * @return 大类别得分映射
     */
    public Map<String, Double> getAllCategoryScores() {
        return new HashMap<>(categoryScores);
    }
    
    /**
     * 获取所有大类别等级
     * 
     * @return 大类别等级映射
     */
    public Map<String, Grade> getAllCategoryGrades() {
        return new HashMap<>(categoryGrades);
    }
    
    /**
     * 设置加权GPA
     * 
     * @param weightedGpa 加权GPA
     */
    public void setWeightedGpa(double weightedGpa) {
        this.weightedGpa = weightedGpa;
    }
    
    /**
     * 获取加权GPA
     * 
     * @return 加权GPA
     */
    public double getWeightedGpa() {
        return weightedGpa;
    }
    
    /**
     * 设置最终排序分数
     * 
     * @param finalScore 最终分数
     */
    public void setFinalScore(double finalScore) {
        this.finalScore = finalScore;
    }
    
    /**
     * 获取最终排序分数
     * 
     * @return 最终分数
     */
    public double getFinalScore() {
        return finalScore;
    }
    
    /**
     * 获取司机ID
     * 
     * @return 司机ID
     */
    public Long getDriverId() {
        return model.getDriver().getDriverId();
    }
    
    /**
     * 获取原始模型
     * 
     * @return 原始模型
     */
    public DspModelVO getModel() {
        return model;
    }
    
    /**
     * 添加调试信息
     * 
     * @param key 键
     * @param value 值
     */
    public void addDebugInfo(String key, Object value) {
        debugInfo.put(key, value);
    }
    
    /**
     * 获取调试信息
     * 
     * @return 调试信息映射
     */
    public Map<String, Object> getDebugInfo() {
        return new HashMap<>(debugInfo);
    }
    
    /**
     * 检查是否存在D级（短板检查）
     * 
     * @return 是否存在D级
     */
    public boolean hasPoorGrade() {
        return categoryGrades.values().stream().anyMatch(Grade::isPoor);
    }
    
    /**
     * 获取最低等级
     * 
     * @return 最低等级
     */
    public Grade getLowestGrade() {
        return categoryGrades.values().stream()
            .min((g1, g2) -> Integer.compare(g1.getGpaValue(), g2.getGpaValue()))
            .orElse(Grade.D);
    }
    
    /**
     * 获取等级分布统计
     * 
     * @return 等级分布统计
     */
    public Map<Grade, Integer> getGradeDistribution() {
        Map<Grade, Integer> distribution = new HashMap<>();
        for (Grade grade : Grade.values()) {
            distribution.put(grade, 0);
        }
        
        for (Grade grade : categoryGrades.values()) {
            distribution.put(grade, distribution.get(grade) + 1);
        }
        
        return distribution;
    }
    
    @Override
    public String toString() {
        return String.format("SortModelV2{driverId=%d, weightedGpa=%.4f, finalScore=%.4f, grades=%s}",
            getDriverId(), weightedGpa, finalScore, categoryGrades);
    }
}
