package com.ctrip.dcs.domain.schedule.sort.v2.scorer;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.SortModelV2;
import com.ctrip.dcs.domain.schedule.sort.v2.category.CategoryScorer;
import com.ctrip.dcs.domain.schedule.sort.v2.grade.GpaCalculator;
import com.ctrip.dcs.domain.schedule.sort.v2.weight.CategoryWeight;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基于分类的评分器
 * 使用四大类别进行分层评分
 * 
 * <AUTHOR> Assistant
 */
public class CategoryBasedScorer {
    
    private static final Logger logger = LoggerFactory.getLogger(CategoryBasedScorer.class);
    
    private final List<CategoryScorer> categoryScorers;
    private final GpaCalculator gpaCalculator;
    
    public CategoryBasedScorer() {
        this.categoryScorers = new ArrayList<>();
        this.gpaCalculator = new GpaCalculator();
    }
    
    /**
     * 添加大类别评分器
     * 
     * @param categoryScorer 大类别评分器
     */
    public void addCategoryScorer(CategoryScorer categoryScorer) {
        if (categoryScorer != null) {
            categoryScorers.add(categoryScorer);
        }
    }
    
    /**
     * 计算所有司机的分层得分
     * 
     * @param models 司机模型列表
     * @param context 排序上下文
     * @param categoryWeight 大类别权重
     * @return 排序模型V2列表
     */
    public List<SortModelV2> score(List<SortModel> models, SortContext context, CategoryWeight categoryWeight) {
        List<SortModelV2> sortModelsV2 = new ArrayList<>();
        
        // 转换为V2模型
        for (SortModel model : models) {
            sortModelsV2.add(new SortModelV2(model.getModel()));
        }
        
        try {
            // 计算各大类别得分
            calculateCategoryScores(models, sortModelsV2, context);
            
            // 计算GPA和最终得分
            calculateFinalScores(sortModelsV2, categoryWeight);
            
        } catch (Exception e) {
            logger.error("分层评分计算失败", e);
            // 设置默认得分
            for (SortModelV2 modelV2 : sortModelsV2) {
                modelV2.setWeightedGpa(0.0);
                modelV2.setFinalScore(0.0);
            }
        }
        
        return sortModelsV2;
    }
    
    /**
     * 计算各大类别得分
     * 
     * @param originalModels 原始司机模型列表
     * @param sortModelsV2 V2司机模型列表
     * @param context 排序上下文
     */
    private void calculateCategoryScores(List<SortModel> originalModels, List<SortModelV2> sortModelsV2, SortContext context) {
        for (CategoryScorer categoryScorer : categoryScorers) {
            if (!categoryScorer.isEnabled(context)) {
                continue;
            }
            
            try {
                // 计算该大类别的得分
                Map<Long, Double> categoryScores = categoryScorer.calculateCategoryScore(originalModels, context);
                
                // 将得分设置到V2模型中
                for (SortModelV2 modelV2 : sortModelsV2) {
                    Long driverId = modelV2.getDriverId();
                    Double score = categoryScores.getOrDefault(driverId, 0.0);
                    modelV2.setCategoryScore(categoryScorer.getCategoryName(), score);
                    
                    // 添加调试信息
                    modelV2.addDebugInfo(categoryScorer.getCategoryName() + "_score", score);
                    modelV2.addDebugInfo(categoryScorer.getCategoryName() + "_grade", 
                                       modelV2.getCategoryGrade(categoryScorer.getCategoryName()));
                }
                
            } catch (Exception e) {
                logger.warn("大类别评分计算失败", "category=" + categoryScorer.getCategoryName(), e);
                
                // 设置默认得分
                for (SortModelV2 modelV2 : sortModelsV2) {
                    modelV2.setCategoryScore(categoryScorer.getCategoryName(), 0.0);
                }
            }
        }
    }
    
    /**
     * 计算最终得分
     * 
     * @param sortModelsV2 V2司机模型列表
     * @param categoryWeight 大类别权重
     */
    private void calculateFinalScores(List<SortModelV2> sortModelsV2, CategoryWeight categoryWeight) {
        for (SortModelV2 modelV2 : sortModelsV2) {
            try {
                // 计算加权GPA
                double weightedGpa = gpaCalculator.calculateWeightedGpa(
                    modelV2.getAllCategoryGrades(), 
                    categoryWeight
                );
                modelV2.setWeightedGpa(weightedGpa);
                
                // 计算最终得分
                double finalScore = calculateFinalScore(modelV2, categoryWeight);
                modelV2.setFinalScore(finalScore);
                
                // 添加调试信息
                modelV2.addDebugInfo("weighted_gpa", weightedGpa);
                modelV2.addDebugInfo("final_score", finalScore);
                modelV2.addDebugInfo("has_poor_grade", modelV2.hasPoorGrade());
                modelV2.addDebugInfo("lowest_grade", modelV2.getLowestGrade());
                
            } catch (Exception e) {
                logger.warn("最终得分计算失败", "driverId=" + modelV2.getDriverId(), e);
                modelV2.setWeightedGpa(0.0);
                modelV2.setFinalScore(0.0);
            }
        }
    }
    
    /**
     * 计算最终得分
     * 结合GPA和原始分数进行细分
     * 
     * @param modelV2 V2司机模型
     * @param categoryWeight 大类别权重
     * @return 最终得分
     */
    private double calculateFinalScore(SortModelV2 modelV2, CategoryWeight categoryWeight) {
        double weightedGpa = modelV2.getWeightedGpa();
        
        // 基础分数：GPA * 1000（放大到便于排序的范围）
        double baseScore = weightedGpa * 1000;
        
        // 细分分数：使用原始分数的加权平均作为小数部分
        double detailScore = 0.0;
        double totalWeight = 0.0;
        
        Map<String, Double> categoryScores = modelV2.getAllCategoryScores();
        for (Map.Entry<String, Double> entry : categoryScores.entrySet()) {
            String categoryName = entry.getKey();
            Double score = entry.getValue();
            double weight = categoryWeight.getWeight(categoryName);
            
            if (weight > 0 && score != null) {
                detailScore += score * weight;
                totalWeight += weight;
            }
        }
        
        if (totalWeight > 0) {
            detailScore = detailScore / totalWeight / 100; // 归一化到[0,1]
        }
        
        return baseScore + detailScore;
    }
    
    /**
     * 获取大类别评分器数量
     * 
     * @return 评分器数量
     */
    public int getCategoryScorerCount() {
        return categoryScorers.size();
    }
}
