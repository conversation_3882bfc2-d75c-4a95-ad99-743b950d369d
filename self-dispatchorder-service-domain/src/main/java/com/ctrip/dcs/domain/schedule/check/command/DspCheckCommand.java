package com.ctrip.dcs.domain.schedule.check.command;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.schedule.check.CheckConfig;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public class DspCheckCommand {

    private DspOrderVO order;

    private SubSkuVO subSku;

    private List<DriverVO> drivers;

    private DuidVO duid;

    private TransportGroupVO transportGroup;

    private CheckConfig checkConfig;

    public DspCheckCommand(DspOrderVO order, SubSkuVO subSku) {
        Assert.notNull(order);
        Assert.notNull(subSku);
        this.order = order;
        this.subSku = subSku;
        this.drivers = Collections.emptyList();
        this.checkConfig = subSku.getCheck();
    }

    public DspCheckCommand(DspOrderVO order, SubSkuVO subSku, DuidVO duid) {
        Assert.notNull(order);
        Assert.notNull(subSku);
        this.order = order;
        this.subSku = subSku;
        this.drivers = Collections.emptyList();
        this.duid = duid;
        this.checkConfig = subSku.getCheck();
    }

    public DspCheckCommand(DspOrderVO order, SubSkuVO subSku, List<DriverVO> drivers, DuidVO duid) {
        Assert.notNull(order);
        Assert.notNull(subSku);
        this.order = order;
        this.subSku = subSku;
        this.drivers = drivers;
        this.duid = duid;
        this.checkConfig = subSku.getCheck();
    }

    public DspCheckCommand(DspOrderVO order, SubSkuVO subSku, List<DriverVO> drivers, DuidVO duid, CheckConfig checkConfig) {
        Assert.notNull(order);
        Assert.notNull(subSku);
        this.order = order;
        this.subSku = subSku;
        this.drivers = drivers;
        this.duid = duid;
        this.checkConfig = checkConfig;
    }

    public DspCheckCommand(DspOrderVO order, SubSkuVO subSku, List<DriverVO> drivers, TransportGroupVO transportGroup, DuidVO duid) {
        Assert.notNull(order);
        Assert.notNull(subSku);
        this.order = order;
        this.subSku = subSku;
        this.drivers = drivers;
        this.transportGroup = transportGroup;
        this.duid = duid;
        this.checkConfig = subSku.getCheck();
    }
}
