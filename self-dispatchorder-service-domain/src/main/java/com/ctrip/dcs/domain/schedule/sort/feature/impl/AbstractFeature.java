package com.ctrip.dcs.domain.schedule.sort.feature.impl;

import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.*;
import com.ctrip.dcs.domain.schedule.check.CheckItemRecord;
import com.ctrip.dcs.domain.schedule.sort.SortRecord;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.Feature;
import com.ctrip.dcs.domain.schedule.sort.feature.FeatureItem;
import com.ctrip.dcs.domain.schedule.sort.feature.Normalizer;
import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 特征项抽象类
 * <AUTHOR>
 */
public abstract class AbstractFeature implements Feature {

    private static final Logger logger = LoggerFactory.getLogger(AbstractFeature.class);

    protected static final double ZERO_VALUE = 0D;

    protected static double DEFAULT_EMPTY_DIS = 35.0;

    @Override
    public List<Value> value(List<SortModel> models, SortContext context) {
        try {
            // 加载依赖的数据
            load(models, context);
        } catch (Exception e) {
            MetricsUtil.recordValue(MetricsConstants.SORT_ITEM_PROCESS_ERROR_COUNT);
            Map<String, String> map = new HashMap<>();
            logger.warn("sort_item_load_error","sort_item_load_error",e,map);
        }
        List<Value> values = Lists.newArrayList();
        for (SortModel model : models) {
            try {
                // 计算特征值
                Value value = value(model, context);
                values.add(value);
                model.addValue(value);
                // 后处理
                post(model, context);
            } catch (Exception e) {
                MetricsUtil.recordValue(MetricsConstants.SORT_ITEM_PROCESS_ERROR_COUNT);
                Map<String, String> map = new HashMap<>();
                logger.warn("sort_item_process_error","sort_item_process_error",e,map);
            }
        }
        batchPost(models, context);
        // 归一
        normalize(context, values);
        return values;
    }

    /**
     * 后处理
     * @param model
     * @param context
     */
    protected void post(SortModel model, SortContext context) {
        // 检查记录
//        context.record(model);
    }

    /**
     * 批量后处理
     * @param models
     * @param context
     */
    protected void batchPost(List<SortModel> models, SortContext context) {
    }

    /**
     * 加载特征项需要的数据
     * @param models
     * @param context
     */
    protected void load(List<SortModel> models, SortContext context) {

    }

    /**
     * 计算特征值
     * @param model
     * @param context
     * @return
     */
    protected abstract Value value(SortModel model, SortContext context);

    /**
     * 归一器
     * @return
     */
    protected abstract Normalizer normalizer();

    protected void normalize(SortContext context, List<Value> values) {  // NOSONAR
        normalize(values);
    }

    /**
     * 归一
     * @param values
     */
    protected void normalize(List<Value> values) {
        try {
            double max = Integer.MIN_VALUE;
            double min = Integer.MAX_VALUE;
            for (Value value : values) {
                max = Math.max(max, value.getValue());
                min = Math.min(min, value.getValue());
            }
            for (Value value : values) {
                value.normalize(max, min, normalizer());
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }

    protected List<DriverVO> getDrivers(List<SortModel> models) {
        return Optional.ofNullable(models).orElse(Collections.emptyList()).stream().map(SortModel::getModel).map(DspModelVO::getDriver).collect(Collectors.toList());
    }

    protected List<TransportGroupVO> getTransportGroups(List<SortModel> models) {
        return Optional.ofNullable(models).orElse(Collections.emptyList()).stream().map(SortModel::getModel).map(DspModelVO::getTransportGroup).collect(Collectors.toList());
    }

    /**
     * 相邻单特征详情-算法埋点
     * @return
     */
    public static Map<String,Object> getRelateOrderValueDetail(DspOrderVO frowardOrderInfo, DspOrderVO backwardOrderInfo, EmptyDrivingVO frowardEmptyDrivingInfo, EmptyDrivingVO backwardEmptyDrivingInfo, EmptyDrivingVO originalEmptyDrivingInfo) {
        Map<String,Object> valueDetailMap = new HashMap<>();
        // 前向空驶
        Double forwardEmptyDistance = Optional.ofNullable(frowardEmptyDrivingInfo).map(DriverRelateOrderVO::getEmptyDistance).orElse(DEFAULT_EMPTY_DIS);
        // 后向空驶
        Double afterEmptyDistance = Optional.ofNullable(backwardEmptyDrivingInfo).map(DriverRelateOrderVO::getEmptyDistance).orElse(DEFAULT_EMPTY_DIS);
        // 原始空驶
        Double originalEmptyDistance = Optional.ofNullable(originalEmptyDrivingInfo).map(DriverRelateOrderVO::getEmptyDistance).orElse(DEFAULT_EMPTY_DIS);
        if (frowardOrderInfo != null) {
            valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_F_OID, frowardOrderInfo.getDspOrderId());
            if (frowardOrderInfo.getPredicServiceStopTime() != null) {
                valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_PRE_ORDER_STOP_TIME, DateUtil.formatDate(frowardOrderInfo.getPredicServiceStopTime(), DateUtil.DATETIME_FORMAT));
            }
            if (frowardOrderInfo.getEstimatedMin() != null) {
                valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_PRE_ORDER_PREDICT_DURATION, frowardOrderInfo.getEstimatedMin().divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toString());
            }
        }
        if (backwardOrderInfo != null) {
            valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_A_OID, backwardOrderInfo.getDspOrderId());
        }
        valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_F, forwardEmptyDistance);
        valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_A, afterEmptyDistance);
        valueDetailMap.put(SysConstants.SortValKey.SORT_VAL_KEY_O, originalEmptyDistance);
        return valueDetailMap;
    }
}
