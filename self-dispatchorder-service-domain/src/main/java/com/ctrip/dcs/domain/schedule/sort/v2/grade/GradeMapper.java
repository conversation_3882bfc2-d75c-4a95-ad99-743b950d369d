package com.ctrip.dcs.domain.schedule.sort.v2.grade;

import java.util.Map;

/**
 * 等级映射器接口
 * 负责将原始分数映射为等级
 * 
 * <AUTHOR> Assistant
 */
public interface GradeMapper {
    
    /**
     * 将分数映射为等级
     * 
     * @param score 原始分数 (0-100)
     * @return 对应等级
     */
    Grade mapToGrade(double score);
    
    /**
     * 批量映射分数为等级
     * 
     * @param scores 分数映射表，key为标识，value为分数
     * @return 等级映射表，key为标识，value为等级
     */
    Map<String, Grade> mapToGrades(Map<String, Double> scores);
    
    /**
     * 获取等级分布统计
     * 
     * @param scores 分数列表
     * @return 等级分布统计，key为等级，value为数量
     */
    Map<Grade, Integer> getGradeDistribution(double[] scores);
}
