package com.ctrip.dcs.domain.schedule.sort.v2.grade;

/**
 * 司机评分等级枚举
 * 采用A/B/C/D四级制度，每个等级对应不同的GPA分值
 * 
 * <AUTHOR> Assistant
 */
public enum Grade {
    
    /**
     * A级：优秀 (≥85分)
     * GPA分值：4
     */
    A(4, 85, 100, "优秀"),
    
    /**
     * B级：良好 (70-84分)
     * GPA分值：3
     */
    B(3, 70, 84, "良好"),
    
    /**
     * C级：一般 (50-69分)
     * GPA分值：2
     */
    C(2, 50, 69, "一般"),
    
    /**
     * D级：较差 (<50分)
     * GPA分值：1
     */
    D(1, 0, 49, "较差");
    
    private final int gpaValue;
    private final int minScore;
    private final int maxScore;
    private final String description;
    
    Grade(int gpaValue, int minScore, int maxScore, String description) {
        this.gpaValue = gpaValue;
        this.minScore = minScore;
        this.maxScore = maxScore;
        this.description = description;
    }
    
    /**
     * 根据分数获取对应等级
     * 
     * @param score 分数 (0-100)
     * @return 对应等级
     */
    public static Grade fromScore(double score) {
        if (score >= A.minScore) {
            return A;
        } else if (score >= B.minScore) {
            return B;
        } else if (score >= C.minScore) {
            return C;
        } else {
            return D;
        }
    }
    
    /**
     * 获取GPA分值
     * 
     * @return GPA分值
     */
    public int getGpaValue() {
        return gpaValue;
    }
    
    /**
     * 获取最小分数
     * 
     * @return 最小分数
     */
    public int getMinScore() {
        return minScore;
    }
    
    /**
     * 获取最大分数
     * 
     * @return 最大分数
     */
    public int getMaxScore() {
        return maxScore;
    }
    
    /**
     * 获取等级描述
     * 
     * @return 等级描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 判断是否为优秀等级
     * 
     * @return 是否为A级
     */
    public boolean isExcellent() {
        return this == A;
    }
    
    /**
     * 判断是否为良好及以上等级
     * 
     * @return 是否为B级及以上
     */
    public boolean isGoodOrAbove() {
        return this == A || this == B;
    }
    
    /**
     * 判断是否为较差等级
     * 
     * @return 是否为D级
     */
    public boolean isPoor() {
        return this == D;
    }
}
