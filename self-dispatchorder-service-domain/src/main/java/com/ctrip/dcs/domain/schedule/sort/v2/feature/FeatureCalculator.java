package com.ctrip.dcs.domain.schedule.sort.v2.feature;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;

import java.util.List;
import java.util.Map;

/**
 * 特征计算器接口
 * 定义特征计算的标准流程
 * 
 * <AUTHOR> Assistant
 */
public interface FeatureCalculator {
    
    /**
     * 计算特征值
     * 
     * @param models 待计算的司机模型列表
     * @param context 排序上下文
     * @return 特征值映射，key为司机ID，value为特征值
     */
    Map<Long, Double> calculate(List<SortModel> models, SortContext context);
    
    /**
     * 计算单个司机的特征值
     * 
     * @param model 司机模型
     * @param context 排序上下文
     * @return 特征值
     */
    double calculateSingle(SortModel model, SortContext context);
    
    /**
     * 获取特征名称
     * 
     * @return 特征名称
     */
    String getFeatureName();
    
    /**
     * 获取特征描述
     * 
     * @return 特征描述
     */
    String getDescription();
    
    /**
     * 获取归一化策略
     * 
     * @return 归一化策略
     */
    NormalizationStrategy getNormalizationStrategy();
    
    /**
     * 获取特征权重（在所属大类别中的权重）
     * 
     * @return 特征权重
     */
    double getWeight();
    
    /**
     * 是否启用该特征
     * 
     * @param context 排序上下文
     * @return 是否启用
     */
    boolean isEnabled(SortContext context);
    
    /**
     * 加载特征计算所需的数据
     * 
     * @param models 司机模型列表
     * @param context 排序上下文
     */
    void loadData(List<SortModel> models, SortContext context);
}
