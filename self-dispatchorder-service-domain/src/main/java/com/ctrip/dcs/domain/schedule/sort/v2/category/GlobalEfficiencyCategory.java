package com.ctrip.dcs.domain.schedule.sort.v2.category;

import com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.global.*;

/**
 * 全局效率类别
 * 包含与全局调度效率相关的特征
 * 
 * <AUTHOR> Assistant
 */
public class GlobalEfficiencyCategory extends AbstractCategory {
    
    @Override
    protected void initializeFeatures() {
        // 收益均衡特征（对应现有F13）
        addFeature(new IncomeBalanceFeature());
        
        // 区域调度特征
        addFeature(new RegionalDispatchFeature());
        
        // 高峰策略特征
        addFeature(new PeakStrategyFeature());
        
        // 未来接单能力特征（对应现有F11）
        addFeature(new FutureOrderCapacityFeature());
        
        // 司机分层特征（对应现有F19）
        addFeature(new DriverTierFeature());
    }
    
    @Override
    public String getCategoryName() {
        return "GlobalEfficiency";
    }
    
    @Override
    public String getDescription() {
        return "全局效率类别：评估司机对全局调度效率的贡献，包括收益均衡、区域调度、高峰策略、未来接单能力、司机分层等指标";
    }
}
