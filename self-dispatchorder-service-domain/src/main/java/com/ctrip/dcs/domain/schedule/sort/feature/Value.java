package com.ctrip.dcs.domain.schedule.sort.feature;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.Getter;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 分值
 * <AUTHOR>
 */
@Getter
public class Value {

    private String id;

    private double value;

    private Map<String,Object> valueDetails;

    public Value(String id, double value) {
        this.id = id;
        this.value = value;
        this.valueDetails = new HashMap<>();
        valueDetails.put(SysConstants.SortValKey.SORT_VAL_KEY_V, value);
    }

    public Value(String id, double value, Map<String, Object> valueDetails) {
        this.id = id;
        this.value = value;
        this.valueDetails = valueDetails;
        if (MapUtils.isNotEmpty(valueDetails)) {
            this.valueDetails.put(SysConstants.SortValKey.SORT_VAL_KEY_V, value);
        }
    }

    /**
     * 归一后的特征值
     *
     * @param max
     * @param min
     * @param normalizer
     */
    public void normalize(double max, double min, Normalizer normalizer) {
        Transaction transaction = Cat.newTransaction("App.Normalizer", normalizer.name());
        try {
            Assert.notNull(normalizer);
            double before = this.value;
            this.value = normalizer.apply(this.value, max, min);
            double after = this.value;
            transaction.addData("id", id);
            transaction.addData("min", min);
            transaction.addData("max", max);
            transaction.addData("before", before);
            transaction.addData("after", after);
        } catch (Throwable e) {
            transaction.setStatus(e);
            throw e;
        } finally {
            transaction.complete();
        }
    }

}
