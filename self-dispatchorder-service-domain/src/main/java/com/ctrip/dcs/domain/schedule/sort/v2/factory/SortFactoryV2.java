package com.ctrip.dcs.domain.schedule.sort.v2.factory;

import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.schedule.sort.v2.HierarchicalSorterV2;
import com.ctrip.dcs.domain.schedule.sort.v2.weight.DynamicWeightManager;
import com.ctrip.dcs.domain.schedule.sort.v2.weight.WeightStrategy;

/**
 * 排序工厂V2
 * 负责创建新版本的分层排序器
 * 
 * <AUTHOR> Assistant
 */
public class SortFactoryV2 {
    
    private final ConfigService configService;
    
    public SortFactoryV2(ConfigService configService) {
        this.configService = configService;
    }
    
    /**
     * 创建分层排序器
     * 
     * @return 分层排序器
     */
    public HierarchicalSorterV2 createHierarchicalSorter() {
        WeightStrategy weightStrategy = createWeightStrategy();
        return new HierarchicalSorterV2(weightStrategy);
    }
    
    /**
     * 创建权重策略
     * 
     * @return 权重策略
     */
    private WeightStrategy createWeightStrategy() {
        String strategyType = configService.getString("sort.v2.weight.strategy", "dynamic");
        
        switch (strategyType.toLowerCase()) {
            case "dynamic":
                return new DynamicWeightManager(configService);
            case "static":
                // 可以扩展其他权重策略
                return new DynamicWeightManager(configService);
            default:
                return new DynamicWeightManager(configService);
        }
    }
    
    /**
     * 检查是否启用V2排序系统
     * 
     * @return 是否启用
     */
    public boolean isV2Enabled() {
        return configService.getString("sort.v2.enabled", "false").equals("true");
    }
}
