package com.ctrip.dcs.domain.schedule.service;

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum;
import com.ctrip.dcs.domain.common.enums.OrderSourceCodeEnum;
import com.ctrip.dcs.domain.common.enums.UdlEnum;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.value.DriverUdlVO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway;
import com.dianping.cat.utils.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/28 14:49
 */
@Service
public class CommonService {
    
    @Resource
    QueryDriverService queryDriverService;
    @Resource
    SysSwitchConfigGateway sysSwitchConfigGateway;
    
    //将列表数据按照境内外进行分组，同时每组取一个司机UDL数据作为整组的UDL信息
    public Map<UdlEnum, Pair<DriverUdlVO, List<GrabDspOrderDriverIndexDO>>> getGroupGrabDspOrderDriverIndex(List<GrabDspOrderDriverIndexDO> indexes) {
        //获取司机udls
        Set<Long> driverIds = indexes.stream().map(GrabDspOrderDriverIndexDO::getDriverId).collect(Collectors.toSet());
        Map<Long, DriverUdlVO> drvUdlMap = queryDriverService.getDrvUdlMap(driverIds);
        //区分境内外司机数据
        Map<Boolean, List<GrabDspOrderDriverIndexDO>> groupGrabDspOrderDriverIndexMap = Maps.newHashMap();
        List<DriverUdlVO> domesticUdls = Lists.newArrayList();
        List<DriverUdlVO> overseasUdls = Lists.newArrayList();
        for (GrabDspOrderDriverIndexDO index : indexes) {
            DriverUdlVO driverUdlVO = drvUdlMap.get(index.getDriverId());
            if (UdlEnum.isSgp(driverUdlVO.getUdl())) {
                if (!groupGrabDspOrderDriverIndexMap.containsKey(Boolean.TRUE)) {
                    groupGrabDspOrderDriverIndexMap.put(Boolean.TRUE, Lists.newArrayList());
                }
                groupGrabDspOrderDriverIndexMap.get(Boolean.TRUE).add(index);
                overseasUdls.add(driverUdlVO);
            } else {
                if (!groupGrabDspOrderDriverIndexMap.containsKey(Boolean.FALSE)) {
                    groupGrabDspOrderDriverIndexMap.put(Boolean.FALSE, Lists.newArrayList());
                }
                groupGrabDspOrderDriverIndexMap.get(Boolean.FALSE).add(index);
                domesticUdls.add(driverUdlVO);
            }
        }
        //境外数据
        Map<UdlEnum, Pair<DriverUdlVO, List<GrabDspOrderDriverIndexDO>>> result = Maps.newHashMap();
        List<GrabDspOrderDriverIndexDO> overseasList = groupGrabDspOrderDriverIndexMap.get(Boolean.TRUE);
        if (CollectionUtils.isNotEmpty(overseasList)) {
            DriverUdlVO driverUdlVO = overseasUdls.stream().filter(t -> StringUtils.isNotBlank(t.getUdl()) && StringUtils.isNotBlank(t.getRequestFrom())).findFirst().orElse(overseasUdls.get(0));
            result.put(UdlEnum.SGP, new Pair<>(driverUdlVO ,overseasList));
        }
        //境内数据
        List<GrabDspOrderDriverIndexDO> domesticList = groupGrabDspOrderDriverIndexMap.get(Boolean.FALSE);
        if (CollectionUtils.isNotEmpty(domesticList)) {
            DriverUdlVO driverUdlVO = domesticUdls.stream().filter(t -> StringUtils.isNotBlank(t.getUdl()) && StringUtils.isNotBlank(t.getRequestFrom())).findFirst().orElse(domesticUdls.get(0));
            result.put(UdlEnum.SHA, new Pair<>(driverUdlVO ,domesticList));
        }
        return result;
    }
    
    
    public boolean isNotWriteDriverInfo(Integer orderSourceCode, String categoryCode) {
        //(非携程渠道单、包车、点对点产)不上云的直接写入司机信息
        if (isNotCloud(orderSourceCode, categoryCode)) {
            return false;
        }
        //接送机站产线 按照开关控制
        return sysSwitchConfigGateway.getDspConfirmRecordNotWriteDriverInfoSwitch();
    }
    
    public boolean isNotCloud(Integer orderSourceCode, String categoryCode) {
        //非携程渠道单，不上云
        if (Objects.nonNull(orderSourceCode) && !Objects.equals(orderSourceCode, OrderSourceCodeEnum.TRIP.getCode())) {
            return true;
        }
        //包车、点对点产线，不上云
        if (CategoryCodeEnum.isCharterOrder(categoryCode) || CategoryCodeEnum.isPointToPoint(categoryCode)) {
            return true;
        }
        return false;
    }
    
}
