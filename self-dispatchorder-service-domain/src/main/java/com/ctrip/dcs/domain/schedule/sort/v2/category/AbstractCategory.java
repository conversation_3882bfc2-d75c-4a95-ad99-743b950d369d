package com.ctrip.dcs.domain.schedule.sort.v2.category;

import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.FeatureCalculator;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 抽象大类别实现
 * 提供大类别评分的通用逻辑
 * 
 * <AUTHOR> Assistant
 */
public abstract class AbstractCategory implements CategoryScorer {
    
    private static final Logger logger = LoggerFactory.getLogger(AbstractCategory.class);
    
    protected final List<FeatureCalculator> features;
    
    public AbstractCategory() {
        this.features = new ArrayList<>();
        initializeFeatures();
    }
    
    /**
     * 初始化特征列表
     * 子类需要实现此方法来添加具体的特征
     */
    protected abstract void initializeFeatures();
    
    @Override
    public Map<Long, Double> calculateCategoryScore(List<SortModel> models, SortContext context) {
        Map<Long, Double> categoryScores = new HashMap<>();
        
        // 初始化所有司机的得分为0
        for (SortModel model : models) {
            categoryScores.put(model.getModel().getDriver().getDriverId(), 0.0);
        }
        
        if (!isEnabled(context) || features.isEmpty()) {
            return categoryScores;
        }
        
        try {
            // 计算所有特征的加权得分
            double totalWeight = 0.0;
            Map<Long, Double> weightedScores = new HashMap<>();
            
            for (FeatureCalculator feature : features) {
                if (!feature.isEnabled(context)) {
                    continue;
                }
                
                try {
                    // 计算特征值
                    Map<Long, Double> featureValues = feature.calculate(models, context);
                    double featureWeight = feature.getWeight();
                    totalWeight += featureWeight;
                    
                    // 累加加权得分
                    for (Map.Entry<Long, Double> entry : featureValues.entrySet()) {
                        Long driverId = entry.getKey();
                        Double featureValue = entry.getValue();
                        
                        if (featureValue != null) {
                            double weightedScore = featureValue * featureWeight;
                            weightedScores.merge(driverId, weightedScore, Double::sum);
                        }
                    }
                    
                } catch (Exception e) {
                    logger.warn("特征计算失败", "category=" + getCategoryName() + 
                               ", feature=" + feature.getFeatureName(), e);
                }
            }
            
            // 计算最终得分（归一化到0-100）
            if (totalWeight > 0) {
                for (Map.Entry<Long, Double> entry : weightedScores.entrySet()) {
                    Long driverId = entry.getKey();
                    double averageScore = entry.getValue() / totalWeight;
                    // 将[0,1]区间的得分映射到[0,100]
                    double finalScore = Math.max(0, Math.min(100, averageScore * 100));
                    categoryScores.put(driverId, finalScore);
                }
            }
            
        } catch (Exception e) {
            logger.error("大类别评分计算失败", "category=" + getCategoryName(), e);
        }
        
        return categoryScores;
    }
    
    @Override
    public int getFeatureCount() {
        return features.size();
    }
    
    @Override
    public boolean isEnabled(SortContext context) {
        String configKey = "sort.v2.category." + getCategoryName().toLowerCase() + ".enabled";
        return context.getProperties(configKey, "true").equals("true");
    }
    
    /**
     * 添加特征
     * 
     * @param feature 特征计算器
     */
    protected void addFeature(FeatureCalculator feature) {
        if (feature != null) {
            features.add(feature);
        }
    }
    
    /**
     * 获取所有特征
     * 
     * @return 特征列表
     */
    public List<FeatureCalculator> getFeatures() {
        return new ArrayList<>(features);
    }
}
