package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.service;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;

/**
 * 投诉率特征
 * 评估司机的投诉率水平
 * 
 * <AUTHOR> Assistant
 */
public class ComplaintRateFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        
        // 这里需要从实际的数据源获取司机投诉率
        // 暂时使用模拟数据
        double complaintRate = getDriverComplaintRate(driver, context);
        
        // 投诉率越低，得分越高（使用逆向计算）
        // 假设最大投诉率为10%
        double maxComplaintRate = getConfigDouble(context, "feature.complaint.rate.max", 0.1);
        return Math.max(0, (maxComplaintRate - complaintRate) / maxComplaintRate * 100);
    }
    
    /**
     * 获取司机投诉率
     * 
     * @param driver 司机信息
     * @param context 排序上下文
     * @return 投诉率（0-1之间的小数）
     */
    private double getDriverComplaintRate(DriverVO driver, SortContext context) {
        // 实际实现中需要从数据库或缓存中获取司机的投诉率数据
        // 这里使用简化逻辑：基于司机ID生成模拟数据
        
        // 可以从司机的历史数据中计算投诉率
        // 投诉率 = 投诉订单数 / 总订单数
        
        // 暂时返回模拟数据
        long driverId = driver.getDriverId();
        return (driverId % 100) / 1000.0; // 生成0-0.099之间的投诉率
    }
    
    @Override
    public String getFeatureName() {
        return "ComplaintRate";
    }
    
    @Override
    public String getDescription() {
        return "投诉率特征：评估司机的投诉率水平，投诉率越低得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，值越大得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 1.2; // 投诉率是服务质量的重要指标
    }
}
