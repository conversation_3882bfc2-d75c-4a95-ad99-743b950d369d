package com.ctrip.dcs.domain.schedule.sort.feature.impl;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.feature.FeatureItem;
import com.ctrip.dcs.domain.schedule.sort.feature.FeatureItemId;
import com.ctrip.dcs.domain.schedule.sort.feature.Normalizer;
import com.ctrip.dcs.domain.schedule.sort.feature.Value;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverForwardAndBackwardOrderVisitor;
import com.dianping.cat.Cat;

import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 司机订单衔接特征值
 */
@FeatureItem(id = FeatureItemId.F41)
public class DriverOrderConnectionFeature extends AbstractFeature {

    @Override
    protected void load(List<SortModel> models, SortContext context) {
        context.accept(new DriverForwardAndBackwardOrderVisitor(context.getDspOrder(), getDrivers(models)));
    }

    @Override
    protected Value value(SortModel model, SortContext context) {
        // 获取司机
        DriverVO driver = model.getModel().getDriver();
        // 获取待派单
        DspOrderVO current = context.getDspOrder();
        // 获取前向单
        DspOrderVO forward = context.getDspContext().getDriverForwardOrderMap().get(driver.getDriverId());
        // 计算得分
        double score = calculate(context, forward, current);
        // 返回结果
        return new Value(FeatureItemId.F41.name(), score);
    }

    protected double calculate(SortContext context, DspOrderVO forwardOrder, DspOrderVO dispatchOrder) {
        if (Objects.isNull(forwardOrder)) {
            return 0;
        }
        if (Objects.isNull(dispatchOrder)) {
            return 0;
        }
        return match(context, forwardOrder, dispatchOrder);
    }

    protected double match(SortContext context, DspOrderVO previousOrder, DspOrderVO nextOrder) {
        // 前序单结束时间
        Date previousOrderEndTime = previousOrder.getPredicServiceStopTimeBj();
        // 后序单开始时间
        Date nextOrderStartTime = nextOrder.getEstimatedUseTimeBj();
        // 间隔时长分钟数
        Long durationMinutes = Duration.between(previousOrderEndTime.toInstant(), nextOrderStartTime.toInstant()).toMinutes();
        // 匹配分数区间
        return context.getDspContext().getService().getOrderConnectionDurationScore(Long.valueOf(context.getDspOrder().getCityId()), durationMinutes);
    }

    @Override
    public Normalizer normalizer() {
        return Normalizer.EMPTY;
    }

    @Override
    protected void post(SortModel model, SortContext context) {
        Cat.logEvent("App.DriverOrderConnectionFeature.Score", String.valueOf(model.getScore()));
    }

}