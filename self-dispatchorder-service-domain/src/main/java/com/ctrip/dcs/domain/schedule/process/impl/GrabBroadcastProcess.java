package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.*;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.util.OrderExtendInfoUtil;
import com.ctrip.dcs.domain.common.value.DriverServiceCityVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.common.value.TransportGroupVO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabDriverDO;
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabDriverRepository;
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository;
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO;
import com.ctrip.dcs.domain.schedule.ScheduleDO;
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.CreateGrabOrderDriverIndexEvent;
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway;
import com.ctrip.dcs.domain.schedule.process.Process;
import com.ctrip.dcs.domain.schedule.process.Processor;
import com.ctrip.dcs.domain.schedule.repository.DspOrderRewardStrategyRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 派发方式-播报抢单
 * <AUTHOR>
 */
@Processor(value = "grabBroadcastProcess", type = DspType.GRAB_BROADCAST)
public class GrabBroadcastProcess extends BaseProcess implements Process {

    private static final Logger logger = LoggerFactory.getLogger(GrabBroadcastProcess.class);

    private static final String GRAB_BROADCAST_DISTRIBUTE_KEY_PREFIX = SysConstants.APP_ID + "_GRAB_BROADCAST_PROCESS_%s";

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    protected DistributedLockService distributedLockService;

    @Autowired
    private GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository;

    @Autowired
    private GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository;

    @Autowired
    private DriverPointsGateway driverPointsGateway;

    @Autowired
    private QueryTransportGroupService queryTransportGroupService;

    @Autowired
    private QueryDriverService queryDriverService;

    @Autowired
    private QueryOrderSettlePriceService queryOrderSettlePriceService;

    @Autowired
    private VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository;

    @Autowired
    private VBKDriverGrabDriverRepository vbkDriverGrabDriverRepository;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Autowired
    @Qualifier("broadcastSelectTimeConfig")
    private ConfigService broadcastSelectTimeConfig;

    @Autowired
    private MessageProviderService messageProviderService;

    @Autowired
    private GrabDspOrderSnapshotRecordService grabDspOrderSnapshotRecordService;

    @Autowired
    private DspOrderRewardStrategyRepository dspOrderRewardStrategyRepository;

    @Autowired
    private QueryGuideServiceCityService queryGuideServiceCityService;

    @Override
    public void execute(ScheduleTaskDO task, DspOrderVO order) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(String.format(GRAB_BROADCAST_DISTRIBUTE_KEY_PREFIX, order.getDspOrderId()));
        try {
            if (!lock.tryLock()) {
                return;
            }
            // 查询调度
            ScheduleDO schedule = scheduleRepository.find(task.getScheduleId());
            // 校验
            boolean ok = validate(schedule, order);
            if (!ok) {
                logger.info("GrabBroadcastProcessInfo", "order status is not to be confirmed or dispatch confirmed, order id: {}", order.getDspOrderId());
                return;
            }
            // 查询运力组
            List<TransportGroupVO> transportGroups = queryTransportGroups(schedule, order);
            // 查询司机id
            List<Long> driverIds = queryDriverIds(order, schedule, transportGroups);
            // 查询特殊时段分
            BigDecimal specialTimePoint = querySpecialTimePoint(order);
            // 查询结算信息
            List<OrderSettlePriceVO> orderSettlePrices = queryOrderSettlePrice(order, schedule, task, transportGroups);
            // 抢单结果通知时间-秒
            Integer tipsTime = querySelectTipsTime(order);
            // 执行
            execute(schedule, task, order, driverIds, specialTimePoint, orderSettlePrices, tipsTime);
        } catch (Exception e) {
            logger.error("GrabBroadcastProcessError", e);
            MetricsUtil.recordValue(MetricsConstants.GRAB_BROADCAST_PROCESS_ERROR);
        } finally {
            completeTask(task, false);
            lock.unlock();
        }
    }

    public void execute(ScheduleDO schedule, ScheduleTaskDO task, DspOrderVO order, List<Long> driverIds, BigDecimal specialTimePoint, List<OrderSettlePriceVO> orderSettlePrices, Integer tipsTime) {
        GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(order.getDspOrderId());
        List<GrabDspOrderDriverIndexDO> indexes = Lists.newArrayList();
        if (Objects.isNull(snapshot)) {
            // 创建抢单快照
            snapshot = new GrabDspOrderSnapshotDO();
        } else {
            indexes = grabDspOrderDriverIndexRepository.queryFomCache(order.getDspOrderId());
        }
        snapshot.updateOrderDetail(order)
                .updateScheduleDetail(schedule)
                .updateScheduleTaskDetail(task)
                .updateSpecialTimePoint(specialTimePoint)
                .addOrderSettlePrices(orderSettlePrices)
                .updateTipsTime(tipsTime);
        // 存量失效映射
        List<GrabDspOrderDriverIndexDO> updateIndexes = snapshot.updateIndex(indexes, driverIds);
        // 生成新映射
        List<GrabDspOrderDriverIndexDO> newIndexes = snapshot.newIndex(indexes, driverIds);
        logger.info("GrabDspOrderSnapshotInfo_" + snapshot.getDspOrderId(), JacksonSerializer.INSTANCE().serialize(snapshot));
        logger.info("GrabDspOrderDriverTotalIndexInfo", JacksonSerializer.INSTANCE().serialize(indexes));
        logger.info("GrabDspOrderDriverNewIndexInfo", JacksonSerializer.INSTANCE().serialize(newIndexes));
        logger.info("GrabDspOrderDriverUpdateIndexInfo", JacksonSerializer.INSTANCE().serialize(updateIndexes));
        // 更新存量失效的映射
        grabDspOrderDriverIndexRepository.update(updateIndexes);
        // 存量有效的映射
        List<GrabDspOrderDriverIndexDO> validIndexes = indexes.stream().filter(item -> YesOrNo.isYes(item.getIsValid())).toList();
        if (CollectionUtils.isEmpty(newIndexes) && CollectionUtils.isEmpty(validIndexes)) {
            return;
        }
        // 匹配发单规则，计算发单时间
        boolean ok = snapshot.rule(merge(newIndexes, validIndexes));
        if (!ok) {
            logger.info("GrabDspOrderSnapshotInfo_" + order.getDspOrderId(), "order is not match rule, order id: {}", order.getDspOrderId());
            return;
        }
        // 保持或更新抢单快照
        grabDspOrderSnapshotRepository.saveOrUpdate(snapshot);
        // 插入新的司机映射
        grabDspOrderDriverIndexRepository.save(newIndexes);
        // 更新存量有效的司机映射
        grabDspOrderDriverIndexRepository.update(validIndexes);
        // 发送抢单快照变更消息
        sendMessage(snapshot, newIndexes);
        // 发送VBK订单操作记录
        grabDspOrderSnapshotRecordService.sendVBKOperationRecord(snapshot);
    }

    public List<GrabDspOrderDriverIndexDO> merge(List<GrabDspOrderDriverIndexDO> newIndexes, List<GrabDspOrderDriverIndexDO> validIndexes) {
        List<GrabDspOrderDriverIndexDO> total = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(newIndexes)) {
            total.addAll(newIndexes);
        }
        if (CollectionUtils.isNotEmpty(validIndexes)) {
            total.addAll(validIndexes);
        }
        return total;
    }

    private void sendMessage(GrabDspOrderSnapshotDO snapshot, List<GrabDspOrderDriverIndexDO> newIndex) {
        if (CollectionUtils.isEmpty(newIndex)) {
            return;
        }
        List<Long> driverIds = newIndex.stream().filter(index -> Objects.nonNull(index.getGrabPushTimeBj())).sorted(Comparator.comparing(GrabDspOrderDriverIndexDO::getGrabPushTimeBj)).map(GrabDspOrderDriverIndexDO::getDriverId).distinct().toList();
        List<List<Long>> partition = Lists.partition(driverIds, 100);
        for (List<Long> list : partition) {
            messageProviderService.send(new CreateGrabOrderDriverIndexEvent(snapshot.getDspOrderId(), list));
        }
    }

    public Integer querySelectTipsTime(DspOrderVO order) {
        String key = String.format("select_tips_time_%s_%s", order.getCityId(), order.getCategoryCode().getType());
        return broadcastSelectTimeConfig.getInteger(key, 10);
    }

    public BigDecimal querySpecialTimePoint(DspOrderVO order) {
        if (CategoryCodeEnum.isCharterOrder(order.getCategoryCode().getType())) {
            // 包车订单，没有特殊时段分
            return BigDecimal.ZERO;
        }
        return driverPointsGateway.querySpecialTimePoint(order.getDspOrderId());
    }

    public List<OrderSettlePriceVO> queryOrderSettlePrice(DspOrderVO order, ScheduleDO schedule, ScheduleTaskDO task, List<TransportGroupVO> transportGroups) {
        if (Objects.isNull(order.getSettleToDriver()) || YesOrNo.isYes(order.getSettleToDriver())) {
            // 司机结算订单，查询结算信息
            return queryOrderSettleToDriverPrice(order, task, transportGroups);
        }
        // 供应商结算订单，查询结算信息
        return queryOrderSettleToSupplierPrice(order, schedule, task);
    }

    public List<OrderSettlePriceVO> queryOrderSettleToDriverPrice(DspOrderVO order, ScheduleTaskDO task, List<TransportGroupVO> transportGroups) {
        // 司机结算订单，查询结算信息
        Set<Long> supplierIds = transportGroups.stream().map(TransportGroupVO::getSupplierId).collect(Collectors.toSet());
        return queryOrderSettlePriceService.queryOrderDriverSettlePriceListBeforeTaken(order.getDspOrderId(), order.getCarTypeId(), order.getCategoryCode().getType(), supplierIds, DuidVO.of(task).toString());
    }

    public List<OrderSettlePriceVO> queryOrderSettleToSupplierPrice(DspOrderVO order, ScheduleDO schedule, ScheduleTaskDO task) {
        // 派发加价奖励
        BigDecimal preRewardAmount = BigDecimal.ZERO;
        if (task.getDspRewardStrategyId() > 0) {
            DspOrderRewardStrategyDO rewardStrategy = dspOrderRewardStrategyRepository.find(task.getDspRewardStrategyId());
            preRewardAmount = Optional.ofNullable(rewardStrategy).map(DspOrderRewardStrategyDO::getDspRewardAmount).orElse(BigDecimal.ZERO);
        }
        // 调度确认时，查询结算获取的供应商结算价
        BigDecimal preSupplierSettleAmount = OrderExtendInfoUtil.getExtendInfo(order, "preSupplierSettleAmount", BigDecimal.ZERO);
        String preSupplierSettleCurrency = OrderExtendInfoUtil.getExtendInfo(order, "preSupplierSettleCurrency", StringUtils.EMPTY);
        if (Objects.equals(schedule.getType(), ScheduleType.VBK)) {
            // 供应商触发的
            VBKDriverGrabOrderDO vbkDriverGrabOrder = vbkDriverGrabOrderRepository.queryBySupplierId(order.getDspOrderId(), order.getSupplierId());
            // 供应商配置的抢单初始金额
            BigDecimal initialAmount = Optional.ofNullable(vbkDriverGrabOrder).map(VBKDriverGrabOrderDO::getInitialAmount).orElse(BigDecimal.ZERO);
            String initialCurrency = Optional.ofNullable(vbkDriverGrabOrder).map(VBKDriverGrabOrderDO::getInitialCurrency).orElse(StringUtils.EMPTY);
            return Lists.newArrayList(
                    OrderSettlePriceVO.builder()
                            .supplierId(order.getSupplierId().longValue())
                            .dspOrderId(order.getDspOrderId())
                            .preSupplierSettleAmount(preSupplierSettleAmount)
                            .preSupplierSettleCurrency(preSupplierSettleCurrency)
                            .preDriverSettleAmount(initialAmount.add(preRewardAmount))
                            .preDriverSettleCurrency(initialCurrency)
                            .preRewardAmount(preRewardAmount)
                            .settleToDriver(YesOrNo.NO.getCode())
                            .build()
            );
        }
        // 调度确认时，查询结算获取的司机指导价
        BigDecimal preDriverGuideAmount = OrderExtendInfoUtil.getExtendInfo(order, "preDriverGuideAmount", BigDecimal.ZERO);
        String preDriverGuideCurrency = OrderExtendInfoUtil.getExtendInfo(order, "preDriverGuideCurrency", StringUtils.EMPTY);
        return Lists.newArrayList(
                OrderSettlePriceVO.builder()
                        .supplierId(order.getSupplierId().longValue())
                        .dspOrderId(order.getDspOrderId())
                        .preSupplierSettleAmount(preSupplierSettleAmount)
                        .preSupplierSettleCurrency(preSupplierSettleCurrency)
                        .preDriverGuideAmount(preDriverGuideAmount)
                        .preDriverGuideCurrency(preDriverGuideCurrency)
                        .preDriverSettleAmount(preDriverGuideAmount.add(preRewardAmount))
                        .preDriverSettleCurrency(preDriverGuideCurrency)
                        .preRewardAmount(preRewardAmount)
                        .settleToDriver(YesOrNo.NO.getCode())
                        .build()
        );
    }

    private boolean validate(ScheduleDO schedule, DspOrderVO order) {
        if (schedule == null || order == null) {
            return false;
        }
        return schedule.isVBKType() ? Objects.equals(order.getOrderStatus(), OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) : Objects.equals(order.getOrderStatus(), OrderStatusEnum.TO_BE_CONFIRMED.getCode());
    }

    public List<Long> queryDriverIds(DspOrderVO order, ScheduleDO schedule, List<TransportGroupVO> transportGroups) {
        if (Objects.equals(schedule.getType(), ScheduleType.VBK)) {
            // 获取供应商圈定的司机
            return queryDriverIdsFromVBKTask(order);
        }
        List<Long> transportGroupIds = transportGroups.stream().map(TransportGroupVO::getTransportGroupId).toList();
        ParentCategoryEnum parentCategoryEnum = CategoryUtils.selfGetParentType(order);
        List<Long> driverIds = queryDriverService.queryDriverIds(transportGroupIds, parentCategoryEnum);
        if (!Objects.equals(parentCategoryEnum, ParentCategoryEnum.DAY)) {
            // 非包车订单，直接返回
            return driverIds;
        }
        // 包车订单，查询司机全部服务目的地，是否跟订单匹配
        List<DriverServiceCityVO> cities = queryGuideServiceCityService.query(driverIds);
        Map<Long /*driverId*/, List<Long> /*cityIds*/> map = cities.stream().collect(Collectors.toMap(DriverServiceCityVO::getDriverId, DriverServiceCityVO::getCityIds));
        return driverIds.stream()
                .filter(id -> {
                    List<Long> cityIds = map.getOrDefault(id, Collections.emptyList());
                    return cityIds.contains(order.getCityId().longValue()) || cityIds.contains(order.getFromCityId().longValue()) || cityIds.contains(order.getToCityId().longValue());
                })
                .toList();
    }

    public List<Long> queryDriverIdsFromVBKTask(DspOrderVO order) {
        VBKDriverGrabOrderDO vbkDriverGrabOrder = vbkDriverGrabOrderRepository.queryBySupplierId(order.getDspOrderId(), order.getSupplierId());
        if (Objects.isNull(vbkDriverGrabOrder)) {
            return Collections.emptyList();
        }
        List<VBKDriverGrabDriverDO> vbkDriverGrabDrivers = vbkDriverGrabDriverRepository.queryDriverListByTaskId(vbkDriverGrabOrder.getVbkGrabTaskId());
        if (CollectionUtils.isEmpty(vbkDriverGrabDrivers)) {
            return Collections.emptyList();
        }
        return vbkDriverGrabDrivers.stream().map(VBKDriverGrabDriverDO::getDriverId).filter(Objects::nonNull).map(Long::valueOf).distinct().toList();
    }

    public List<TransportGroupVO> queryTransportGroups(ScheduleDO schedule, DspOrderVO order) {
        if (schedule.isVBKType()) {
            return queryTransportGroups(order.getSkuId(), order.getSupplierId());
        }
        return queryTransportGroups(order);
    }

    private List<TransportGroupVO> queryTransportGroups(Integer skuId, Integer supplierId) {
        // 查询供应商下绑定了商品的运力组
        List<TransportGroupVO> list = queryTransportGroupService.queryTransportGroups(skuId, supplierId, null);
        return list.stream().filter(t -> Objects.equals(t.getSupplierId(), supplierId.longValue())).collect(Collectors.toList());
    }

    private List<TransportGroupVO> queryTransportGroups(DspOrderVO order) {
        // 查询可以服务订单的运力组
        List<TransportGroupVO> list = queryTransportGroupService.queryTransportGroups(order);
        // 过滤出兼职司机播报和报名制运力组
        return list.stream().filter(t -> Objects.equals(TransportGroupMode.PART_TIME_BROADCAST, t.getTransportGroupMode()) || Objects.equals(TransportGroupMode.REGISTER_DISPATCH, t.getTransportGroupMode())).collect(Collectors.toList());
    }
}
