package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.timespace;

import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;
import com.ctrip.dcs.domain.schedule.value.TimeIntervalVO;
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverRelateOrderVisitor;

import java.util.List;

/**
 * 局部时间间隔特征
 * 对应现有的F9特征，评估司机在局部时间窗口内的时间间隔效率
 * 
 * <AUTHOR> Assistant
 */
public class LocalTimeIntervalFeature extends AbstractFeatureV2 {
    
    private static final int TIME_INTERVAL_NEIGHBOUR = 105 * 60; // 105分钟转换为秒
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DspOrderVO order = model.getModel().getOrder();
        DriverVO driver = model.getModel().getDriver();
        
        // 获取司机相关订单信息
        DriverRelateOrderVO relateOrder = context.getRelateOrderNoEmptyDriveLbs(driver.getDriverId());
        if (relateOrder == null) {
            return DEFAULT_VALUE;
        }
        
        // 创建时间间隔计算对象
        TimeIntervalVO timeIntervalVO = new TimeIntervalVO(
            order.estimatedUseTime(), 
            order.predictServiceStopTime(), 
            driver.getWorkTimes()
        );
        
        // 计算前向和后向时间间隔
        double forwardInterval = timeIntervalVO.interval(
            relateOrder.getFrowardPredictServiceStopTime(), 
            TIME_INTERVAL_NEIGHBOUR
        );
        
        double backwardInterval = timeIntervalVO.interval(
            relateOrder.getBackwardEstimatedUseTime(), 
            TIME_INTERVAL_NEIGHBOUR
        );
        
        // 计算局部时间间隔得分
        // 时间间隔越小，得分越高
        double forwardScore = Math.max(0.0, TIME_INTERVAL_NEIGHBOUR - forwardInterval);
        double backwardScore = Math.max(0.0, TIME_INTERVAL_NEIGHBOUR - backwardInterval);
        
        return forwardScore + backwardScore;
    }
    
    @Override
    public void loadData(List<SortModel> models, SortContext context) {
        DspOrderVO order = context.getDspOrder();
        List<DriverVO> drivers = models.stream()
            .map(model -> model.getModel().getDriver())
            .toList();
        
        // 加载司机相关订单数据
        context.accept(new DriverRelateOrderVisitor(order, drivers, 12, false));
    }
    
    @Override
    public String getFeatureName() {
        return "LocalTimeInterval";
    }
    
    @Override
    public String getDescription() {
        return "局部时间间隔特征：评估司机在局部时间窗口内的时间间隔效率，时间间隔越小得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，值越大得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 0.8; // 对应原F9的权重
    }
}
