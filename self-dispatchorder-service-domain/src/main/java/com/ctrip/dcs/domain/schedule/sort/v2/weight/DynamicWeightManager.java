package com.ctrip.dcs.domain.schedule.sort.v2.weight;

import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 动态权重管理器
 * 支持基于城市、时段、场景的多维度权重配置
 * 
 * <AUTHOR> Assistant
 */
public class DynamicWeightManager implements WeightStrategy {
    
    private final ConfigService configService;
    private final Map<String, WeightStrategy> strategyMap;
    
    // 配置键前缀
    private static final String WEIGHT_CONFIG_PREFIX = "sort.v2.weight";
    private static final String CITY_CONFIG_PREFIX = WEIGHT_CONFIG_PREFIX + ".city";
    private static final String TIME_CONFIG_PREFIX = WEIGHT_CONFIG_PREFIX + ".time";
    private static final String SCENARIO_CONFIG_PREFIX = WEIGHT_CONFIG_PREFIX + ".scenario";
    
    // 时段定义
    private static final LocalTime PEAK_MORNING_START = LocalTime.of(7, 0);
    private static final LocalTime PEAK_MORNING_END = LocalTime.of(9, 30);
    private static final LocalTime PEAK_EVENING_START = LocalTime.of(17, 0);
    private static final LocalTime PEAK_EVENING_END = LocalTime.of(19, 30);
    
    public DynamicWeightManager(ConfigService configService) {
        this.configService = configService;
        this.strategyMap = new HashMap<>();
        initializeStrategies();
    }
    
    /**
     * 初始化各种权重策略
     */
    private void initializeStrategies() {
        strategyMap.put("default", new DefaultWeightStrategy());
        strategyMap.put("peak", new PeakHourWeightStrategy());
        strategyMap.put("off_peak", new OffPeakWeightStrategy());
        strategyMap.put("airport", new AirportWeightStrategy());
        strategyMap.put("station", new StationWeightStrategy());
    }
    
    @Override
    public CategoryWeight getWeight(DspOrderVO order, SubSkuVO subSku) {
        // 1. 尝试获取城市特定配置
        CategoryWeight cityWeight = getCitySpecificWeight(order.getCityId());
        if (cityWeight != null) {
            return cityWeight;
        }
        
        // 2. 尝试获取时段特定配置
        CategoryWeight timeWeight = getTimeSpecificWeight();
        if (timeWeight != null) {
            return timeWeight;
        }
        
        // 3. 尝试获取场景特定配置
        CategoryWeight scenarioWeight = getScenarioSpecificWeight(order, subSku);
        if (scenarioWeight != null) {
            return scenarioWeight;
        }
        
        // 4. 返回默认配置
        return CategoryWeight.createDefault();
    }
    
    /**
     * 获取城市特定权重配置
     * 
     * @param cityId 城市ID
     * @return 城市特定权重配置
     */
    private CategoryWeight getCitySpecificWeight(Integer cityId) {
        String configKey = CITY_CONFIG_PREFIX + "." + cityId;
        
        Double timeSpaceWeight = configService.getDouble(configKey + ".timeSpace", null);
        Double serviceQualityWeight = configService.getDouble(configKey + ".serviceQuality", null);
        Double orderMatchingWeight = configService.getDouble(configKey + ".orderMatching", null);
        Double globalEfficiencyWeight = configService.getDouble(configKey + ".globalEfficiency", null);
        
        if (timeSpaceWeight != null && serviceQualityWeight != null && 
            orderMatchingWeight != null && globalEfficiencyWeight != null) {
            return new CategoryWeight(timeSpaceWeight, serviceQualityWeight, 
                                    orderMatchingWeight, globalEfficiencyWeight);
        }
        
        return null;
    }
    
    /**
     * 获取时段特定权重配置
     * 
     * @return 时段特定权重配置
     */
    private CategoryWeight getTimeSpecificWeight() {
        LocalTime now = LocalTime.now();
        String timeSlot = getTimeSlot(now);
        
        String configKey = TIME_CONFIG_PREFIX + "." + timeSlot;
        
        Double timeSpaceWeight = configService.getDouble(configKey + ".timeSpace", null);
        Double serviceQualityWeight = configService.getDouble(configKey + ".serviceQuality", null);
        Double orderMatchingWeight = configService.getDouble(configKey + ".orderMatching", null);
        Double globalEfficiencyWeight = configService.getDouble(configKey + ".globalEfficiency", null);
        
        if (timeSpaceWeight != null && serviceQualityWeight != null && 
            orderMatchingWeight != null && globalEfficiencyWeight != null) {
            return new CategoryWeight(timeSpaceWeight, serviceQualityWeight, 
                                    orderMatchingWeight, globalEfficiencyWeight);
        }
        
        // 使用预定义的时段策略
        WeightStrategy strategy = strategyMap.get(timeSlot);
        if (strategy != null) {
            return strategy.getWeight(null, null);
        }
        
        return null;
    }
    
    /**
     * 获取场景特定权重配置
     * 
     * @param order 订单信息
     * @param subSku 子产品信息
     * @return 场景特定权重配置
     */
    private CategoryWeight getScenarioSpecificWeight(DspOrderVO order, SubSkuVO subSku) {
        String scenario = determineScenario(order, subSku);
        if (scenario == null) {
            return null;
        }
        
        String configKey = SCENARIO_CONFIG_PREFIX + "." + scenario;
        
        Double timeSpaceWeight = configService.getDouble(configKey + ".timeSpace", null);
        Double serviceQualityWeight = configService.getDouble(configKey + ".serviceQuality", null);
        Double orderMatchingWeight = configService.getDouble(configKey + ".orderMatching", null);
        Double globalEfficiencyWeight = configService.getDouble(configKey + ".globalEfficiency", null);
        
        if (timeSpaceWeight != null && serviceQualityWeight != null && 
            orderMatchingWeight != null && globalEfficiencyWeight != null) {
            return new CategoryWeight(timeSpaceWeight, serviceQualityWeight, 
                                    orderMatchingWeight, globalEfficiencyWeight);
        }
        
        // 使用预定义的场景策略
        WeightStrategy strategy = strategyMap.get(scenario);
        if (strategy != null) {
            return strategy.getWeight(order, subSku);
        }
        
        return null;
    }
    
    /**
     * 确定时段
     * 
     * @param time 当前时间
     * @return 时段标识
     */
    private String getTimeSlot(LocalTime time) {
        if ((time.isAfter(PEAK_MORNING_START) && time.isBefore(PEAK_MORNING_END)) ||
            (time.isAfter(PEAK_EVENING_START) && time.isBefore(PEAK_EVENING_END))) {
            return "peak";
        } else {
            return "off_peak";
        }
    }
    
    /**
     * 确定场景类型
     * 
     * @param order 订单信息
     * @param subSku 子产品信息
     * @return 场景标识
     */
    private String determineScenario(DspOrderVO order, SubSkuVO subSku) {
        // 根据订单的起点或终点判断场景
        // 这里需要根据实际业务逻辑来实现
        // 示例逻辑：
        if (isAirportOrder(order)) {
            return "airport";
        } else if (isStationOrder(order)) {
            return "station";
        }
        
        return null;
    }
    
    /**
     * 判断是否为机场订单
     * 
     * @param order 订单信息
     * @return 是否为机场订单
     */
    private boolean isAirportOrder(DspOrderVO order) {
        // 实际实现中需要根据POI信息或地址信息判断
        // 这里只是示例
        return false;
    }
    
    /**
     * 判断是否为车站订单
     * 
     * @param order 订单信息
     * @return 是否为车站订单
     */
    private boolean isStationOrder(DspOrderVO order) {
        // 实际实现中需要根据POI信息或地址信息判断
        // 这里只是示例
        return false;
    }
    
    @Override
    public String getStrategyName() {
        return "DynamicWeightManager";
    }
    
    @Override
    public String getDescription() {
        return "动态权重管理器，支持基于城市、时段、场景的多维度权重配置";
    }
    
    // 内部策略类
    private static class DefaultWeightStrategy implements WeightStrategy {
        @Override
        public CategoryWeight getWeight(DspOrderVO order, SubSkuVO subSku) {
            return CategoryWeight.createDefault();
        }
        
        @Override
        public String getStrategyName() {
            return "Default";
        }
        
        @Override
        public String getDescription() {
            return "默认权重策略";
        }
    }
    
    private static class PeakHourWeightStrategy implements WeightStrategy {
        @Override
        public CategoryWeight getWeight(DspOrderVO order, SubSkuVO subSku) {
            // 高峰期更注重时空效率
            return CategoryWeight.createTimeSpaceFocused();
        }
        
        @Override
        public String getStrategyName() {
            return "PeakHour";
        }
        
        @Override
        public String getDescription() {
            return "高峰期权重策略，更注重时空效率";
        }
    }
    
    private static class OffPeakWeightStrategy implements WeightStrategy {
        @Override
        public CategoryWeight getWeight(DspOrderVO order, SubSkuVO subSku) {
            // 非高峰期更注重服务质量
            return CategoryWeight.createServiceQualityFocused();
        }
        
        @Override
        public String getStrategyName() {
            return "OffPeak";
        }
        
        @Override
        public String getDescription() {
            return "非高峰期权重策略，更注重服务质量";
        }
    }
    
    private static class AirportWeightStrategy implements WeightStrategy {
        @Override
        public CategoryWeight getWeight(DspOrderVO order, SubSkuVO subSku) {
            // 机场订单更注重服务质量和订单匹配度
            return new CategoryWeight(0.30, 0.40, 0.25, 0.05);
        }
        
        @Override
        public String getStrategyName() {
            return "Airport";
        }
        
        @Override
        public String getDescription() {
            return "机场订单权重策略，更注重服务质量和订单匹配度";
        }
    }
    
    private static class StationWeightStrategy implements WeightStrategy {
        @Override
        public CategoryWeight getWeight(DspOrderVO order, SubSkuVO subSku) {
            // 车站订单更注重时空效率
            return new CategoryWeight(0.55, 0.20, 0.20, 0.05);
        }
        
        @Override
        public String getStrategyName() {
            return "Station";
        }
        
        @Override
        public String getDescription() {
            return "车站订单权重策略，更注重时空效率";
        }
    }
}
