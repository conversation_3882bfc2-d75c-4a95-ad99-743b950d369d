package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.timespace;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;

/**
 * 空驶时长特征
 * 评估司机的空驶时长效率
 * 
 * <AUTHOR> Assistant
 */
public class EmptyDurationFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        
        // 获取司机空驶时长（秒）
        Double emptyDuration = context.getDriverEmptyDurationMap().get(driver.getDriverId());
        if (emptyDuration == null) {
            return DEFAULT_VALUE;
        }
        
        // 转换为分钟
        double emptyDurationMinutes = emptyDuration / 60.0;
        
        // 空驶时长越短，得分越高（使用逆向计算）
        // 假设最大合理空驶时长为120分钟
        double maxEmptyDuration = getConfigDouble(context, "feature.empty.duration.max", 120.0);
        return Math.max(0, maxEmptyDuration - emptyDurationMinutes);
    }
    
    @Override
    public String getFeatureName() {
        return "EmptyDuration";
    }
    
    @Override
    public String getDescription() {
        return "空驶时长特征：评估司机的空驶时长效率，空驶时长越短得分越高";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，值越大得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 0.8;
    }
}
