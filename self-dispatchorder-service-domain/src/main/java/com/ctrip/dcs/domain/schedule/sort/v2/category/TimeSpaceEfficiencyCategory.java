package com.ctrip.dcs.domain.schedule.sort.v2.category;

import com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.timespace.*;

/**
 * 时空效率类别
 * 包含与时间和空间效率相关的特征
 * 
 * <AUTHOR> Assistant
 */
public class TimeSpaceEfficiencyCategory extends AbstractCategory {
    
    @Override
    protected void initializeFeatures() {
        // ETA特征 - 预估到达时间
        addFeature(new EtaFeature());
        
        // 空驶距离特征
        addFeature(new EmptyDistanceFeature());
        
        // 空驶时长特征
        addFeature(new EmptyDurationFeature());
        
        // 局部时间间隔特征（对应现有F9）
        addFeature(new LocalTimeIntervalFeature());
        
        // 局部空驶距离特征（对应现有F10）
        addFeature(new LocalEmptyDistanceFeature());
    }
    
    @Override
    public String getCategoryName() {
        return "TimeSpaceEfficiency";
    }
    
    @Override
    public String getDescription() {
        return "时空效率类别：评估司机在时间和空间维度的效率，包括ETA、空驶距离、空驶时长、局部时间间隔等指标";
    }
}
