package com.ctrip.dcs.domain.schedule.sort.v2.grade;

import java.util.HashMap;
import java.util.Map;

/**
 * 默认等级映射器实现
 * 使用标准的分数区间进行等级映射
 * 
 * <AUTHOR> Assistant
 */
public class DefaultGradeMapper implements GradeMapper {
    
    @Override
    public Grade mapToGrade(double score) {
        return Grade.fromScore(score);
    }
    
    @Override
    public Map<String, Grade> mapToGrades(Map<String, Double> scores) {
        Map<String, Grade> grades = new HashMap<>();
        for (Map.Entry<String, Double> entry : scores.entrySet()) {
            grades.put(entry.getKey(), mapToGrade(entry.getValue()));
        }
        return grades;
    }
    
    @Override
    public Map<Grade, Integer> getGradeDistribution(double[] scores) {
        Map<Grade, Integer> distribution = new HashMap<>();
        
        // 初始化计数器
        for (Grade grade : Grade.values()) {
            distribution.put(grade, 0);
        }
        
        // 统计各等级数量
        for (double score : scores) {
            Grade grade = mapToGrade(score);
            distribution.put(grade, distribution.get(grade) + 1);
        }
        
        return distribution;
    }
}
