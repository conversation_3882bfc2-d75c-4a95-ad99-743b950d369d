package com.ctrip.dcs.domain.schedule.process.impl;

import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.constants.IdempotentConstants;
import com.ctrip.dcs.domain.common.enums.DspType;
import com.ctrip.dcs.domain.common.enums.UdlEnum;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.IdempotentCheckService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.CatUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO;
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO;
import com.ctrip.dcs.domain.schedule.event.GrabCentreEvent;
import com.ctrip.dcs.domain.schedule.factory.GrabOrderFactory;
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway;
import com.ctrip.dcs.domain.schedule.process.Process;
import com.ctrip.dcs.domain.schedule.process.Processor;
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository;
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.service.DspContextService;
import com.ctrip.dcs.domain.schedule.service.RecommendService;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

;

/**
 * 抢单大厅
 * <AUTHOR>
 */
@Processor(value = "grabCentreProcess", type = DspType.GRAB_ORDER)
public class GrabCentreProcess extends BaseProcess implements Process {

    private static final Logger logger = LoggerFactory.getLogger(GrabCentreProcess.class);

    private static final Integer BROADCAST_DRIVER_LIMIT = 100;

    @Autowired
    private ScheduleTaskRepository taskRepository;

    @Autowired
    private RecommendService recommendService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private MessageProviderService messageProducer;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Autowired
    private GrabOrderFactory grabOrderFactory;

    @Autowired
    private GrabCentreRepository grabCentreRepository;
    @Autowired
    private DspContextService dspContextService;

    @Autowired
    private IdempotentCheckService idempotentCheckService;
    @Resource
    SysSwitchConfigGateway sysSwitchConfigGateway;
    
    @Override
    public void execute(ScheduleTaskDO task, DspOrderVO order) {
        DuidVO duid = new DuidVO();
        List<DspModelVO> list = Lists.newArrayList();
        try {
            // 运力检查
            DuidVO duidVO = DuidVO.of(task);
            logger.info("grab_centre_process_duid", duidVO.toString());
            List<CheckModel> checkModels = checkService.grabCheck(new DspCheckCommand(order, task.getSubSku(), duidVO));
            List<DspModelVO> models = checkModels.stream().filter(CheckModel::isPass).map(CheckModel::getModel).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(models)) {
                logger.info("system grab centre process", "driver recommend null! task id:{}, dsp order id:{}", task.getTaskId(), task.getDspOrderId());
                return;
            }
            list = models;
            // duid
            duid = DuidVO.of(task);
            super.upLoadMeMetrics("sum", duid, list);

            List<GrabOrderDO> grabOrders = saveGrabOrder(task, order, duid, models);
            // 发送播报消息
            sendGrabCentreMessage(duid, models);
            // 缓存失效消息
            super.sendGrabOrderExpireMessage(grabOrders);
        } catch (Exception e) {
            super.upLoadMeMetrics("error", duid, list);
            logger.error(e);
        } finally {
            completeTask(task, false);
        }
    }

    /**
     * 抢单大厅消息
     * @param duid
     * @param models
     */
    protected void sendGrabCentreMessage(DuidVO duid, List<DspModelVO> models) {
        models = models.stream().filter(this::isProcessed).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        if (!sysSwitchConfigGateway.getDriverQmqAddUcsSwitch()) {
            sendGrabCentreMsg(duid, models, 0L);
            return;
        }
        final AtomicLong delay = new AtomicLong(NumberUtils.LONG_ZERO);
        Map<Boolean, List<DspModelVO>> groupDspModel = models.stream().collect(Collectors.partitioningBy(t -> UdlEnum.isSgp(t.getDriver().getUdl())));
        for (Map.Entry<Boolean, List<DspModelVO>> entry :groupDspModel.entrySet()) {
            List<DspModelVO> dspModelVOS = entry.getValue();
            if (CollectionUtils.isEmpty(dspModelVOS)) {
                continue;
            }
            //尽量取uid不为空的司机
            DspModelVO dspModelVO = dspModelVOS.stream().filter(t -> StringUtils.isNotBlank(t.getDriver().getUdl()) && StringUtils.isNotBlank(t.getDriver().getRequestFrom()) && StringUtils.isNotBlank(t.getDriver().getUid())).findFirst().orElse(dspModelVOS.get(0));
            String udl = dspModelVO.getDriver().getUdl();
            String requestFrom = dspModelVO.getDriver().getRequestFrom();
            //如果udl和requestFrom为空的话，重新查询
            if (StringUtils.isBlank(udl)) {
                logger.info("send_grabCentre_message", "default udl");
                udl = UdlEnum.SHA.getValue();
                requestFrom = UdlEnum.SHA.getValue().split("_")[1];
            }
            long delayVal = CatUtil.doWithUdlOverride(() -> sendGrabCentreMsg(duid, dspModelVOS, delay.get()), udl, requestFrom);
            delay.set(delayVal);
        }
    }
    
    
    public long sendGrabCentreMsg(DuidVO duid, List<DspModelVO> models, long delay) {
        Integer limit = broadcastGrabConfig.getInteger(ConfigKey.BROADCAST_DRIVER_LIMIT_KEY, BROADCAST_DRIVER_LIMIT);
        List<List<DspModelVO>> partition = Lists.partition(models, limit);
        for (List<DspModelVO> list : partition) {
            messageProducer.send(new GrabCentreEvent(duid, list, delay));
            super.upLoadMeMetrics("success", duid, list);
            delay += broadcastGrabConfig.getLong(ConfigKey.BROADCAST_DRIVER_DELAY_KEY, NumberUtils.LONG_ZERO);
        }
        return delay;
    }

    /**
     * 抢单大厅消息幂等
     * @param model
     * @return true:未推送过，false:已推送过
     */
    private boolean isProcessed(DspModelVO model) {
        return notPushDriverByOrder(model.getDriver().getDriverId(), model.getOrder().getDspOrderId()) && notPushDriverByDuration(model.getDriver().getDriverId());
    }

    /**
     * 订单是否给该司机推送过进单提醒
     * @param driverId
     * @param dspOrderId
     * @return
     */
    public boolean notPushDriverByOrder(Long driverId, String dspOrderId) {
        try {
            String key = IdempotentConstants.IDEMPOTENT_CHECK_SEND_GRAB_CENTRE_MESSAGE_PREFIX + driverId + "_" + dspOrderId;
            Long expire = broadcastGrabConfig.getLong(ConfigKey.GRAB_MESSAGE_ORDER_IDEMPOTENT_SECONDS, 0L);
            if (expire <= 0) {
                return true;
            }
            return idempotentCheckService.isNotProcessed(key, expire);
        } catch (Exception e) {
            logger.error(e);
        }
        return false;
    }

    /**
     * 该司机一定时间内，是否被推送过进单提醒
     * @param driverId
     * @return
     */
    public boolean notPushDriverByDuration(Long driverId) {
        try {
            String key = IdempotentConstants.IDEMPOTENT_CHECK_SEND_GRAB_CENTRE_MESSAGE_PREFIX + driverId;
            Long expire = broadcastGrabConfig.getLong(ConfigKey.GRAB_MESSAGE_IDEMPOTENT_SECONDS, 0L);
            if (expire <= 0) {
                return true;
            }
            return idempotentCheckService.isNotProcessed(key, expire);
        } catch (Exception e) {
            logger.error(e);
        }
        return false;
    }

    protected List<GrabOrderDO> saveGrabOrder(ScheduleTaskDO task, DspOrderVO order, DuidVO duid, List<DspModelVO> models) {
        // 删除历史待抢订单
        grabCentreRepository.deleteAll(order.getDspOrderId());
        List<DriverVO> drivers = models.stream().map(DspModelVO::getDriver).collect(Collectors.toList());
        // 创建抢单他的订单
        List<GrabOrderDO> grabOrders = grabOrderFactory.create(order, duid, task.getSubSku(), drivers);
        grabCentreRepository.save(grabOrders);
        super.saveGrabOrderDetail(order.getDspOrderId(), drivers, task.getSubSku(), duid, grabOrders);
        return grabOrders;
    }
}
