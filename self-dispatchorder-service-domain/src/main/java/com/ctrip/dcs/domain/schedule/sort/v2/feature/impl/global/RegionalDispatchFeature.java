package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.global;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;

/**
 * 区域调度特征
 * 评估司机对区域调度效率的贡献
 * 
 * <AUTHOR> Assistant
 */
public class RegionalDispatchFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        DspOrderVO order = model.getModel().getOrder();
        
        // 计算区域调度得分
        double regionalScore = calculateRegionalDispatchScore(driver, order, context);
        
        return regionalScore;
    }
    
    /**
     * 计算区域调度得分
     * 
     * @param driver 司机信息
     * @param order 订单信息
     * @param context 排序上下文
     * @return 区域调度得分
     */
    private double calculateRegionalDispatchScore(DriverVO driver, DspOrderVO order, SortContext context) {
        // 实际实现中需要考虑：
        // 1. 区域供需平衡
        // 2. 司机在该区域的历史表现
        // 3. 区域调度策略
        // 4. 跨区域调度的成本和收益
        
        Integer cityId = order.getCityId();
        long driverId = driver.getDriverId();
        
        // 简化实现：基于城市和司机ID计算区域适配度
        double baseScore = 50.0;
        
        // 区域熟悉度加分
        boolean familiarWithRegion = (driverId + cityId) % 4 < 2;
        if (familiarWithRegion) {
            baseScore += 20.0;
        }
        
        // 区域供需状况调整（模拟）
        boolean highDemandRegion = cityId % 3 == 0;
        if (highDemandRegion) {
            baseScore += 15.0;
        }
        
        // 司机在该区域的历史表现（模拟）
        boolean goodPerformanceInRegion = (driverId % 5) < 3;
        if (goodPerformanceInRegion) {
            baseScore += 15.0;
        }
        
        return Math.min(100.0, baseScore);
    }
    
    @Override
    public String getFeatureName() {
        return "RegionalDispatch";
    }
    
    @Override
    public String getDescription() {
        return "区域调度特征：评估司机对区域调度效率的贡献，考虑区域熟悉度、供需状况等因素";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 0.6;
    }
}
