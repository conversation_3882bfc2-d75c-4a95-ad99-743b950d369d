package com.ctrip.dcs.domain.schedule.sort.v2.feature.impl.global;

import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.schedule.sort.SortContext;
import com.ctrip.dcs.domain.schedule.sort.SortModel;
import com.ctrip.dcs.domain.schedule.sort.v2.feature.AbstractFeatureV2;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.NormalizationStrategy;
import com.ctrip.dcs.domain.schedule.sort.v2.normalizer.impl.MinMaxNormalizer;
import com.ctrip.dcs.domain.schedule.value.DriverMileageProfitVO;

/**
 * 收益均衡特征
 * 对应现有的F13特征，评估司机的日收益均衡性
 * 
 * <AUTHOR> Assistant
 */
public class IncomeBalanceFeature extends AbstractFeatureV2 {
    
    @Override
    public double calculateSingle(SortModel model, SortContext context) {
        DriverVO driver = model.getModel().getDriver();
        Integer cityId = model.getModel().getOrder().getCityId();
        Long carTypeId = driver.getCarTypeId();
        
        // 获取司机今日收益
        DriverMileageProfitVO todayProfit = context.getTodayDriverMileageProfit(driver.getDriverId());
        double driverDayProfit = 0.0;
        
        if (todayProfit != null && todayProfit.getProfit() != null) {
            driverDayProfit = todayProfit.getProfit().doubleValue();
        }
        
        // 获取日基线收益
        Integer baselineProfit = context.getProfitBaselineDay(cityId, carTypeId);
        double dayBaseline = safeGetDouble(baselineProfit != null ? baselineProfit.doubleValue() : null, 300.0);
        
        // 计算收益均衡得分
        double balanceScore;
        
        if (driverDayProfit > dayBaseline) {
            // 如果司机日收益 > 日基线收益，则得分为0（不需要优先派单）
            balanceScore = 0.0;
        } else {
            // 否则，得分 = 日收益/日基线收益
            balanceScore = dayBaseline > 0 ? (driverDayProfit / dayBaseline) : 0.0;
        }
        
        // 转换为百分制，并取反（收益越低，优先级越高）
        return (1.0 - balanceScore) * 100;
    }
    
    @Override
    public String getFeatureName() {
        return "IncomeBalance";
    }
    
    @Override
    public String getDescription() {
        return "收益均衡特征：评估司机的日收益均衡性，优先给收益较低的司机派单以实现收益均衡";
    }
    
    @Override
    public NormalizationStrategy getNormalizationStrategy() {
        // 使用正向归一化，值越大得分越高
        return new MinMaxNormalizer(true);
    }
    
    @Override
    public double getWeight() {
        return 0.75; // 对应原F13的权重
    }
}
