package com.ctrip.dcs.domain.schedule.sort.v2.weight;

import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;

/**
 * 权重策略接口
 * 支持根据不同场景动态调整权重
 * 
 * <AUTHOR> Assistant
 */
public interface WeightStrategy {
    
    /**
     * 根据订单和子产品信息获取权重配置
     * 
     * @param order 订单信息
     * @param subSku 子产品信息
     * @return 权重配置
     */
    CategoryWeight getWeight(DspOrderVO order, SubSkuVO subSku);
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
    
    /**
     * 获取策略描述
     * 
     * @return 策略描述
     */
    String getDescription();
}
