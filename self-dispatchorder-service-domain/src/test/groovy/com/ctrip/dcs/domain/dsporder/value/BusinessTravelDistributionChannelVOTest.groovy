package com.ctrip.dcs.domain.dsporder.value


import spock.lang.Specification
import spock.lang.Unroll

class BusinessTravelDistributionChannelVOTest extends Specification {
    BusinessTravelDistributionChannelVO businessTravelDistributionChannelVO = new BusinessTravelDistributionChannelVO()

    def "test new Instance"() {
        when:
        DistributionChannelVO result = BusinessTravelDistributionChannelVO.newInstance()

        then:
        result instanceof BusinessTravelDistributionChannelVO
    }

    @Unroll
    def "test business Travel Black List"() {
        when:
        List<String> result = businessTravelDistributionChannelVO.businessTravelBlackList(querySupplier)

        then:
        result.size() == size

        where:
        querySupplier || size
        null          || 0
//        { -> [] }     || 0
//        { -> ["1"] }  || 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme