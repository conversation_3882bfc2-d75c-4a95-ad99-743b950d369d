package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.GradientDriverNewFeature
import com.ctrip.dcs.domain.schedule.value.DriverAttendanceVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.google.common.collect.Maps
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class DriverGradientFeatureNewSortTest extends Specification {
    @Mock
    SortModel model
    @Mock
    SortContext context
    @Mock
    DriverVO driver
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverMileageProfitVO dmp
    @Mock
    CarVO carVO
    @Mock
    PeriodsVO periodsVO

    @InjectMocks
    GradientDriverNewFeature feature = new GradientDriverNewFeature()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(model.getModel()).thenReturn(dspModelVO)
        Mockito.when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getDspOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getExpectDriverMileageProfit(Mockito.any())).thenReturn(dmp)
        Mockito.when(context.getTodayDriverMileageProfit(Mockito.any())).thenReturn(dmp)
        Mockito.when(context.getCompleteDriverMileageProfit(Mockito.any())).thenReturn(dmp)
        Mockito.when(dspModelVO.getDriver()).thenReturn(driver)
        Mockito.when(driver.getDriverId()).thenReturn(1L)
        Mockito.when(driver.getCar()).thenReturn(carVO)
        Mockito.when(carVO.getCarTypeId()).thenReturn(117L)
        Mockito.when(driver.getWorkTimes()).thenReturn(periodsVO)
        Mockito.when(periodsVO.getPeriodStrs()).thenReturn(Lists.newArrayList("10:00~12:00", "12:00~14:00", "14:00~16:00"))
        Mockito.when(dspOrderVO.getEstimatedUseTime()).thenReturn(new Date())
        Mockito.when(context.getOrderList(Mockito.any())).thenReturn([dspOrderVO])

    }

    @Unroll
    def "test value"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(context.getProfitBaselineMonth(Mockito.any(), Mockito.any())).thenReturn(100)
        Mockito.when(context.getProfitBaselineDay(Mockito.any(), Mockito.any())).thenReturn(99)
        Mockito.when(context.getProperties(Mockito.anyString(), Mockito.anyString())).thenReturn("1:200,2:200")
        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)


        when:
        Value res = feature.value(model, context)

        then:
        res.value > 0.0 == true


    }

    @Unroll
    def "test value1"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()
        DriverAttendanceVO daa =  new DriverAttendanceVO(driverId: 1L,attendance:22D)

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(context.getProfitBaselineMonth(Mockito.any(), Mockito.any())).thenReturn(100)
        Mockito.when(context.getProfitBaselineDay(Mockito.any(), Mockito.any())).thenReturn(99)
        Mockito.when(context.getProperties(Mockito.anyString(), Mockito.anyString())).thenReturn("1:200,2:200")
        Mockito.when(context.getTodayDriverMileageProfit(Mockito.any())).thenReturn(null)

        Mockito.when(context.getDriverAttendance(Mockito.any())).thenReturn(Optional.ofNullable(daa))
//        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)


        when:
        Value res = feature.value(model, context)

        then:
        res.value > 0.0 == true


    }

    @Unroll
    def "test value5"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()
        DriverAttendanceVO daa =  new DriverAttendanceVO(driverId: 1L,attendance:22D)

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(null)
        Mockito.when(context.getProfitBaselineMonth(Mockito.any(), Mockito.any())).thenReturn(100)
        Mockito.when(context.getProfitBaselineDay(Mockito.any(), Mockito.any())).thenReturn(99)
        Mockito.when(context.getProperties(Mockito.anyString(), Mockito.anyString())).thenReturn("1:200,2:200")
        Mockito.when(context.getTodayDriverMileageProfit(Mockito.any())).thenReturn(dmp)

        Mockito.when(context.getDriverAttendance(Mockito.any())).thenReturn(Optional.ofNullable(daa))
        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)


        when:
        Value res = feature.value(model, context)

        then:
        res.value > 0.0 == true


    }

    @Unroll
    def "test value6"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()
        DriverAttendanceVO daa =  new DriverAttendanceVO(driverId: 1L,attendance:22D)

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(context.getProfitBaselineMonth(Mockito.any(), Mockito.any())).thenReturn(100)
        Mockito.when(context.getProfitBaselineDay(Mockito.any(), Mockito.any())).thenReturn(99)
        Mockito.when(context.getProperties(Mockito.anyString(), Mockito.anyString())).thenReturn("1:200,2:200")
        Mockito.when(context.getTodayDriverMileageProfit(Mockito.any())).thenReturn(dmp)

        Mockito.when(context.getDriverAttendance(Mockito.any())).thenReturn(Optional.ofNullable(daa))
        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)


        when:
        Value res = feature.value(model, context)

        then:
        res.value > 0.0 == true


    }

    @Unroll
    def "test value4"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(context.getProfitBaselineMonth(Mockito.any(), Mockito.any())).thenReturn(100)
        Mockito.when(context.getProfitBaselineDay(Mockito.any(), Mockito.any())).thenReturn(99)
        Mockito.when(context.getProperties(Mockito.any(), Mockito.any())).thenReturn("1:200,2:200")

        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)
        Map<String,Object> valueDetailMap = Maps.newHashMap()


        when:
        boolean res = feature.virtualProfitOver(dspOrderVO, driver, context, dmp, 10,valueDetailMap)

        then:
        res == true


    }

    @Unroll
    def "test value2"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(context.getProfitBaselineMonth(Mockito.any(), Mockito.any())).thenReturn(100)
        Mockito.when(context.getProfitBaselineDay(Mockito.any(), Mockito.any())).thenReturn(99)
        Mockito.when(context.getProperties(Mockito.any(), Mockito.any())).thenReturn("1:200,2:200")

        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)
        Mockito.when(context.getOrderList(Mockito.any())).thenReturn(null)
        Map<String,Object> valueDetailMap = Maps.newHashMap()


        when:
        boolean res = feature.virtualProfitOver(dspOrderVO, driver, context, dmp, 10,valueDetailMap)

        then:
        res == true


    }

    @Unroll
    def "test value3"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(context.getProfitBaselineMonth(Mockito.any(), Mockito.any())).thenReturn(100)
        Mockito.when(context.getProfitBaselineDay(Mockito.any(), Mockito.any())).thenReturn(99)
        Mockito.when(context.getProperties(Mockito.any(), Mockito.any())).thenReturn("1:200,2:200")

        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)
        Mockito.when(context.getOrderList(Mockito.any())).thenReturn([dspOrderVO])


        when:
        boolean res = feature.calculateTimeInterval([dspOrderVO,dspOrderVO], new Date(), DateUtil.addHours(new Date(),360))

        then:
        res == true


    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme