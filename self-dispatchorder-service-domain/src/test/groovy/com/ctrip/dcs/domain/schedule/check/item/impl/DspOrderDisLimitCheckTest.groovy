package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.OrderDisLimitVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.Mockito.mock

/**
 * <AUTHOR>
 * @date 2023/4/20 11:26
 * @version V1.0
 */
class DspOrderDisLimitCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    OrderDisLimitVO disLimit
    @Mock
    DspContext dspContext
    @Mock
    DspContextService dspContextService
    @InjectMocks
    DspOrderDisLimitCheck dspOrderDisLimitCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "司机满足长短公司指派规则-接单检查"() {

        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.TAKEN)

        Map<Long, OrderDisLimitVO> drvMapDisModel = Maps.newHashMap()
        drvMapDisModel.put(1L, disLimit)
        Mockito.when(context.getOrderDisLimitMap()).thenReturn(drvMapDisModel)
        Mockito.when(disLimit.getHadTakenNum()).thenReturn(8)
        Mockito.when(disLimit.getTakenLimit()).thenReturn(10)
        Mockito.when(disLimit.getOrderLen()).thenReturn(12.0D)
        Mockito.when(disLimit.getBookTime()).thenReturn(new Date())
        Mockito.when(context.getContext()).thenReturn(dspContext)
        Mockito.when(dspContext.getService()).thenReturn(dspContextService)

        when:
        CheckCode result = dspOrderDisLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机满足长短公司指派规则"() {

        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)

        Map<Long, OrderDisLimitVO> drvMapDisModel = Maps.newHashMap()
        drvMapDisModel.put(1L, disLimit)
        Mockito.when(context.getOrderDisLimitMap()).thenReturn(drvMapDisModel)
        Mockito.when(disLimit.getHadTakenNum()).thenReturn(8)
        Mockito.when(disLimit.getTakenLimit()).thenReturn(10)
        Mockito.when(disLimit.getOrderLen()).thenReturn(12.0D)
        Mockito.when(disLimit.getBookTime()).thenReturn(new Date())

        when:
        CheckCode result = dspOrderDisLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机不满足长短公司指派规则"() {

        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)

        Map<Long, OrderDisLimitVO> drvMapDisModel = Maps.newHashMap()
        drvMapDisModel.put(1L, disLimit)
        Mockito.when(context.getOrderDisLimitMap()).thenReturn(drvMapDisModel)
        Mockito.when(disLimit.getHadTakenNum()).thenReturn(12)
        Mockito.when(disLimit.getTakenLimit()).thenReturn(10)
        Mockito.when(disLimit.getOrderLen()).thenReturn(12.0D)
        Mockito.when(disLimit.getBookTime()).thenReturn(new Date())

        when:
        CheckCode result = dspOrderDisLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.DSP_ORDER_DIS_NUM_LIMIT
    }

    def "test check"() {
        given:
        def dspOrderVo = Mock(DspOrderVO)
        CheckModel checkModel = new CheckModel(model: new DspModelVO(order: dspOrderVo, driver: new DriverVO(driverId: 1L, car: new CarVO(carTypeId: 2L))))


        when:
        def result = dspOrderDisLimitCheck.check(checkModel, checkContext)
        then:
        result == checkCode

        where:
        checkContext       || checkCode
        getCheckContext(1) || CheckCode.PASS
        getCheckContext(2) || CheckCode.DSP_ORDER_DIS_NUM_LIMIT
        getCheckContext(3) || CheckCode.PASS
    }

    def getCheckContext(int num) {
        if (num == 1) {
            return Mock(CheckContext.class);
        }
        def checkContext = new CheckContext(mock(DspContext.class), mock(ConfigService.class), mock(SubSkuVO.class), mock(DspOrderVO), null, null, DspStage.DSP, [], null, null);
        if (num == 2) {
            checkContext.getOrderDisLimitMap().put(1L, new OrderDisLimitVO(hadTakenNum: 10, takenLimit: 10,orderLen: 12.0,bookTime: new Date()))
            return checkContext;
        }
        if (num == 3) {
            checkContext.getOrderDisLimitMap().put(1L, new OrderDisLimitVO(hadTakenNum: 10, takenLimit: 11,orderLen: 12.0,bookTime: new Date()))
            return checkContext;
        }
    }

    def "test downgrade"() {
        when:
        CheckCode result = dspOrderDisLimitCheck.downgrade()
        then:
        result == CheckCode.PASS
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
