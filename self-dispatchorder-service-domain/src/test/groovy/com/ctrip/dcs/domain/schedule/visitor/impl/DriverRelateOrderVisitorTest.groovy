package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverRelateOrderVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    SortContext sortContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driverVO
    @Mock
    QueryDspOrderService queryDspOrderService
    @InjectMocks
    DriverRelateOrderVisitor driverRelateOrderVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        int i = 10
        when(checkContext.getContext()).thenReturn(dspContext)
        when(queryDspOrderService.queryRelateOrders(any(), any(), anyInt(),any())).thenReturn([(1l): new DriverRelateOrderVO()])
        DriverRelateOrderVisitor driverRelateOrderVisitor = new DriverRelateOrderVisitor(dspOrder, [driverVO], 1, queryDspOrderService);

        when:
        driverRelateOrderVisitor.visit(checkContext)

        then:
        i>0
    }

    def "test visit 2"() {
        given:
        int i = 10
        when(sortContext.getDspContext()).thenReturn(dspContext)
        when(queryDspOrderService.queryRelateOrders(any(), any(), anyInt(),any())).thenReturn([(1l): new DriverRelateOrderVO()])
        DriverRelateOrderVisitor driverRelateOrderVisitor = new DriverRelateOrderVisitor(dspOrder, [driverVO], 1, queryDspOrderService);

        when:
        driverRelateOrderVisitor.visit(sortContext)

        then:
        i>0
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme