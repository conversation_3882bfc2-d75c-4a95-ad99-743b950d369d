package com.ctrip.dcs.domain.common.value

import org.apache.commons.collections.CollectionUtils
import org.assertj.core.util.Lists
import org.junit.Assert
import spock.lang.Specification

/**
 * <AUTHOR>
 * @since 2025/4/30 16:52
 */
class DriverGuidVehicleRequestVOTest extends Specification {
    def testObj = new DriverGuidVehicleRequestVO()
    def vehicleStatusList = Mock(List)

    def setup() {
        testObj.vehicleStatusList = vehicleStatusList
    }

    def "test"() {
        given:

        when:
        DriverGuidVehicleRequestVO driverGuidVehicleRequestVO = new DriverGuidVehicleRequestVO();
        driverGuidVehicleRequestVO.setSupplierId(11L);
        driverGuidVehicleRequestVO.setHasDrv(1);
        driverGuidVehicleRequestVO.setVehicleStatusList(Lists.newArrayList(1));

        then:
        Assert.assertTrue(Objects.equals(driverGuidVehicleRequestVO.getSupplierId(), 11L));
        Assert.assertTrue(Objects.equals(driverGuidVehicleRequestVO.getHasDrv(), 1));
        Assert.assertTrue(CollectionUtils.isNotEmpty(driverGuidVehicleRequestVO.getVehicleStatusList()));
    }
}
