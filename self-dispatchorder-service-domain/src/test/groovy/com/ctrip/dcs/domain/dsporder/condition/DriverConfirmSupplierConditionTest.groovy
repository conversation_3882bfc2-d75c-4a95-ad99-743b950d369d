package com.ctrip.dcs.domain.dsporder.condition

import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.dsporder.entity.*
import com.ctrip.dcs.domain.dsporder.gateway.RhbSettlementRuleGateway
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusContext
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent
import com.ctrip.dcs.domain.dsporder.logic.PlatformPriceStrategyLogic
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.PlatformPriceStrategyRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class DriverConfirmSupplierConditionTest extends Specification {

    def context = Mock(OrderStatusContext)


    def executor = new DriverConfirmSupplierCondition(


    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"
        context.getGreyFlag() >> greyFlag
        context.getDspOrder() >> new DspOrderDO(lastConfirmCarTimeBj: Timestamp.valueOf("2024-01-01 00:00:00"), lastConfirmTimeBj: Timestamp.valueOf("2024-01-01 00:00:00"),estimatedUseTimeBj: Timestamp.valueOf("2024-01-01 00:00:00"))


        when: "执行校验方法"
        def res = executor.isMatch(from, event, to,context)

        then: "验证校验结果"
        res == code

        where:
        greyFlag | from | event | to   || code
        null| OrderStatusEnum.DISPATCH_CONFIRMED| OrderStatusEvent.SYSTEM_ASSIGN | OrderStatusEnum.DISPATCH_CONFIRMED || false
        false| OrderStatusEnum.DISPATCH_CONFIRMED| OrderStatusEvent.SYSTEM_ASSIGN | OrderStatusEnum.DISPATCH_CONFIRMED || false
        true| OrderStatusEnum.SERVICE_PROVIDER_CONFIRMED| OrderStatusEvent.SYSTEM_ASSIGN | OrderStatusEnum.DISPATCH_CONFIRMED || false
        true| OrderStatusEnum.DRIVER_SERVICE_END| OrderStatusEvent.SYSTEM_ASSIGN | OrderStatusEnum.DISPATCH_CONFIRMED || false
        true| OrderStatusEnum.DISPATCH_CONFIRMED| OrderStatusEvent.SYSTEM_ASSIGN | OrderStatusEnum.DISPATCH_CONFIRMED || false
        true| OrderStatusEnum.DISPATCH_CONFIRMED| OrderStatusEvent.SYSTEM_ASSIGN | OrderStatusEnum.DISPATCH_CONFIRMED || false

    }


}
