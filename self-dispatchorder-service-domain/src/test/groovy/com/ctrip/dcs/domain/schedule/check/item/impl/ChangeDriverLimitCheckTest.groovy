package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.carconfig.DriverChangeLimitConfig
import com.ctrip.dcs.domain.dsporder.repository.DspOrderChangeDriverRecordRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.visitor.impl.DriverChangeVisitor
import com.ctrip.igt.framework.common.spring.InstanceLocator
import org.springframework.context.ApplicationContext
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Field

class ChangeDriverLimitCheckTest extends Specification {
    def context = Mock(CheckContext)
    def model = Mock(CheckModel)
    def dspModelVO = Mock(DspModelVO)
    def order = Mock(DspOrderVO)
    def driver = Mock(DriverVO)
    def driverChangeLimitConfig = Mock(DriverChangeLimitConfig)
    def queryDspOrderService = Mock(QueryDspOrderService)
    def dspOrderChangeDriverRecordRepository = Mock(DspOrderChangeDriverRecordRepository)
    ChangeDriverLimitCheck check
    ApplicationContext appContext

    def setup() {
        // 创建 Spy 对象前先设置静态字段
        appContext = Mock(ApplicationContext)
        InstanceLocator.applicationContext = appContext

        // 配置InstanceLocator返回我们的mock对象
        appContext.getBean(DriverChangeLimitConfig.class) >> driverChangeLimitConfig
        appContext.getBean(QueryDspOrderService.class) >> queryDspOrderService
        appContext.getBean(DspOrderChangeDriverRecordRepository.class) >> dspOrderChangeDriverRecordRepository

        // 所有其他类型的 getBean 都返回非空对象，防止错误
        appContext.getBean(_) >> Mock(Object)

        // 创建被测类实例
        check = new ChangeDriverLimitCheck()

        // 模拟基本属性
        context.getDspOrder() >> order
        order.getCityId() >> 100
        order.getSupplierId() >> 200
        order.getConfirmRecordId() >> 999L

        // 模拟模型数据
        dspModelVO.getDriver() >> driver
        driver.getDriverId() >> 12345L
        model.getModel() >> dspModelVO

        // 模拟Map参数
        context.getDriverChangeOutCountMap() >> [1001L: 1]
        context.getDriverChangeInCountMap() >> [12345L: 1]
    }

    def cleanup() {
        // 测试结束后清空静态字段
        InstanceLocator.applicationContext = null
    }

    // 帮助方法：使用反射设置私有字段
    void setFieldValue(Object obj, String fieldName, Object value) {
        Field field = obj.getClass().getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(obj, value)
    }

    def "needCheck should return false when both limits are 0"() {
        given:
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 0
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 0

        when:
        def result = check.needCheck(100, 200, 1L)

        then:
        !result
        0 * queryDspOrderService.queryDspOrderConfirmRecord(_) // 不应该查询确认记录
    }

    def "needCheck should return false when confirmId is null"() {
        given:
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 3
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 2

        when:
        def result = check.needCheck(100, 200, null)

        then:
        !result
        0 * queryDspOrderService.queryDspOrderConfirmRecord(_) // 不应该查询确认记录
    }

    def "needCheck should return false when confirmId is 0"() {
        given:
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 3
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 2

        when:
        def result = check.needCheck(100, 200, 0L)

        then:
        !result
        0 * queryDspOrderService.queryDspOrderConfirmRecord(_) // 不应该查询确认记录
    }

    def "needCheck should return false when confirmRecord is null"() {
        given:
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 3
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 2
        queryDspOrderService.queryDspOrderConfirmRecord(999L) >> null

        when:
        def result = check.needCheck(100, 200, 999L)

        then:
        !result
        1 * queryDspOrderService.queryDspOrderConfirmRecord(999L)
    }

    def "needCheck should return false when driverInfo is null"() {
        given:
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 3
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 2

        def confirmRecord = new DspOrderConfirmRecordVO()
        // 没有设置driverInfo

        queryDspOrderService.queryDspOrderConfirmRecord(999L) >> confirmRecord

        when:
        def result = check.needCheck(100, 200, 999L)

        then:
        !result
        1 * queryDspOrderService.queryDspOrderConfirmRecord(999L)
    }

    def "needCheck should return false when driverId is null"() {
        given:
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 3
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 2

        def confirmRecord = new DspOrderConfirmRecordVO()
        def driverRecord = new DspOrderConfirmRecordVO.DriverRecord()
        driverRecord.setDriverId(null) // 设置driverId为null
        confirmRecord.setDriverInfo(driverRecord)

        queryDspOrderService.queryDspOrderConfirmRecord(999L) >> confirmRecord

        when:
        def result = check.needCheck(100, 200, 999L)

        then:
        !result
    }

    def "needCheck should return false when driverId is 0"() {
        given:
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 3
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 2

        def confirmRecord = new DspOrderConfirmRecordVO()
        def driverRecord = new DspOrderConfirmRecordVO.DriverRecord()
        driverRecord.setDriverId(0L) // 设置driverId为0
        confirmRecord.setDriverInfo(driverRecord)

        queryDspOrderService.queryDspOrderConfirmRecord(999L) >> confirmRecord

        when:
        def result = check.needCheck(100, 200, 999L)

        then:
        !result
    }

    def "needCheck should return true when all conditions are met"() {
        given:
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 3
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 2

        def confirmRecord = new DspOrderConfirmRecordVO()
        def driverRecord = new DspOrderConfirmRecordVO.DriverRecord()
        driverRecord.setDriverId(1001L) // 有效的driverId
        confirmRecord.setDriverInfo(driverRecord)

        queryDspOrderService.queryDspOrderConfirmRecord(999L) >> confirmRecord

        when:
        def result = check.needCheck(100, 200, 999L)

        then:
        result
        // 验证driverId被正确设置
        check.driverId == 1001L
    }

    def "load should not call accept if needCheck is false"() {
        given:
        // 配置needCheck返回false的条件
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 0
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 0

        when:
        check.load([model], context)

        then:
        // 不应该调用accept
        0 * context.accept(_)
    }

    def "load should call accept if needCheck is true"() {
        given:
        // 配置needCheck返回true的条件
        driverChangeLimitConfig.getDriverChangeOutLimit(100, 200) >> 3
        driverChangeLimitConfig.getDriverChangeInLimit(100, 200) >> 2

        def confirmRecord = new DspOrderConfirmRecordVO()
        def driverRecord = new DspOrderConfirmRecordVO.DriverRecord()
        driverRecord.setDriverId(1001L)
        confirmRecord.setDriverInfo(driverRecord)

        queryDspOrderService.queryDspOrderConfirmRecord(999L) >> confirmRecord

        when:
        check.load([model], context)

        then:
        1 * context.accept({ it instanceof DriverChangeVisitor })
    }

    def "check returns PASS if needCheck is false"() {
        given:
        // 直接设置needCheck为false
        setFieldValue(check, "needCheck", false)

        when:
        def result = check.check(model, context)

        then:
        result == CheckCode.PASS
    }

    @Unroll
    def "check returns correct code when outTimes=#outTimes outLimit=#outLimit inTimes=#inTimes inLimit=#inLimit"() {
        given:
        // 设置测试参数
        setFieldValue(check, "needCheck", true)
        setFieldValue(check, "driverId", 1001L)
        setFieldValue(check, "resForOut", outLimit)
        setFieldValue(check, "resForIn", inLimit)

        // 提供一个默认值，避免NPE
        def outMap = [:]
        if (outTimes != null) {
            outMap.put(1001L, outTimes)
        }

        def inMap = [:]
        if (inTimes != null) {
            inMap.put(12345L, inTimes)
        }

        // 模拟Map返回值
        context.getDriverChangeOutCountMap() >> outMap
        context.getDriverChangeInCountMap() >> inMap

        when:
        def result = check.check(model, context)

        then:
        result == expectedCode

        where:
        outTimes | outLimit | inTimes | inLimit | expectedCode
        1        | 3        | 1        | 3       | CheckCode.PASS
        0        | 0        | 0        | 0       | CheckCode.PASS
    }

    def "check handles null values in map gracefully"() {
        given:
        setFieldValue(check, "needCheck", true)
        setFieldValue(check, "driverId", 1001L)
        setFieldValue(check, "resForOut", 2)
        setFieldValue(check, "resForIn", 2)

        // 模拟Map返回空
        context.getDriverChangeOutCountMap() >> [:]
        context.getDriverChangeInCountMap() >> [:]

        when:
        def result = check.check(model, context)

        then:
        result == CheckCode.PASS
        noExceptionThrown()
    }

    def "downgrade always returns PASS"() {
        expect:
        check.downgrade() == CheckCode.PASS
    }
}