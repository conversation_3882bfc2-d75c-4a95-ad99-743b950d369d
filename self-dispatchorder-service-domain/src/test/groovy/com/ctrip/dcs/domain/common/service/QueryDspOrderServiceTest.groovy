package com.ctrip.dcs.domain.common.service

import com.ctrip.dcs.domain.common.enums.DspStage
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class QueryDspOrderServiceTest extends Specification {

    def "GetServiceName"() {
        when:
        String name = QueryDspOrderService.getServiceName(stage)
        then:
        name == service
        where:
        stage          || service
        DspStage.DSP   || "queryDspOrderCacheServiceImpl"
        DspStage.TAKEN || "queryDspOrderServiceImpl"
    }
}
