package com.ctrip.dcs.domain.schedule.check

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.item.Check
import com.ctrip.dcs.domain.schedule.check.source.CheckSource
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class CheckChainTest extends Specification{

    def order = new DspOrderVO()

    def driver = new DriverVO()

    def dspModel = new DspModelVO(order, driver)

    def checkModel = new CheckModel(dspModel, CheckCode.PASS)

    def source = new CheckSource() {

        @Override
        List<CheckModel> init(CheckContext context) {
            return Lists.newArrayList(checkModel)
        }

        @Override
        CheckModel taken(CheckContext context) {
            return checkModel
        }
    }

    def item = new Check() {
        @Override
        List<CheckModel> check(List<CheckModel> models, CheckContext context) {
            return Lists.newArrayList(checkModel)
        }
    }

    def checkContext = Mock(CheckContext)

    @Unroll
    def "test check"() {
        def chain = new CheckChain(stage)
        chain.addSource(source)
        chain.addCheck(item)
        when: "执行校验方法"
        List<CheckModel> pass = chain.check(checkContext)

        then: "验证校验结果"
        pass != null
        pass.size() == 1
        pass.get(0).getCheckCode() == code

        where:
        stage          || code
        DspStage.DSP   || CheckCode.PASS
        DspStage.TAKEN || CheckCode.PASS
    }
}
