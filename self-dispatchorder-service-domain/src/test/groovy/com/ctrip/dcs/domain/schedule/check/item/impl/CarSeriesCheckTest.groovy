package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.AppointCarType
import com.ctrip.dcs.domain.dsporder.value.SeriesVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.exception.CheckItemException
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class CarSeriesCheckTest extends Specification {
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    SeriesVO seriesVO
    @InjectMocks
    CarSeriesCheck carSeriesCheck = new CarSeriesCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "指定车型，车系包含"() {
        given:
        Map<String, SeriesVO> map = Maps.newHashMap()
        map.put("1", seriesVO)
        Mockito.when(order.getCategoryCode()).thenReturn(CategoryCodeEnum.DAY_RENTAL)
        Mockito.when(order.getAppointCarType()).thenReturn(AppointCarType.DESIGNATED_VEHICLE)
        Mockito.when(car.getCarSeriesId()).thenReturn(1L)
        Mockito.when(context.getCarSeriesMap()).thenReturn(map)
        Mockito.when(seriesVO.contains(Mockito.any())).thenReturn(true)

        when:
        CheckCode result = carSeriesCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "指定车型，车系不包含"() {
        given:
        Map<String, SeriesVO> map = Maps.newHashMap()
        map.put("1", seriesVO)
        Mockito.when(order.getCategoryCode()).thenReturn(CategoryCodeEnum.DAY_RENTAL)
        Mockito.when(order.getAppointCarType()).thenReturn(AppointCarType.DESIGNATED_VEHICLE)
        Mockito.when(car.getCarSeriesId()).thenReturn(1L)
        Mockito.when(context.getCarSeriesMap()).thenReturn(map)
        Mockito.when(seriesVO.contains(Mockito.any())).thenReturn(false)

        when:
        CheckCode result = carSeriesCheck.check(checkModel, context)

        then:
        result == CheckCode.APPOINT_VEHICLE_SERIES_DRIVER
    }

    def "非指定车型"() {
        given:
        Map<String, SeriesVO> map = Maps.newHashMap()
        Mockito.when(order.getCategoryCode()).thenReturn(CategoryCodeEnum.DAY_RENTAL)
        Mockito.when(order.getAppointCarType()).thenReturn(AppointCarType.ORDINARY_MODEL)
        Mockito.when(car.getCarSeriesId()).thenReturn(1L)
        Mockito.when(context.getCarSeriesMap()).thenReturn(map)

        when:
        CheckCode result = carSeriesCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    @Unroll
    def "test check"() {
        given:
        Mockito.when(car.getCarSeriesId()).thenReturn(1L)
        Mockito.when(order.getAppointCarType()).thenReturn(appointCarType)
        Mockito.when(order.getCategoryCode()).thenReturn(categoryCode)
        Mockito.when(context.getCarSeriesMap()).thenReturn(orderSeries)
        Mockito.when(order.getDspOrderId()).thenReturn("1")

        when:
        CheckCode result = carSeriesCheck.check(checkModel, context)

        then:
        result == r

        where:
        categoryCode                  | appointCarType                    | orderSeries                                    || r
        null                          | null                              | null                                           || CheckCode.PASS
        CategoryCodeEnum.FROM_AIRPORT | null                              | null                                           || CheckCode.PASS
        CategoryCodeEnum.DAY_RENTAL   | null                              | null                                           || CheckCode.PASS
        CategoryCodeEnum.DAY_RENTAL   | AppointCarType.ORDINARY_MODEL     | null                                           || CheckCode.PASS
        CategoryCodeEnum.DAY_RENTAL   | AppointCarType.DESIGNATED_VEHICLE | null                                           || CheckCode.APPOINT_VEHICLE_SERIES_DRIVER
        CategoryCodeEnum.DAY_RENTAL   | AppointCarType.DESIGNATED_VEHICLE | ["1": new SeriesVO(null)]                      || CheckCode.APPOINT_VEHICLE_SERIES_DRIVER
        CategoryCodeEnum.DAY_RENTAL   | AppointCarType.DESIGNATED_VEHICLE | ["1": new SeriesVO([2L, 3L] as Set<Long>)]     || CheckCode.APPOINT_VEHICLE_SERIES_DRIVER
        CategoryCodeEnum.DAY_RENTAL   | AppointCarType.DESIGNATED_VEHICLE | ["1": new SeriesVO([1L, 2L, 3L] as Set<Long>)] || CheckCode.PASS
    }

    def "test downgrade"() {
        when:
        CheckCode result = carSeriesCheck.downgrade()

        then:
        result == CheckCode.APPOINT_VEHICLE_SERIES_DRIVER
    }

    def "test check 2"() {
        given:
        DspOrderVO order = new DspOrderVO()
        DriverVO driver = new DriverVO(
                car: new CarVO()
        )
        DspModelVO model = new DspModelVO(
                order: order,
                driver: driver
        )
        CheckModel checkModel = new CheckModel(
                model: model
        )

        CheckContext context = Mock()

        when:
        carSeriesCheck.check(checkModel, context)

        then:
        thrown(CheckItemException)
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme