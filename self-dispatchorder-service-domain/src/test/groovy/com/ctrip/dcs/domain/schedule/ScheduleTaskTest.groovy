package com.ctrip.dcs.domain.schedule

import cn.hutool.core.math.Money
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.value.RewardVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import org.apache.commons.lang3.math.NumberUtils
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class ScheduleTaskTest extends Specification {

    @Unroll
    def "test schedule task to execute"() {

        given: "Mock数据"
        def order = DspOrderVO.builder().estimatedUseTimeBj(new Timestamp(System.currentTimeMillis())).lastConfirmTimeBj(new Timestamp(System.currentTimeMillis())).categoryCode(CategoryCodeEnum.FROM_AIRPORT).build()
        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(30).build()
        def task = ScheduleTaskDO.builder()
                .taskId(1)
                .status( ScheduleTaskStatus.INIT)
                .priority(1)
                .scheduleId(1L)
                .dspOrderId("1")
                .subSku(subSku)
                .begin(0)
                .end(0)
                .round(0)
                .limit(0)
                .reward(new Money())
                .createTime(new Date())
                .executeTime(new Date()).build()


        when: "执行校验方法"
        task.execute()
        boolean isExecute = task.isExecute()

        then: "验证校验结果"
        isExecute
        task.getExecuteTime() != null
    }

    @Unroll
    def "test schedule task to wait execute"() {

        given: "Mock数据"
        def order = DspOrderVO.builder().estimatedUseTimeBj(new Timestamp(System.currentTimeMillis())).lastConfirmTimeBj(new Timestamp(System.currentTimeMillis())).categoryCode(CategoryCodeEnum.FROM_AIRPORT).build()
        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(30).roundRetryFactor([]).build()
        def task = ScheduleTaskDO.builder()
                .taskId(1)
                .status( ScheduleTaskStatus.INIT)
                .priority(1)
                .scheduleId(1L)
                .dspOrderId("1")
                .subSku(subSku)
                .begin(0)
                .end(0)
                .round(0)
                .limit(0)
                .reward(new Money())
                .createTime(new Date())
                .executeTime(new Date()).build()


        when: "执行校验方法"
        task.waitExecute(order, new RewardVO(BigDecimal.TEN, "", []), 30)
        boolean isWaitExecute = task.isWaitExecute()

        then: "验证校验结果"
        isWaitExecute
        task.getRound() == 1
        task.getReward() == new Money(BigDecimal.ZERO)
        task.getDspRewardStrategyId() == NumberUtils.LONG_ZERO
    }

    @Unroll
    def "test schedule task cancel"() {

        given: "Mock数据"
        def order = DspOrderVO.builder().estimatedUseTimeBj(new Timestamp(System.currentTimeMillis())).lastConfirmTimeBj(new Timestamp(System.currentTimeMillis())).categoryCode(CategoryCodeEnum.FROM_AIRPORT).build()
        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(30).build()
        def task = ScheduleTaskDO.builder()
                .taskId(1)
                .status( ScheduleTaskStatus.INIT)
                .priority(1)
                .scheduleId(1L)
                .dspOrderId("1")
                .subSku(subSku)
                .begin(0)
                .end(0)
                .round(0)
                .limit(0)
                .reward(new Money())
                .createTime(new Date())
                .executeTime(new Date()).build()


        when: "执行校验方法"
        task.cancel()
        boolean isCancel = task.isCancel()

        then: "验证校验结果"
        isCancel
        task.getCancelTime() != null
    }
}
