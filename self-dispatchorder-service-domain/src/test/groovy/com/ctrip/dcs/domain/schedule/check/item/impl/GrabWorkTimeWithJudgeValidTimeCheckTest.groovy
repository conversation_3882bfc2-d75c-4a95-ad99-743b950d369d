package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.EstimatedUseTimeVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.carconfig.DelayDspConfigVO
import org.mockito.Mockito
import spock.lang.Specification

class GrabWorkTimeWithJudgeValidTimeCheckTest extends Specification {
    def check = new GrabWorkTimeWithJudgeValidTimeCheck()

    def context = Mock(CheckContext)
    def checkModel = Mock(CheckModel)
    def dspOrder = Mock(DspOrderVO)
    def driver = Mock(DriverVO)
    def model = Mock(DspModelVO)

    def setup() {
        context.getDspOrder() >> dspOrder
        checkModel.getModel() >> model
        model.getDriver() >> driver
        model.getOrder() >> dspOrder
    }


    def "test check method with driver is not full driver"() {
        given:
        driver.isFullDriver() >> false

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check method with no delay dsp config"() {
        given:
        driver.isFullDriver() >> true
        context.getDelayDspConfigMap() >> [:]
        dspOrder.estimatedUseTime() >> new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2024-01-02 12:00:00"))

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.DRV_GRAB_WORK_TIME_LIMIT
    }

    def "test check method with non-first class driver"() {
        given:
        driver.isFullDriver() >> true
        context.getDelayDspConfigMap() >> [(dspOrder.getDspOrderId()): new DelayDspConfigVO(taskDeadTime: "2023-12-31 23:59:59")]
        dspOrder.estimatedUseTime() >> new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2024-01-02 12:00:00"))
        context.getFirstClassDriverList() >> []
        driver.isRegisterDriver() >> false

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.DRV_GRAB_WORK_TIME_LIMIT
    }

    def "test check method with valid time"() {
        given:
        driver.isFullDriver() >> true
        context.getDelayDspConfigMap() >> [(dspOrder.getDspOrderId()): new DelayDspConfigVO(taskDeadTime: "2023-12-31 23:59:59")]
        dspOrder.estimatedUseTime() >> new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2024-01-02 12:00:00"))
        context.getFirstClassDriverList() >> [driver.getDriverId()]
        driver.isRegisterDriver() >> true

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check method with not valid time"() {
        given:
        driver.isFullDriver() >> true
        context.getDelayDspConfigMap() >> [(dspOrder.getDspOrderId()): new DelayDspConfigVO(taskDeadTime: "2099-12-31 23:59:59")]
        dspOrder.estimatedUseTime() >> new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2024-01-02 12:00:00"))
        context.getFirstClassDriverList() >> [driver.getDriverId()]
        driver.isRegisterDriver() >> true

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.DRV_GRAB_WORK_TIME_LIMIT
    }
}
