package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class OrderMileageValueCheckTest extends Specification {
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    DeadHeadDisModelVO deadHeadDisModel
    @InjectMocks
    OrderMileageValueCheck orderMileageValueCheck = new OrderMileageValueCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
    }

    def "订单里程价值>= 配置"() {
        given:

        Map<Long, DeadHeadDisModelVO> map = Maps.newHashMap()
        map.put(1L, deadHeadDisModel)
        Mockito.when(context.getDriverEmptyDrivingInfo4OrderMileageMap()).thenReturn(map)
        Mockito.when(context.queryOrderMileageValueConfig(Mockito.any())).thenReturn(1D)
        Mockito.when(order.getCostAmount()).thenReturn(BigDecimal.valueOf(100))
        Mockito.when(order.getEstimatedKm()).thenReturn(BigDecimal.valueOf(10))
        Mockito.when(deadHeadDisModel.getFrowardEmptyDistance()).thenReturn(10D)

        when:
        CheckCode result = orderMileageValueCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "订单里程价值< 配置"() {
        given:

        Map<Long, DeadHeadDisModelVO> map = Maps.newHashMap()
        map.put(1L, deadHeadDisModel)
        Mockito.when(context.getDriverEmptyDrivingInfo4OrderMileageMap()).thenReturn(map)
        Mockito.when(context.queryOrderMileageValueConfig(Mockito.any())).thenReturn(1D)
        Mockito.when(order.getCostAmount()).thenReturn(BigDecimal.valueOf(100))
        Mockito.when(order.getEstimatedKm()).thenReturn(BigDecimal.valueOf(50))
        Mockito.when(deadHeadDisModel.getFrowardEmptyDistance()).thenReturn(60D)

        when:
        CheckCode result = orderMileageValueCheck.check(checkModel, context)

        then:
        result == CheckCode.PREV_LOW_ORDER_MILEAGE_VALUE
    }

    def "test check"() {
        def context = Mock(CheckContext)
        context.getDriverEmptyDrivingInfo4OrderMileageMap() >> resMap
        context.queryOrderMileageValueConfig(_) >> 1
        def mockOrder = new DspOrderVO(costAmount: 100 as BigDecimal,estimatedKm: 10 as BigDecimal)
        def mockDriver = new DriverVO(driverId: driverId, cityId: 1, car: new CarVO(carTypeId: 117),
                transportGroups: Arrays.asList(new TransportGroupVO(transportGroupMode: TransportGroupMode.REGISTER_DISPATCH)))
        def checkModel = new CheckModel(model: new DspModelVO(mockOrder, mockDriver))

        when:
        CheckCode result = orderMileageValueCheck.check(checkModel,context)

        then:
        result == checkRes

        where:
        resMap|driverId||checkRes
        null|1L||CheckCode.PASS
        Maps.newHashMap()|1l||CheckCode.PASS
        getMap()|1L||CheckCode.PASS
        getMap()|2l||CheckCode.PREV_LOW_ORDER_MILEAGE_VALUE
        getMap()|3L||CheckCode.PREV_LOW_ORDER_MILEAGE_VALUE_DRIVER_POSITION
        getMap()|4L||CheckCode.NEXT_LOW_ORDER_MILEAGE_VALUE


    }

    def getMap() {
        Map<Long, DeadHeadDisModelVO> map = Maps.newHashMap()
        map.put(1L, new DeadHeadDisModelVO(drvId: 1L, frowardOrderInfo: new DspOrderVO(costAmount: 20,estimatedKm: 4 ),
                frowardEmptyDrivingInfo: new EmptyDrivingVO(1000, 30),
                backwardEmptyDrivingInfo: new EmptyDrivingVO(1000, 30),
                realTimePosition: true))
        map.put(2L, new DeadHeadDisModelVO(drvId: 2L, backwardOrderInfo: new DspOrderVO(costAmount: new BigDecimal("20"),estimatedKm: 4),
                frowardEmptyDrivingInfo: new EmptyDrivingVO(900000, 30),
                backwardEmptyDrivingInfo: new EmptyDrivingVO(90000, 30),
                realTimePosition: false))
        map.put(3L, new DeadHeadDisModelVO(drvId: 3L, backwardOrderInfo: new DspOrderVO(costAmount: new BigDecimal("20"),estimatedKm: 4),
                frowardEmptyDrivingInfo: new EmptyDrivingVO(900000, 30),
                backwardEmptyDrivingInfo: new EmptyDrivingVO(90000, 30),
                realTimePosition: true))
        map.put(4L, new DeadHeadDisModelVO(drvId: 4L, backwardOrderInfo: new DspOrderVO(costAmount: new BigDecimal("20"),estimatedKm: 100),
                frowardEmptyDrivingInfo: new EmptyDrivingVO(1000, 30),
                backwardEmptyDrivingInfo: new EmptyDrivingVO(100000, 30),
                realTimePosition: true))
        return map
    }

    def "test downgrade"() {
        when:
        CheckCode result = orderMileageValueCheck.downgrade()

        then:
        result == CheckCode.PASS
    }



}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme