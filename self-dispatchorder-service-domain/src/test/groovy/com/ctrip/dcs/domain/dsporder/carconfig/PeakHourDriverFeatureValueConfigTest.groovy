package com.ctrip.dcs.domain.dsporder.carconfig

import spock.lang.Specification

class PeakHourDriverFeatureValueConfigTest extends Specification {
    Map<Integer, Double> config = Mock()
    def peakHourDriverFeatureValueConfig = new PeakHourDriverFeatureValueConfig(
            config: config
    )

    def "get"() {
        given:
        config.get(_) >> configValue

        when:
        def value = peakHourDriverFeatureValueConfig.getConfigValue(2)

        then:
        res == value

        where:
        res   || configValue
        0d || null
        500d || "500"
        200d || "200"
    }
}
