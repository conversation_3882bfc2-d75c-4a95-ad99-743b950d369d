package com.ctrip.dcs.domain.common.value


import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverRelateOrderVOTest extends Specification {

    def "test"() {
        given:

        when:
        DriverRelateOrderVO baseDetailVO = new DriverRelateOrderVO();

        then:
        35.0 == baseDetailVO.getFrowardEmptyDistance();
        35.0 == baseDetailVO.getBackwardEmptyDistance()
        0.0 == baseDetailVO.getOriginalEmptyDistance()
        30.0 == baseDetailVO.getBackwardEmptyDuration()


    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme