package com.ctrip.dcs.domain.schedule.sort.feature.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification
import spock.lang.Unroll

class DriverCategoryFeatureTest extends Specification {
    def testObj = new DriverCategoryFeature()

    @Unroll
    def "valueTest"() {
        given: "设定相关方法入参"
        def model = Mock(SortModel)
        def context = Mock(SortContext)
        def driver = Mock(DriverVO)

        model.getModel() >> Mock(DspModelVO) {
            getDriver() >> driver
        }
        driver.isRegisterDriver() >> isRegisterDriver
        driver.isRegisterPartTimeDriver() >> isRegisterPartTimeDriver
        context.getProperties("driverCategoryASortScore", 0) >> 10
        context.getProperties("driverCategoryBSortScore", 0) >> 9
        context.getProperties("driverCategoryCSortScore", 0) >> 8

        when:
        def result = testObj.value(model, context)

        then: "验证返回结果里属性值是否符合预期"
        result.getValue() == expectedResult
        where: "表格方式验证多种分支调用场景"
        isRegisterDriver | isRegisterPartTimeDriver || expectedResult
        true             | false                    || 10
        false            | true                     || 9
        false            | false                    || 8
    }
}
