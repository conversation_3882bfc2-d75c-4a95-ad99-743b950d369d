package com.ctrip.dcs.domain.schedule.process.impl

import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.factory.GrabOrderFactory
import com.ctrip.dcs.domain.schedule.repository.BroadcastRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SystemBroadcastProcessTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    RecommendService recommendService
    @Mock
    CheckService checkService
    @Mock
    MessageProviderService messageProducer
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    GrabOrderFactory grabOrderFactory
    @Mock
    BroadcastRepository grabOrderRepository
    @Mock
    ScheduleTaskDO scheduleTaskDO
    @Mock
    DspOrderVO dspOrderVO;
    @Mock
    SubSkuVO subSkuVO
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DriverVO driverVO;
    @Mock
    TransportGroupVO transportGroupVO;
    @Mock
    SupplierVO supplierVO
    @Mock
    DriverOrderFactory driverOrderFactory
    @Mock
    GrabOrderDO grabOrderDO

    @InjectMocks
    SystemBroadcastProcess systemBroadcastProcess

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(transportGroupVO.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(dspModelVO.getTransportGroup()).thenReturn(transportGroupVO)
        when(driverVO.getTransportGroups()).thenReturn([transportGroupVO])
        when(driverVO.getSupplier()).thenReturn(supplierVO)
        when(subSkuVO.getSubSkuId()).thenReturn(1)
        when(subSkuVO.getTakenType()).thenReturn(TakenType.ASSISTANT)
        when(subSkuVO.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        when(scheduleTaskDO.getTaskId()).thenReturn(1L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(1L)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("1")
        when(scheduleTaskDO.getRound()).thenReturn(1)
        when(checkModel.isPass()).thenReturn(true)
        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(broadcastGrabConfig.getInteger(anyString(), anyInt())).thenReturn(0)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(grabOrderFactory.create(any(), any(), any(), any())).thenReturn([grabOrderDO])

        when:
        def result = systemBroadcastProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }

    def "test execute 2"() {
        given:
        when(transportGroupVO.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(dspModelVO.getTransportGroup()).thenReturn(transportGroupVO)
        when(driverVO.getTransportGroups()).thenReturn([transportGroupVO])
        when(driverVO.getSupplier()).thenReturn(supplierVO)
        when(subSkuVO.getSubSkuId()).thenReturn(1)
        when(subSkuVO.getTakenType()).thenReturn(TakenType.ASSISTANT)
        when(subSkuVO.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        when(scheduleTaskDO.getTaskId()).thenReturn(1L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(1L)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("1")
        when(scheduleTaskDO.getRound()).thenReturn(1)
        when(checkModel.isPass()).thenReturn(true)
        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(checkService.check(any() as DspCheckCommand)).thenReturn([checkModel])
        when(recommendService.recommend(any(), any(), any())).thenReturn([new SortModel(dspModelVO)])
        when(broadcastGrabConfig.getInteger(anyString(), anyInt())).thenReturn(1)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(grabOrderFactory.create(any(), any(), any(), any())).thenReturn([grabOrderDO])

        when:
        def result = systemBroadcastProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme