package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.apache.commons.lang3.StringUtils
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class MockOrderCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext checkContext
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverVO driverVO
    @InjectMocks
    MockOrderCheck mockOrderCheck

    def setup() {
        MockitoAnnotations.initMocks(this)

        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(dspOrderVO.getUid()).thenReturn("OPS_cgrinder_0000001")
        when(driverVO.getDriverId()).thenReturn(1L)

    }

    def "test check"() {
        given:
        when(checkContext.getProperties("MOCK_ORDER_UID_PREFIX", "OPS_cgrinder")).thenReturn(uids)
        when(checkContext.getProperties("MOCK_DRIVER_ID_LIST", StringUtils.EMPTY)).thenReturn(driverIds)

        when:
        CheckCode code = mockOrderCheck.check(checkModel, checkContext)

        then:
        code == result

        where:
        uids                   | driverIds || result
        "OPS_cgrinder,OPS_OPS" | "1,2"     || CheckCode.PASS
        "OPS_cgrinder,OPS_OPS" | "2"       || CheckCode.MOCK_ORDER_LIMIT
        "OPS_OPS"              | "1,2"     || CheckCode.PASS
    }

    def "test downgrade"() {
        when:
        CheckCode result = mockOrderCheck.downgrade()

        then:
        result == CheckCode.MOCK_ORDER_LIMIT
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme