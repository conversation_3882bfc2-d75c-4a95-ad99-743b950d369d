package com.ctrip.dcs.domain.dsporder.value

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.SpecialConfirmTypeEnum
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent
import org.junit.Assert
import spock.lang.Specification

/**
 * <AUTHOR> Zhang<PERSON><PERSON>
 * @create 2024/7/17 15:29
 */
class DspOrderConfirmRecordVOTest extends Specification {

    DspOrderConfirmRecordVO recordVO = new DspOrderConfirmRecordVO()

    def "recordVO Test"() {

        given:

        when:

        def res = recordVO.queryDriverIdStr()

        then:

        res == ""

    }

    def "recordVO Test 1"() {

        given:

        recordVO.setDriverInfo(new DspOrderConfirmRecordVO.DriverRecord())

        when:

        def res = recordVO.queryDriverIdStr()

        then:

        res == ""

    }

    def "recordVO Test 2"() {

        given:

        recordVO.setDriverInfo(new DspOrderConfirmRecordVO.DriverRecord(driverId: -1))

        when:

        def res = recordVO.queryDriverIdStr()

        then:

        res == ""

    }

    def "recordVO Test 3"() {

        given:

        recordVO.setDriverInfo(new DspOrderConfirmRecordVO.DriverRecord(driverId: 11))

        when:

        def res = recordVO.queryDriverIdStr()

        then:

        res == "11"

    }

    def "recordVO querySupplierIdStr Test"() {

        given:

        when:

        def res = recordVO.querySupplierIdStr()

        then:

        res == ""

    }

    def "recordVO querySupplierIdStr Test 1"() {

        given:

        recordVO.setSupplierInfo(new DspOrderConfirmRecordVO.SupplierRecord())

        when:

        def res = recordVO.querySupplierIdStr()

        then:

        res == ""

    }

    def "recordVO querySupplierIdStr Test 2"() {

        given:

        recordVO.setSupplierInfo(new DspOrderConfirmRecordVO.SupplierRecord(supplierId: -1))

        when:

        def res = recordVO.querySupplierIdStr()

        then:

        res == ""

    }

    def "recordVO querySupplierIdStr Test 3"() {

        given:

        recordVO.setSupplierInfo(new DspOrderConfirmRecordVO.SupplierRecord(supplierId: 11))

        when:

        def res = recordVO.querySupplierIdStr()

        then:

        res == "11"

    }

    def "test isNotWriteDriverInfo"() {
        given:
        recordVO.setNotWriteDriverInfoFlag(notWriteDriverInfoFlag)
        when:
        def result = recordVO.isNotWriteDriverInfo()
        then:
        Assert.assertTrue(Objects.equals(result, expect))

        where:
        notWriteDriverInfoFlag || expect
        0                      || false
        1                      || true
    }

    def " test getSpecialConfirmType"() {
        when:
        def confrim = new DspOrderConfirmRecordVO(categoryCode: categoryCode, confirmEvent: confirmEvent, specialConfirmType: specialConfirmType)

        then:
        res == confrim.getSpecialConfirmType()

        where:
        res                                                      || categoryCode                            | confirmEvent                                  | specialConfirmType
        SpecialConfirmTypeEnum.CHARTERED_CHANGE_DRIVER.getCode() || CategoryCodeEnum.DAY_RENTAL.getType()   | OrderStatusEvent.VBK_UPDATE_DRIVER.getCode()  | null
        SpecialConfirmTypeEnum.DRIVER_INFO_CHG.getCode()         || CategoryCodeEnum.FROM_AIRPORT.getType() | OrderStatusEvent.VBK_UPDATE_DRIVER.getCode()  | null
        SpecialConfirmTypeEnum.ORI_DRIVER_INFO_CHANGE.getCode()  || CategoryCodeEnum.TO_AIRPORT.getType()   | OrderStatusEvent.SAAS_UPDATE_DRIVER.getCode() | SpecialConfirmTypeEnum.ORI_DRIVER_INFO_CHANGE.getCode()
        SpecialConfirmTypeEnum.ORI_DRIVER_INFO_CHANGE.getCode()  || CategoryCodeEnum.FROM_AIRPORT.getType() | null                                          | SpecialConfirmTypeEnum.ORI_DRIVER_INFO_CHANGE.getCode()
        SpecialConfirmTypeEnum.DEFAULT.getCode()                 || CategoryCodeEnum.FROM_AIRPORT.getType() | null                                          | null
    }

}
