package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckConfig
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.source.CheckSourceConfig
import com.ctrip.dcs.domain.schedule.sort.SortConfig
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class DeadHeadDisLimitCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    DeadHeadDisModelVO deadHeadDisModelVO
    @Mock
    Logger logger
    @InjectMocks
    DeadHeadDisLimitCheck deadHeadDisLimitCheck = new DeadHeadDisLimitCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "司机空驶<阈值"() {
        given:
        Map<Long, DeadHeadDisModelVO> map = new HashMap<>()
        map.put(1L, deadHeadDisModelVO)
        Mockito.when(context.getDriverDeadHeadDisModelMap()).thenReturn(map)
        Mockito.when(context.getDeadHeadDisLimit(Mockito.any(), Mockito.any())).thenReturn(600)
        Mockito.when(deadHeadDisModelVO.getDeadHeadDis()).thenReturn(400D)

        when:
        CheckCode result = deadHeadDisLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机空驶>阈值"() {
        given:
        Map<Long, DeadHeadDisModelVO> map = new HashMap<>()
        map.put(1L, deadHeadDisModelVO)
        Mockito.when(context.getDriverDeadHeadDisModelMap()).thenReturn(map)
        Mockito.when(context.getDeadHeadDisLimit(Mockito.any(), Mockito.any())).thenReturn(600)
        Mockito.when(deadHeadDisModelVO.getDeadHeadDis()).thenReturn(700D)

        when:
        CheckCode result = deadHeadDisLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.NO_EFFICIENCY
    }

    def "test check"() {
        given:
        def context = Mock(CheckContext)
        context.getDriverDeadHeadDisModelMap() >> resMap
        context.getSubSku() >> subSku
        context.getDeadHeadDisLimit(_,_) >> 100
        def mockOrder = new DspOrderVO(costAmount: 100 as BigDecimal, estimatedKm: 10 as BigDecimal)
        def mockDriver = new DriverVO(driverId: driverId, cityId: 1, car: new CarVO(carTypeId: 117),
                transportGroups: Arrays.asList(new TransportGroupVO(transportGroupMode: TransportGroupMode.REGISTER_DISPATCH)))
        def checkModel = new CheckModel(model: new DspModelVO(mockOrder, mockDriver))
        when:
        CheckCode result = deadHeadDisLimitCheck.check(checkModel, context)

        then:
        result == res

        where:
        resMap            | subSku   | driverId || res
        null              | getSku() | 1L       || CheckCode.NO_EFFICIENCY
        Maps.newHashMap() | getSku() | 1L       || CheckCode.NO_EFFICIENCY
        getMap()          | getSku() | 1L       || CheckCode.PASS
        getMap()          | getSku() | 2L       || CheckCode.NO_EFFICIENCY
        getMap()          | getSku() | 4L       || CheckCode.NO_EFFICIENCY
    }

    def getMap() {
        Map<Long, DeadHeadDisModelVO> map = Maps.newHashMap()
        map.put(1L, new DeadHeadDisModelVO(drvId: 1L, deadHeadDis: 10))
        map.put(2L, new DeadHeadDisModelVO(drvId: 2L, deadHeadDis: 1000))
        map.put(3L, new DeadHeadDisModelVO(drvId: 3L, deadHeadDis: 10))
        return map
    }

    def getSku() {
        def checkConfig = CheckConfig.builder().checkId("111").checkSourceConfig(new CheckSourceConfig(1, 1)).build()
        return SubSkuVO.builder()
                .check(checkConfig)
                .subSkuId(1).subSkuName("1").dspType(DspType.DELAY_ASSIGN).takenType(TakenType.ASSISTANT).sort(SortConfig.builder().build()).retrySecond(1L).build()
    }

    def "test downgrade"() {
        when:
        CheckCode result = deadHeadDisLimitCheck.downgrade()

        then:
        result == CheckCode.NO_EFFICIENCY
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme