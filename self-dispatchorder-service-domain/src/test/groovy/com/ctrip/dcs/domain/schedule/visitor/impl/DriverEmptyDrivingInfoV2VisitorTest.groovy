package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.service.DriverDeadHeadDisService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO
import com.google.common.collect.Maps
import spock.lang.Specification

class DriverEmptyDrivingInfoV2VisitorTest extends Specification {
    def driverDeadHeadDisService = Mock(DriverDeadHeadDisService)
    def driverLocationVisitor = Mock(DriverLocationVisitor)
    def driverForwardAndBackwardOrderVisitor = Mock(DriverForwardAndBackwardOrderVisitor)

    def dspOrder = <PERSON>ck(DspOrderVO)
    def driver = Mock(DriverVO)

    def "test visit method"() {
        given:
        def context = Mock(CheckContext)
        context.getContext() >> Mock(DspContext)
        context.getDriverEmptyDrivingInfo4OrderMileageV2Map() >> [:] >> [123L: new DeadHeadDisModelVO()]
        def visitor = new DriverEmptyDrivingInfoV2Visitor(dspOrder, [driver], driverDeadHeadDisService, driverLocationVisitor, driverForwardAndBackwardOrderVisitor)
        driverDeadHeadDisService.calculateDeadHeadDisForOrderMileage(_, _, _, _, _) >> Maps.newHashMap()

        when:
        visitor.visit(context)

        then:
        1 * driverLocationVisitor.visit(_)
    }

    def "test visit method1"() {
        given:
        def context = Mock(SortContext)
        context.getDspContext() >> Mock(DspContext)
        context.getDriverEmptyDrivingInfo4OrderMileageV2Map() >> [:] >> [123L: new DeadHeadDisModelVO()]
        def visitor = new DriverEmptyDrivingInfoV2Visitor(dspOrder, [driver], driverDeadHeadDisService, driverLocationVisitor, driverForwardAndBackwardOrderVisitor)
        driverDeadHeadDisService.calculateDeadHeadDisForOrderMileage(_, _, _, _, _) >> Maps.newHashMap()

        when:
        visitor.visit(context)

        then:
        1 * driverLocationVisitor.visit(_)
    }
}
