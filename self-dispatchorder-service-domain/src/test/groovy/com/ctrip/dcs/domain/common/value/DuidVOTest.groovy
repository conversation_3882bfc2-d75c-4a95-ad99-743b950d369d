package com.ctrip.dcs.domain.common.value

import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DuidVOTest extends Specification {

    def "of(String s) should parse input correctly"() {

        when:
        DuidVO result = DuidVO.of("135278820974002310-65141588-135279621909905521-1-8882-40-5-S135279621909905521-null-0")

        then:
        result.dspOrderId == "135278820974002310"
        result.scheduleId == 65141588L
        result.taskId == 135279621909905521L
        result.round == 1
        result.subSkuId == 8882
        result.dspType == 40
        result.takenType == 5
        result.scheduleTaskId == "S135279621909905521"
        result.dspRewardStrategyId == 0L
    }
}
