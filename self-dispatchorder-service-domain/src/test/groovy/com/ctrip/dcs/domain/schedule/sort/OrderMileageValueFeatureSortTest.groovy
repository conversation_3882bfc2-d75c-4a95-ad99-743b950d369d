package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.OrderMileageValueFeature
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class OrderMileageValueFeatureSortTest extends Specification {
    @Mock
    SortModel model
    @Mock
    SortContext context
    @Mock
    DriverVO driver
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverMileageProfitVO dmp
    @Mock
    DriverMileageProfitVO dmp1
    @Mock
    CarVO car
    @Mock
    DriverWorkTimeVO workTime
    @Mock
    DriverRelateOrderVO relateOrder

    @InjectMocks
    OrderMileageValueFeature feature = new OrderMileageValueFeature()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(model.getModel()).thenReturn(dspModelVO)
        Mockito.when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        Mockito.when(dspOrderVO.getEstimatedUseTime()).thenReturn(new Date())
        Mockito.when(dspOrderVO.getPredicServiceStopTime()).thenReturn(new Date())


        Mockito.when(context.getDspOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getExpectDriverMileageProfit(Mockito.any())).thenReturn(dmp)
        Mockito.when(context.getCompleteDriverMileageProfit(Mockito.any())).thenReturn(dmp1)

        Mockito.when(dspModelVO.getDriver()).thenReturn(driver)
        Mockito.when(driver.getDriverId()).thenReturn(1L)
        Mockito.when(driver.getCar()).thenReturn(car)
        Mockito.when(driver.findHitWorkTime(Mockito.any())).thenReturn(workTime)
        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)


        Mockito.when(car.getCarTypeId()).thenReturn(117L)
    }

    @Unroll
    def "test value"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()
        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(dspModelVO.getOrder()).thenReturn(null)

        when:
        Value res = feature.value(model, context)

        then:
        res.value == 0.0


    }

    @Unroll
    def "test value1"() {
        given:

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(null)
        Mockito.when(dspOrderVO.getCostAmount()).thenReturn(new BigDecimal("112"))
        Mockito.when(dspOrderVO.getEstimatedKm()).thenReturn(new BigDecimal("19"))


        when:
        Value res = feature.value(model, context)

        then:
        res.value > 0


    }

    @Unroll
    def "test value2"() {
        given:

        given:

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(relateOrder.getFrowardEmptyDistance()).thenReturn(112D)
        Mockito.when(relateOrder.getBackwardEmptyDistance()).thenReturn(30D)


        when:
        Value res = feature.value(model, context)

        then:
        res.value == 0.0


    }



}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme