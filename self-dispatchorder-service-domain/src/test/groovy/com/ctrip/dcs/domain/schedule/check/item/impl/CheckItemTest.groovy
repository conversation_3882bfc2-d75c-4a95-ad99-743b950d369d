package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.schedule.service.DspContextService
import spock.lang.Specification

class CheckItemTest extends Specification{


    def dspContextService = Mock(DspContextService)
    def configService = Mock(ConfigService)
//    def "drvSourceCheckItem"(){
//        given:"Mock数据"
//
//        def dspOrderDO  = new DspOrderDO(fromPoiDTO:new FromPoiDTO(), toPoiDTO: new ToPoiDTO(),cityId: 1,vehicleGroupId:1,
//                estimatedUseTime:new Timestamp(DateUtil.parseDateStr2Date("2023-03-05 23:00:00").getTime()),
//                predicServiceStopTime:new Timestamp(DateUtil.parseDateStr2Date("2023-03-06 00:01:00").getTime()))
//        def scheduleOrder =ScheduleOrder.builder().dspOrder(dspOrderDO).build()
//        def driver1 =  DriverVO.builder().workTimes(new PeriodsVO(Lists.newArrayList("22:00~01:00"))).driverStatus(1).driverId(1L).addressLatitude(1).addressLongitude(1).car(new CarVO(carId: 1L,carLicense: "京A12345",carTypeId:117,isEnergy:1)).build()
//        def driver2 =  DriverVO.builder().workTimes(new PeriodsVO(Lists.newArrayList("20:00~21:00"))).driverStatus(0).driverId(2L).addressLatitude(2).addressLongitude(2).car(new CarVO(carId: 2L,carLicense: "京A54321",carTypeId:117,isEnergy:0)).build()
//        def driver3 =  DriverVO.builder().workTimes(new PeriodsVO(Lists.newArrayList("23:00~00:00"))).driverStatus(2).driverId(3L).addressLatitude(3).addressLongitude(3).car(new CarVO(carId: 3L,carLicense: "京A54M21",carTypeId:117,isEnergy:0)).build()
//        def models = Lists.newArrayList(
//                new CheckModel(new DspModelVO(scheduleOrder,driver1)),
//                new CheckModel(new DspModelVO(scheduleOrder,driver2)),
//                new CheckModel(new DspModelVO(scheduleOrder,driver3)))
//
//        def checkContext =  CheckContext.builder().context(new DspContext(dspContextService))
//                .dspOrder(scheduleOrder).configService(configService)
//
//                .subSku(new SubSkuVO(subSkuId: 1, check: new CheckConfig(checkSourceConfig :new CheckSourceConfig(transportGroupMode :1)))).build()
//
//        dspContextService.checkDriverTrafficControl(_) >> Lists.newArrayList("京A54M21")
//
//        def driverLocationMap = new HashMap()
//        driverLocationMap.put(1L, DriverLocationVO.builder().driverId(1L).locations(Lists.newArrayList(DriverLocationVO.LocationVO.builder().driverId(1L).locationTimestamp(DateUtils.addMinutes(new Date(),-1).getTime()).build())).build())
//        driverLocationMap.put(2L, DriverLocationVO.builder().driverId(2L).locations(Lists.newArrayList(DriverLocationVO.LocationVO.builder().driverId(2L).locationTimestamp(DateUtils.addMinutes(new Date(),-4).getTime()).build())).build())
//        driverLocationMap.put(3L, DriverLocationVO.builder().driverId(3L).locations(Lists.newArrayList(DriverLocationVO.LocationVO.builder().driverId(3L).locationTimestamp(DateUtils.addMinutes(new Date(),-6).getTime()).build())).build())
//
//        configService.getInteger("DRIVER_ACTIVE_MINUTES_THRESHOLD",3) >> 3
//        configService.getString("sub_sku_white_list", Strings.EMPTY)>> "1,2"
//        configService.getString("driver_white_list", Strings.EMPTY)>>"1,2,5"
//        dspContextService.queryDriverLocation(_) >> driverLocationMap
//
//        def firstClassDriverList = new ArrayList();
//        firstClassDriverList.add(1L)
//        firstClassDriverList.add(3L)
//        dspContextService.queryFirstClassDriverList(_) >> firstClassDriverList
//
//
//        def driverLeaveMap = new HashMap()
//        driverLeaveMap.put(1L,new DriverLeaveVO(1L,"2023-03-05 22:00:00","2023-03-05 23:30:00"))
//        driverLeaveMap.put(2L,new DriverLeaveVO(2L,"2023-03-06 00:00:00","2023-03-06 23:30:00"))
//        dspContextService.queryDriverLeave(_)>> driverLeaveMap
//
//        when: "执行校验方法"
//        def res = checkItem.check(models, checkContext).stream()
//                .filter({item->(item.checkCode==CheckCode.PASS)})
//                .map({ item -> item.model.getDriver().getDriverId() })
//                .collect(Collectors.toList())
//
//
//        then:"验证校验结果"
//        res1 == res.contains(1L)
//        res2 == res.contains(2L)
//        res3 == res.contains(3L)
//
//        where:
//        checkItem                       |res1   |res2   |res3
//        new CarLicenseLimitCheck()      |true   |true   |false
//        new DriverStatusCheck()         |true   |false  |false
//        new DriverAddressCheck()        |true   |true   |true
//        new DriverActiveCheck()         |true   |false  |false
//        new DriverWorkTimeCheck()       |true   |false  |true
//        new ConfDriverCheck()           |true   |true   |false
//        new FirstClassDriverCheckCheck()|true   |false  |true
//        new DriverLeaveCheck()          |false  |false  |true
//    }

}
