package com.ctrip.dcs.domain.dsporder.handler.impl

import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.factory.DspOrderConfirmRecordFactory
import com.ctrip.dcs.domain.dsporder.gateway.PhoneBridgeServiceGateway
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusContext
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.dsporder.value.OperatorVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import java.util.concurrent.Callable
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.Future

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class ConfirmOrderStatusHandlerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    MessageProviderService messageProducer
    @Mock
    DspOrderRepository dspOrderRepository
    @Mock
    DspOrderConfirmRecordRepository confirmRecordRepository
    @Mock
    DspOrderConfirmRecordFactory confirmRecordFactory
    @Mock
    PurchaseSupplyOrderGateway purchaseSupplyOrderGateway
    @Mock
    ExecutorService syncOrderThreadPool
    @Mock
    PhoneBridgeServiceGateway phoneBridgeRepository
    @Mock
    OrderStatusContext orderStatusContext
    @Mock
    DspOrderConfirmRecordVO dspOrderConfirmRecord
    @Mock
    DistributedLockService distributedLockService
    @Mock
    DriverVO driverVO
    @Mock
    OperatorVO operatorVO
    @Mock
    ConfigService commonConfConfig;

    @Mock
    DspOrderDetailRepository dspOrderDetailRepository;
    @InjectMocks
    DriverAndCarConfirmOrderStatusHandler confirmOrderStatusHandler

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test concurrent confirm"() {
        given:
        when(confirmRecordFactory.createDriverCarConfirmRecord(any())).thenReturn(dspOrderConfirmRecord)
        when(orderStatusContext.getDspOrderId()).thenReturn("1")
        when(orderStatusContext.getFrom()).thenReturn(OrderStatusEnum.TO_BE_CONFIRMED)
        when(orderStatusContext.getTo()).thenReturn(OrderStatusEnum.DRIVER_CAR_CONFIRMED)
        when(orderStatusContext.getEvent()).thenReturn(OrderStatusEvent.SYSTEM_ASSIGN)
        when(orderStatusContext.getEvent()).thenReturn(OrderStatusEvent.SYSTEM_ASSIGN)
        DspOrderDO o1 = new DspOrderDO(orderStatus: OrderStatusEnum.TO_BE_CONFIRMED.getCode(),dspOrderId: "1")
        DspOrderDO o2 = new DspOrderDO(orderStatus: OrderStatusEnum.DRIVER_CAR_CONFIRMED.getCode(),dspOrderId: "2")
        when:
        ExecutorService executorService = Executors.newFixedThreadPool(2)
        Future<DspOrderConfirmRecordVO> future1 = executorService.submit(new Callable<DspOrderConfirmRecordVO>() {
            @Override
            DspOrderConfirmRecordVO call() throws Exception {
                return confirmOrderStatusHandler.confirm(orderStatusContext, o1)
            }
        })
        Future<DspOrderConfirmRecordVO> future2 = executorService.submit(new Callable<DspOrderConfirmRecordVO>() {
            @Override
            DspOrderConfirmRecordVO call() throws Exception {
                return confirmOrderStatusHandler.confirm(orderStatusContext, o2)
            }
        })

        then:
        future1.get() != null
        Exception ex = null
        try {
            future2.get()
        } catch (Exception e) {
            ex = e
        }
        ex != null
    }

    def "test concurrent confirm2"() {
        given:
        when(confirmRecordFactory.createDriverCarConfirmRecord(any())).thenReturn(dspOrderConfirmRecord)
        when(orderStatusContext.getDspOrderId()).thenReturn("1")
        when(orderStatusContext.getFrom()).thenReturn(OrderStatusEnum.TO_BE_CONFIRMED)
        when(orderStatusContext.getTo()).thenReturn(OrderStatusEnum.DRIVER_CAR_CONFIRMED)
        when(orderStatusContext.getEvent()).thenReturn(OrderStatusEvent.SYSTEM_ASSIGN)
        when(orderStatusContext.getEvent()).thenReturn(OrderStatusEvent.SYSTEM_ASSIGN)
        when(orderStatusContext.getDriver()).thenReturn(driverVO)
        when(orderStatusContext.getDriver().getDriverId()).thenReturn(1234L)
        when(orderStatusContext.getOperatorVO()).thenReturn(operatorVO)
        when(orderStatusContext.getOperatorVO().getName()).thenReturn("sys")
        DspOrderDO o1 = new DspOrderDO(orderStatus: OrderStatusEnum.TO_BE_CONFIRMED.getCode(),dspOrderId: "1", "driverOrderId": "123")
        DspOrderDO o2 = new DspOrderDO(orderStatus: OrderStatusEnum.DRIVER_CAR_CONFIRMED.getCode(),dspOrderId: "2", "driverOrderId": "123")
        when:
        ExecutorService executorService = Executors.newFixedThreadPool(2)
        Future<DspOrderConfirmRecordVO> future1 = executorService.submit(new Callable<DspOrderConfirmRecordVO>() {
            @Override
            DspOrderConfirmRecordVO call() throws Exception {
                return confirmOrderStatusHandler.confirm(orderStatusContext, o1)
            }
        })
        Future<DspOrderConfirmRecordVO> future2 = executorService.submit(new Callable<DspOrderConfirmRecordVO>() {
            @Override
            DspOrderConfirmRecordVO call() throws Exception {
                return confirmOrderStatusHandler.confirm(orderStatusContext, o2)
            }
        })

        then:
        future1.get() != null
        Exception ex = null
        try {
            future2.get()
        } catch (Exception e) {
            ex = e
        }
        ex != null
    }


    def "test sync_throwex"() {
        given:
        when(commonConfConfig.getString("noticePurchaseSync", "1")).thenReturn("1")
        when(commonConfConfig.getString("throwExWhenSupplyConfirmError", "false")).thenReturn("true")
        when(purchaseSupplyOrderGateway.confirm(any(),any())).thenThrow(ErrorCode.PURCHASE_SUPPLY_ORDER_CONFIRM_ERROR.bizException)
        DspOrderConfirmRecordVO record = Mock()
        DspOrderDO dspOrder = Mock()
        when:
        confirmOrderStatusHandler.sync(record, dspOrder)
        then:
        def exception = thrown(BizException)
        exception.code == ErrorCode.PURCHASE_SUPPLY_ORDER_CONFIRM_ERROR.getCode()
        exception.message ==ErrorCode.PURCHASE_SUPPLY_ORDER_CONFIRM_ERROR.getDesc()

    }

    def "test sync_nothrowex"() {
        given:
        when(commonConfConfig.getString("noticePurchaseSync", "1")).thenReturn("1")
        when(commonConfConfig.getString("throwExWhenSupplyConfirmError", "false")).thenReturn("false")
        when(purchaseSupplyOrderGateway.confirm(any(),any())).thenThrow(ErrorCode.PURCHASE_SUPPLY_ORDER_CONFIRM_ERROR.bizException)
        DspOrderConfirmRecordVO record = Mock()
        DspOrderDO dspOrder = Mock()
        when:
        confirmOrderStatusHandler.sync(record, dspOrder)
        then:
        noExceptionThrown()

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme