package com.ctrip.dcs.domain.dsporder.statemachine

import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.dsporder.handler.DspOrderStatusMachine
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusContext
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DspOrderStatusMachineTest extends Specification {

    def context = Mock(OrderStatusContext)

    @Unroll
    def "test commit"() {

        when: "执行校验方法"
        def state = DspOrderStatusMachine.getInstance().commit(from, event, to, context)

        then: "验证校验结果"
        result == state

        where:
        from                                 | event                           | to                                   || result
        OrderStatusEnum.TO_BE_CONFIRMED      | OrderStatusEvent.OFFLINE_ASSIGN | OrderStatusEnum.DRIVER_CAR_CONFIRMED || OrderStatusEnum.DRIVER_CAR_CONFIRMED
        OrderStatusEnum.DRIVER_CAR_CONFIRMED | OrderStatusEvent.VBK_BIND_CAR   | OrderStatusEnum.DRIVER_CAR_CONFIRMED || OrderStatusEnum.DRIVER_CAR_CONFIRMED
        OrderStatusEnum.DRIVER_CAR_CONFIRMED | OrderStatusEvent.SAAS_BIND_CAR  | OrderStatusEnum.DRIVER_CAR_CONFIRMED || OrderStatusEnum.DRIVER_CAR_CONFIRMED
    }
}
