package com.ctrip.dcs.domain.common.value


import spock.lang.Specification
import spock.lang.Unroll

class WorkTimeEndVOTest extends Specification {

    def "test set End Time"() {
        given:
        WorkTimeEndVO workTimeEndVO = new WorkTimeEndVO("00:00")

        when:
        workTimeEndVO.setEndTime(4799)

        then:
        true
    }

    def "test set End Time2"() {
        given:
        WorkTimeEndVO workTimeEndVO = new WorkTimeEndVO("00:00")

        when:
        workTimeEndVO.setEndTime(4800)

        then:
        thrown(IllegalArgumentException)
    }

    @Unroll
    def "test plus Day If Before Start"() {
        given:
        WorkTimeEndVO workTimeEndVO = new WorkTimeEndVO("12:00")

        when:
        WorkTimeEndVO result = workTimeEndVO.plusDayIfBeforeStart(new WorkTimeStartVO(startTime))

        then:
        result.endTime == endTime

        where:
        startTime || endTime
        "12:00"   || 1200
        "11:59"   || 1200
        "12:01"   || 3600
    }

    @Unroll
    def "test after"() {
        given:
        WorkTimeEndVO workTimeEndVO = new WorkTimeEndVO(endTime)

        when:
        boolean result = workTimeEndVO.after(new WorkTimeStartVO(startTime), timeInt)

        then:
        result == r

        where:
        endTime | startTime | timeInt || r
        3500    | "12:00"   | 1201    || true
        1300    | "12:00"   | 1201    || true
        1300    | "12:00"   | 1300    || false
        1300    | "12:00"   | 1200    || true
        3500    | "12:00"   | 1000    || true
    }

    @Unroll
    def "test new2"() {
        when:
        new WorkTimeEndVO(hourAndMinuteString)
        then:
        thrown(IllegalArgumentException)

        where:
        hourAndMinuteString || e
        null                || IllegalArgumentException
        ""                  || IllegalArgumentException
        "null"              || IllegalArgumentException
        "null:"             || IllegalArgumentException
        "null:null"         || NumberFormatException
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme