package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.service.HeadTailOrderService
import com.ctrip.dcs.domain.common.value.DriverHeadTailOrderVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverHeadTailOrderVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driverVO
    @Mock
    HeadTailOrderService headTailOrderService
    @InjectMocks
    DriverHeadTailOrderVisitor driverHeadTailOrderVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(headTailOrderService.findHeadTailOrder(any(), any())).thenReturn([(1l): new DriverHeadTailOrderVO(1l, true, true, true, true, new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 36).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 36).getTime(), 0d, 0d, 0d, 0d, 0 as BigDecimal, 0 as BigDecimal)])
        DriverHeadTailOrderVisitor driverHeadTailOrderVisitor = new DriverHeadTailOrderVisitor(dspOrder, [driverVO], headTailOrderService)

        when:
        driverHeadTailOrderVisitor.visit(checkContext)

        then:
        checkContext.getDriverHeadTailOrderVOMap().size() == 0
    }

    def "test visit 2"() {
        given:
        when(checkContext.getDriverHeadTailOrderVOMap()).thenReturn([1L:new DriverHeadTailOrderVO()])
        when(driverVO.getDriverId()).thenReturn(1L)
        when(headTailOrderService.findHeadTailOrder(any(), any())).thenReturn([(1l): new DriverHeadTailOrderVO(1l, true, true, true, true, new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 36).getTime(), new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 36).getTime(), 0d, 0d, 0d, 0d, 0 as BigDecimal, 0 as BigDecimal)])
        DriverHeadTailOrderVisitor driverHeadTailOrderVisitor = new DriverHeadTailOrderVisitor(dspOrder, [driverVO], headTailOrderService)

        when:
        driverHeadTailOrderVisitor.visit(checkContext)

        then:
        checkContext.getDriverHeadTailOrderVOMap().size() > 0
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme