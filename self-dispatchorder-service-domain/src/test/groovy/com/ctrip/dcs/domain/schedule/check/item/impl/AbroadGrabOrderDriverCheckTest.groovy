package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class AbroadGrabOrderDriverCheckTest extends Specification {

    AbroadGrabOrderDriverCheck check = new AbroadGrabOrderDriverCheck()

    def "test"() {
        given: "设定相关方法入参"
        DspOrderVO dspOrder = Mock(DspOrderVO)
        DriverVO driver = Mock(DriverVO)
        DspModelVO dspModel = Mock(DspModelVO)
        CheckModel checkModel = Mock(CheckModel)
        CheckContext checkContext = Mock(CheckContext)

        checkModel.getModel() >> dspModel
        dspModel.getOrder() >> dspOrder
        dspModel.getDriver() >> driver
        driver.getDriverId() >> 1L
        driver.getCar() >> new CarVO(carTypeId: carTypeId)
        dspOrder.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        dspOrder.getCarTypeId() >> 117
        checkContext.getDriverPushConfigMap() >> [1L: new DriverPushConfigVO(orderTypes: [orderType], drvIntendVehicleTypes: [intendVehicleTyp])]
        when: "执行方法"
        CheckCode code = check.check(checkModel, checkContext)

        then: "验证方法返回值"
        code == result

        where:
        carTypeId | orderType | intendVehicleTyp || result
        null      | 1         | 1                || CheckCode.GRAB_ORDER_CHECK_DRIVER_NO_CAR
        0         | 1         | 1                || CheckCode.GRAB_ORDER_CHECK_DRIVER_NO_CAR
        117       | 1         | 1                || CheckCode.GRAB_ORDER_CHECK_ORDER_TYPE_FALSE
        118       | 2         | 118              || CheckCode.GRAB_ORDER_CHECK_CAR_TYPE_FALSE
        118       | 2         | 117              || CheckCode.PASS
        117       | 2         | 117              || CheckCode.PASS

    }
}
