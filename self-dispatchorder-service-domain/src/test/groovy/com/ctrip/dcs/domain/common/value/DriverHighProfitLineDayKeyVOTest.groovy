package com.ctrip.dcs.domain.common.value

import com.ctrip.dcs.domain.schedule.value.carconfig.DriverHighProfitLineDayKeyVO
import org.junit.platform.commons.util.StringUtils
import spock.lang.Specification
import spock.lang.Unroll

class DriverHighProfitLineDayKeyVOTest extends Specification {

    @Unroll
    def "test DriverHighProfitLineDayKeyVO"() {
        given:
        DriverHighProfitLineDayKeyVO vo = new DriverHighProfitLineDayKeyVO(1L, 2L)
        when:
        def result = vo.toKey()

        then:
        StringUtils.isNotBlank(result) == true

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme