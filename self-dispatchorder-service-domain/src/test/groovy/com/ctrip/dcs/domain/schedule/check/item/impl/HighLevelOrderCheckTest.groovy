package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.HighLevelCheckService
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Maps
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class HighLevelOrderCheckTest extends Specification {
    @Mock
    HighLevelCheckService highLevelCheckService
    @Mock
    Logger logger
    @Mock
    CheckModel checkModel
    @Mock
    CheckContext checkContext
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverVO driverVO
    @InjectMocks
    HighLevelOrderCheck highLevelOrderCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test check"() {
        given:
        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(driverVO.getDriverId()).thenReturn(1L)
        when(dspOrderVO.getHighGradeOrder()).thenReturn(highGradOrder)
        when(checkContext.getHighDriverMap()).thenReturn(highDriverMap)

        when:
        CheckCode result = highLevelOrderCheck.check(checkModel, checkContext)

        then:
        result == CheckCode.PASS

        where:
        highGradOrder | highDriverMap || code
        0             | [:]           || CheckCode.PASS
        0             | [:]           || CheckCode.PASS
        1             | [1: true]     || CheckCode.PASS
        1             | [1: false]    || CheckCode.HIGH_LEVEL_ORDER_NOT_GOLD_DRIVER_LIMIT
    }

    def "test downgrade"() {
        when:
        CheckCode result = highLevelOrderCheck.downgrade()

        then:
        result == CheckCode.PASS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme