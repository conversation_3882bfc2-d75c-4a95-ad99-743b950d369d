package com.ctrip.dcs.domain.schedule.visitor.impl

import cn.hutool.core.date.DateUtil
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import spock.lang.Specification

class DriverForwardAndBackwardOrderVisitorTest extends Specification {

    def orderService = Mock(QueryDspOrderService)
    def dspContext = Mock(DspContext)
    def sortContext = Mock(SortContext)
    def checkContext = Mock(CheckContext)

    def "visit(#title)"() {
        given:
        var driverList = [new DriverVO(driverId: driverId, workTimes: new PeriodsVO(workTimes))]
        var relateOrderList = Map.of(driverId, new DriverRelateOrderVO(driverId: driverId, frowardOrderInfo: frowardOrder, backwardOrderInfo: backwardOrder))
        var dispatchOrder = new DspOrderVO(estimatedUseTime: DateUtil.parse(estimatedUseTime))
        var forwardOrder = new HashMap()
        var backwardOrderMap = new HashMap()
        and:
        var target = new DriverForwardAndBackwardOrderVisitor(dispatchOrder, driverList, orderService)
        when:
        sortContext.getDspContext() >> dspContext
        checkContext.getContext() >> dspContext
        dspContext.getDriverForwardOrderMap() >> forwardOrder
        dspContext.getDriverBackwardOrderMap() >> backwardOrderMap
        orderService.queryRelateOrders(_, _, _, _) >> relateOrderList
        then:
        target.visit(checkContext)
        target.visit(sortContext)
        expect:
        forwardOrder.containsKey(driverId) == hasForwardOrder
        backwardOrderMap.containsKey(driverId) == hasBackwardOrder
        where:
        title                       | estimatedUseTime      | driverId | workTimes       | frowardOrder                                                            | backwardOrder                                                           | hasForwardOrder | hasBackwardOrder
        "前后向单都为空"            | "2025-04-15 09:40:00" | 1L       | null            | null                                                                    | null                                                                    | false           | false
        "前后向单都在闭区间边界"    | "2025-04-15 09:40:00" | 2L       | ["08:30~14:30"] | new DspOrderVO(estimatedUseTime: DateUtil.parse("2025-04-15 07:30:00")) | new DspOrderVO(estimatedUseTime: DateUtil.parse("2025-04-15 16:30:00")) | true            | false
        "前后向单都在闭区间外"      | "2025-04-15 09:40:00" | 3L       | ["08:30~14:30"] | new DspOrderVO(estimatedUseTime: DateUtil.parse("2025-04-15 05:30:00")) | new DspOrderVO(estimatedUseTime: DateUtil.parse("2025-04-15 17:30:00")) | false           | false
        "待派单不在司机工作时间内1" | "2025-04-15 07:40:00" | 4L       | ["08:30~14:30"] | new DspOrderVO(estimatedUseTime: DateUtil.parse("2025-04-15 07:30:00")) | new DspOrderVO(estimatedUseTime: DateUtil.parse("2025-04-15 16:30:00")) | false           | false
        "待派单不在司机工作时间内2" | "2025-04-15 15:40:00" | 4L       | ["08:30~14:30"] | new DspOrderVO(estimatedUseTime: DateUtil.parse("2025-04-15 07:30:00")) | new DspOrderVO(estimatedUseTime: DateUtil.parse("2025-04-15 16:30:00")) | false           | false
    }

}