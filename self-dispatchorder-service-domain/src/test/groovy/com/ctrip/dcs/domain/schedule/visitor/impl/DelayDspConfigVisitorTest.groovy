package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.gateway.DspDelayGateway
import com.ctrip.dcs.domain.schedule.value.carconfig.DelayDspConfigVO
import spock.lang.Specification

class DelayDspConfigVisitorTest extends Specification {
    def order = Mock(DspOrderVO)
    def dspDelayGateway = Mock(DspDelayGateway)
    def visitor = new DelayDspConfigVisitor(order, dspDelayGateway)

    def "test visit with queryDelayDspConfig is null "() {
        given:
        dspDelayGateway.queryDelayDspConfig(_) >> null
        def context = new CheckContext()

        when:
        visitor.visit(context)

        then:
        context.getDelayDspConfigMap().size() == 0
    }

    def "test visit with queryDelayDspConfig is not null "() {
        given:
        dspDelayGateway.queryDelayDspConfig(_) >> new DelayDspConfigVO()
        def context = new CheckContext()

        when:
        visitor.visit(context)

        then:
        context.getDelayDspConfigMap().size() == 1
    }

}
