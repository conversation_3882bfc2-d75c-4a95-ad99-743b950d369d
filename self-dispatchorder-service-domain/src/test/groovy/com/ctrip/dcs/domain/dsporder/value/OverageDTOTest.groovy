package com.ctrip.dcs.domain.dsporder.value

import org.junit.Assert
import spock.lang.Specification

/**
 * <AUTHOR>
 * @since 2025/5/6 10:56
 */
class OverageDTOTest extends Specification {
    def testObj = new OverageDTO()



    def "test"() {
        given:
        when:
        OverageDTO overageDTO = new OverageDTO();
        overageDTO.setCityId(1L)
        overageDTO.setVehicleTypeId(2L)
        overageDTO.setAccessLimit(3.0D)
        overageDTO.setOverage(4.1D)
        then:
        Assert.assertTrue(Objects.equals(overageDTO.getCityId(), 1L))
        Assert.assertTrue(Objects.equals(overageDTO.getVehicleTypeId(), 2L))
        Assert.assertTrue(Objects.equals(overageDTO.getAccessLimit(), 3.0D))
        Assert.assertTrue(Objects.equals(overageDTO.getOverage(), 4.1D))
    }

}
