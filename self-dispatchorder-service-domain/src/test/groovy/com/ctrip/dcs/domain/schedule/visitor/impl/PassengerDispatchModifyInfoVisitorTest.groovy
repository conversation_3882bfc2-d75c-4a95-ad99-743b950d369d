package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.QueryOrderDispatchModifyInfoService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class PassengerDispatchModifyInfoVisitorTest extends Specification {
    @Mock
    QueryOrderDispatchModifyInfoService queryService
    @Mock
    CheckContext context
    List<String> orderDispatchModifyDriverIds = new ArrayList<String>()
    List<String> orderDispatchModifySupplierIds = new ArrayList<String>()
    @InjectMocks
    PassengerDispatchModifyInfoVisitor passengerDispatchModifyInfoVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        PassengerDispatchModifyInfoVisitor visitor = new PassengerDispatchModifyInfoVisitor("1", queryService)
        when(queryService.queryOrderDspModifyDriver(anyString())).thenReturn(["1"])
        when(queryService.queryOrderDspModifySupplier(anyString())).thenReturn(["2"])
        when(context.getOrderDispatchModifyDriverIds()).thenReturn(orderDispatchModifyDriverIds)
        when(context.getOrderDispatchModifySupplierIds()).thenReturn(orderDispatchModifySupplierIds)
        when:
        visitor.visit(context)

        then:
        orderDispatchModifyDriverIds.contains("1")
        orderDispatchModifySupplierIds.contains("2")
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme