package com.ctrip.dcs.domain.dsporder.entity

import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.*

class IvrRecordDOTest extends Specification {

    @InjectMocks
    IvrRecordDO ivrRecordDO

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {

        when:
        ivrRecordDO.setRecordGuid("11");
        ivrRecordDO.setSupplyOrderId("12");
        ivrRecordDO.setRecordGuid("11");
        ivrRecordDO.setBodyNumber("2023-01-01");
        ivrRecordDO.setRefBookTime("2025-01-01");
        ivrRecordDO.setSupplierId(1);
        ivrRecordDO.setSupplierName("trip");
        ivrRecordDO.setBizType(6);
        ivrRecordDO.setCountryCode("86");
        ivrRecordDO.setCityCode("");
        ivrRecordDO.setBodyNumber("10012");
        ivrRecordDO.setCallPhoneType(1);
        ivrRecordDO.setPhoneStatusCode("complete");
        ivrRecordDO.setCallTime("20232")
        ivrRecordDO.setPickUpTime("s");
        ivrRecordDO.setHangUpTime("1");
        ivrRecordDO.setIvrCount(1);
        ivrRecordDO.setContent("3dsdf");
        ivrRecordDO.setLanguageCode("cn");

        ivrRecordDO.getUserOrderId();
        ivrRecordDO.getSupplyOrderId();
        ivrRecordDO.getRecordGuid();
        ivrRecordDO.getBodyNumber();
        ivrRecordDO.getRefBookTime();
        ivrRecordDO.getBookTime();
        ivrRecordDO.getSupplierId();
        ivrRecordDO.getSupplierName();
        ivrRecordDO.getBizType();
        ivrRecordDO.getCountryCode();
        ivrRecordDO.getCityCode();
        ivrRecordDO.getBodyNumber();
        ivrRecordDO.getCallPhoneType();
        ivrRecordDO.getPhoneStatusCode();
        ivrRecordDO.getCallTime();
        ivrRecordDO.getPickUpTime();
        ivrRecordDO.getIvrCount();
        ivrRecordDO.getContent();
        ivrRecordDO.getLanguageCode();




        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme