package com.ctrip.dcs.domain.schedule.sort.feature.impl


import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverMileageProfitVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverPointFeature
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class DriverGrabTimeFeatureSortTest extends Specification {
    @Mock
    SortModel model
    @Mock
    SortContext context
    @Mock
    DriverVO driver
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverMileageProfitVO dmp
    @Mock
    DriverMileageProfitVO dmp1
    @Mock
    GrabOrderDO grabOrder
    @Mock
    CarVO car

    @InjectMocks
    DriverGrabTimeFeature feature = new DriverGrabTimeFeature()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(model.getModel()).thenReturn(dspModelVO)
        Mockito.when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getDspOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getExpectDriverMileageProfit(Mockito.any())).thenReturn(dmp)
        Mockito.when(context.getCompleteDriverMileageProfit(Mockito.any())).thenReturn(dmp1)

        Mockito.when(dspModelVO.getDriver()).thenReturn(driver)
        Mockito.when(driver.getDriverId()).thenReturn(1L)
        Mockito.when(driver.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
    }

    @Unroll
    def "test value"() {
        given:
        DriverPointsVO point = new DriverPointsVO()
        point.setDriverId(1L)
        point.setPoints(99D)
        Optional<DriverPointsVO> optional = Optional.of(point)

        Mockito.when(context.getDriverGrabOrderMap()).thenReturn([1L: grabOrder])
        Mockito.when(grabOrder.getSubmitTime()).thenReturn(submitTime)


        when:
        Value res = feature.value(model, context)

        then:
        res.value == value

        where:
        submitTime || value
        null       || Integer.MAX_VALUE
        1L         || 1d
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme