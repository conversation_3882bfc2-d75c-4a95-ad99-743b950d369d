package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.value.SeriesVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.CarSeriesGateway
import com.google.common.collect.Sets
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class CarSeriesVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO order
    @Mock
    CarSeriesGateway carSeriesGateway
    @InjectMocks
    CarSeriesVisitor carSeriesVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(checkContext.getCarSeriesMap()).thenReturn(["a":new SeriesVO(Sets.newHashSet(1L))])
        when(order.getDspOrderId()).thenReturn("getDspOrderIdResponse")
        when(order.getCityId()).thenReturn(0)
        when(order.getCarTypeId()).thenReturn(0)
        when(order.getCategoryCode()).thenReturn(CategoryCodeEnum.C_DAY_RENTAL)
        when(carSeriesGateway.query(anyInt(), anyInt())).thenReturn(new SeriesVO([1l] as Set<Long>))

        when:
        carSeriesVisitor.visit(checkContext)

        then:
        checkContext.getCarSeriesMap().isEmpty() == false
    }

    def "test visit 2"() {
        given:
        when(checkContext.getCarSeriesMap()).thenReturn(["a":new SeriesVO(Sets.newHashSet(1L))])
        when(order.getDspOrderId()).thenReturn("a")
        when(order.getCityId()).thenReturn(0)
        when(order.getCarTypeId()).thenReturn(0)
        when(order.getCategoryCode()).thenReturn(CategoryCodeEnum.C_DAY_RENTAL)
        when(carSeriesGateway.query(anyInt(), anyInt())).thenReturn(new SeriesVO([1l] as Set<Long>))

        when:
        carSeriesVisitor.visit(checkContext)

        then:
        checkContext.getCarSeriesMap().isEmpty() == false
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme