package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverMonthProfitFeature
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class DriverMonthProfitFeatureSortTest extends Specification {
    @Mock
    SortModel model
    @Mock
    SortContext context
    @Mock
    DriverVO driver
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverMileageProfitVO dmp
    @Mock
    DriverMileageProfitVO dmp1
    @Mock
    CarVO car

    @InjectMocks
    DriverMonthProfitFeature feature = new DriverMonthProfitFeature()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(model.getModel()).thenReturn(dspModelVO)
        Mockito.when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getDspOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getExpectDriverMileageProfit(Mockito.any())).thenReturn(dmp)
        Mockito.when(context.getCompleteDriverMileageProfit(Mockito.any())).thenReturn(dmp1)

        Mockito.when(dspModelVO.getDriver()).thenReturn(driver)
        Mockito.when(driver.getDriverId()).thenReturn(1L)
        Mockito.when(driver.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
    }

    @Unroll
    def "test value"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(context.getProfitBaselineMonth(Mockito.any(), Mockito.any())).thenReturn(5)

        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp1.getProfit()).thenReturn(11.0D)

        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)


        when:
        Value res = feature.value(model, context)

        then:
        res.value == 0.0


    }

    @Unroll
    def "test value1"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(context.getProfitBaselineMonth(Mockito.any(), Mockito.any())).thenReturn(50)

        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp1.getProfit()).thenReturn(11.0D)

        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)


        when:
        Value res = feature.value(model, context)

        then:
        res.value > 0.0


    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme