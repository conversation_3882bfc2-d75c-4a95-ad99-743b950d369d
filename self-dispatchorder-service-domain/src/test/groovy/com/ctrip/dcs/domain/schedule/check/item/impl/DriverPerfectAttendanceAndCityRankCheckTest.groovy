package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification

class DriverPerfectAttendanceAndCityRankCheckTest extends Specification {

    DriverPerfectAttendanceAndCityRankCheck instance = new DriverPerfectAttendanceAndCityRankCheck()

    CheckContext checkContext = Mock()

    DspContext dispatchContext = Mock()

    DspOrderVO dispatchOrder = Mock()

    CheckModel checkModel = Mock()

    DspModelVO dispatchModel = Mock()

    DriverPointsVO driverPoints = Mock()

    DriverVO driver = Mock()

    def "test load"() {
        given:
        checkContext.getDspOrder() >> dispatchOrder
        checkContext.getCityProperties(_, _, _) >> driverRankThreshold
        checkContext.getProperties(_, _ as Double) >> Double.valueOf("12")
        checkContext.getContext() >> dispatchContext
        dispatchContext.getDriverPointsMap() >> Map.of(driverId, driverPoints)
        driverPoints.getCityRanking() >> driverRankValue
        dispatchOrder.getCityId() >> 999
        checkModel.getModel() >> dispatchModel
        dispatchModel.getDriver() >> driver
        driver.getDriverId() >> driverId
        driver.getWorkTimes() >> new PeriodsVO(driverWorkPeriods)
        when:
        CheckCode actual = instance.check(checkModel, checkContext)
        then:
        actual == expected
        where:
        driverId | driverRankValue | driverRankThreshold | driverWorkPeriods | expected
        1L       | 50L             | 49                  | []                | CheckCode.DRIVER_CITY_RANK_GREAT_THAN_THRESHOLD
        1L       | 50L             | 50                  | []                | CheckCode.DRIVER_WORK_TIME_PERIOD_INCOMPLETE
        1L | 50L | 50 | ["08:00~09:00", "14:00~15:00"] | CheckCode.DRIVER_WORK_TIME_PERIOD_INCOMPLETE
        1L | 50L | 50 | ["08:00~09:00", "14:00~15:00", "19:00~20:00"] | CheckCode.DRIVER_WORK_TIME_PERIOD_INCOMPLETE
        1L | 50L | 50 | ["08:00~09:00"] | CheckCode.DRIVER_WORK_HOURS_LESS_THAN_THRESHOLD
        1L | 50L | 50 | ["02:00~22:00"] | CheckCode.PASS
        1L | 50L | 50 | ["12:00~23:59", "00:00~08:00"] | CheckCode.PASS
        1L | 50L | 50 | ["12:00~08:00"] | CheckCode.PASS
        1L | 50L | 50 | ["00:00~22:00"] | CheckCode.PASS
    }

}