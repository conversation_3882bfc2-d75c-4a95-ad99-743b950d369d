package com.ctrip.dcs.domain.common.value

import com.ctrip.dcs.domain.dsporder.value.DriverLeaveRightsVO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverLeaveRightsVOTest extends Specification {

    def "test "() {
        when:

        def leave = new DriverLeaveRightsVO(1L,true,1)
        def leave1 = new DriverLeaveRightsVO(1L,1,[])
        def base = new BaseDetailVO(estimatedUseTime:"2023-12-30 10:00:01",supplyOrderId: 1,userOrderId: 1)
        def b = leave.useRights(base)
        def list = leave.getUseDriveLeaveRightRecord()






        then:
        b==true
        list!=null == true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme