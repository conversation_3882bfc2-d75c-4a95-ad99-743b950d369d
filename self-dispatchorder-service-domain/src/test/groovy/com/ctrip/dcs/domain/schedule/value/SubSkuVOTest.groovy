package com.ctrip.dcs.domain.schedule.value

import cn.hutool.core.lang.Pair
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class SubSkuVOTest extends Specification {

    SubSkuVO subSkuVO = new SubSkuVO(retrySecond: 10, roundRetryFactor: [Pair.of(100, 3d), Pair.of(10, 2d)])

    def "GetRoundRetrySecond"() {
        given:
        when:
        Long result = subSkuVO.getRoundRetrySecond(round)
        then:
        result == second
        where:
        round || second
        1     || 10
        10    || 10
        11    || 20
        20    || 20
        100   || 20
        101   || 30
        999   || 30
    }
}
