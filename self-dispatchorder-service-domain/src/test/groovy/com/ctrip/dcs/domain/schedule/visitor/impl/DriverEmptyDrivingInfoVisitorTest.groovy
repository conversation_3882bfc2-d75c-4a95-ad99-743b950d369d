package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.DriverDeadHeadDisService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverEmptyDrivingInfoVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driver
    @Mock
    DriverDeadHeadDisService driverDeadHeadDisService
    @Mock
    DriverLocationVisitor driverLocationVisitor
    @Mock
    DriverOrderListVisitor driverOrderListVisitor
    @Mock
    DeadHeadDisModelVO deadHeadDisModelVO
    @InjectMocks
    DriverEmptyDrivingInfoVisitor driverEmptyDrivingInfoVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(checkContext.getDriverEmptyDrivingInfo4OrderMileageMap()).thenReturn([(1l): deadHeadDisModelVO])
        when(driver.getDriverId()).thenReturn(1L)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspOrder.getDspOrderId()).thenReturn("getDspOrderIdResponse")
        when(dspOrder.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 48).getTime())
        when(driverDeadHeadDisService.calculateDeadHeadDisForOrderMileage(any(), any(), any(), any())).thenReturn([(1l): deadHeadDisModelVO])
        when(driverLocationVisitor.filter(any(), any())).thenReturn([driver])
        when(driverOrderListVisitor.filter(any(), any())).thenReturn([driver])
        DriverEmptyDrivingInfoVisitor driverEmptyDrivingInfoVisitor = new DriverEmptyDrivingInfoVisitor(dspOrder, [driver], driverDeadHeadDisService, driverLocationVisitor, driverOrderListVisitor)

        when:
        def result = driverEmptyDrivingInfoVisitor.visit(checkContext)

        then:
        result == null
    }

    def "test visit 2"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(driver.getDriverId()).thenReturn(1L)
        when(dspContext.getOrderListMap()).thenReturn([1L:[dspOrder]])
        when(dspOrder.getDspOrderId()).thenReturn("getDspOrderIdResponse")
        when(dspOrder.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 48).getTime())
        when(driverDeadHeadDisService.calculateDeadHeadDisForOrderMileage(any(), any(), any(), any())).thenReturn([(1l): deadHeadDisModelVO])
        when(driverLocationVisitor.filter(any(), any())).thenReturn([driver])
        when(driverOrderListVisitor.filter(any(), any())).thenReturn([driver])
        DriverEmptyDrivingInfoVisitor driverEmptyDrivingInfoVisitor = new DriverEmptyDrivingInfoVisitor(dspOrder, [driver], driverDeadHeadDisService, driverLocationVisitor, driverOrderListVisitor)

        when:
        def result = driverEmptyDrivingInfoVisitor.visit(checkContext)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme