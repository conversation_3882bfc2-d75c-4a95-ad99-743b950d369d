package com.ctrip.dcs.domain.schedule.process.impl

import com.ctrip.dcs.domain.common.enums.DispatcherGrabOrderStatusEnum
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.service.IdempotentCheckService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.dsporder.gateway.IGTOrderQueryServiceGateway
import com.ctrip.dcs.domain.schedule.entity.DispatcherGrabOrderDO
import com.ctrip.dcs.domain.schedule.factory.DispatcherGrabOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DispatcherGrabOrderGateway
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class RefuseAssignTransportProcessTest extends Specification {
    @Mock
    Logger logger
    @Mock
    IdempotentCheckService idempotentCheckService
    @Mock
    DispatcherGrabOrderGateway dispatcherGrabOrderGateway
    @Mock
    MessageProviderService messageProviderService
    @Mock
    CheckService checkService
    @Mock
    DispatcherGrabOrderFactory dispatcherGrabOrderFactory
    @Mock
    DispatcherGrabOrderDO order

    @Mock
    private IGTOrderQueryServiceGateway igtOrderQueryServiceGateway;

    @InjectMocks
    RefuseAssignTransportProcess refuseAssignTransportProcess

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        DispatcherGrabOrderDO order = new DispatcherGrabOrderDO(0L, "", "", 1L, 1L, 1, "", DispatcherGrabOrderStatusEnum.INIT, 1, "", "", "", new Date(),0)
        when(dispatcherGrabOrderGateway.create(any())).thenReturn([order])
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)

        when:
        refuseAssignTransportProcess.execute([order])

        then:
        verify(messageProviderService).send(any())
    }

    def "test execute 1"() {
        given:
        DispatcherGrabOrderDO order = new DispatcherGrabOrderDO(0L, "", "", 1L, 1L, 1, "", DispatcherGrabOrderStatusEnum.INIT, 1, "", "", "", new Date(),0)
        when(dispatcherGrabOrderGateway.create(any())).thenReturn([order])
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)

        when:
        refuseAssignTransportProcess.execute([])

        then:
        verify(messageProviderService, never()).send(any())
    }

    def "test execute 2"() {
        given:
        DispatcherGrabOrderDO order = new DispatcherGrabOrderDO(0L, "", "", 1L, 1L, 1, "", DispatcherGrabOrderStatusEnum.INIT, 1, "", "", "", new Date(),0)
        when(dispatcherGrabOrderGateway.create(any())).thenReturn([order])
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(false)

        when:
        refuseAssignTransportProcess.execute([order])

        then:
        verify(messageProviderService, never()).send(any())
    }

    def "test execute 3"() {
        given:
        DispatcherGrabOrderDO order = new DispatcherGrabOrderDO(0L, "", "", 1L, 1L, 1, "", DispatcherGrabOrderStatusEnum.INIT, 1, "", "", "", new Date(),0)
        when(dispatcherGrabOrderGateway.create(any())).thenReturn([])
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)

        when:
        refuseAssignTransportProcess.execute([order])

        then:
        verify(messageProviderService, never()).send(any())
    }

    def "test execute fail"() {
        given:
        DispatcherGrabOrderDO order = new DispatcherGrabOrderDO(0L, "", "", 1L, 1L, 1, "", DispatcherGrabOrderStatusEnum.INIT, 1, "", "", "", new Date(),0)
        when(dispatcherGrabOrderGateway.create(any())).thenThrow(new RuntimeException())
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)

        when:
        refuseAssignTransportProcess.execute([order])

        then:
        BizException e = thrown(BizException)
        e.getCode() == ErrorCode.CREATE_DISPATCH_GRAB_ORDER_ERROR.getCode()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
