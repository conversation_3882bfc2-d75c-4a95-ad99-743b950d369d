package com.ctrip.dcs.domain.schedule.check.item.impl


import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.EstimatedUseTimeVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.carconfig.DelayDspConfigVO
import spock.lang.Specification

class CrossCityWithJudgeValidTimeCheckTest extends Specification {
    def check = new CrossCityWithJudgeValidTimeCheck()

    def context = Mock(CheckContext)
    def checkModel = Mock(CheckModel)
    def dspOrder = Mock(DspOrderVO)
    def driver = Mock(DriverVO)
    def model = Mock(DspModelVO)

    def setup() {
        context.getDspOrder() >> dspOrder
        checkModel.getModel() >> model
        model.getDriver() >> driver
        model.getOrder() >> dspOrder
    }

    def "test check method with order is not crossCity order"() {
        given:
        dspOrder.isCrossCityOrder() >> false

        expect:
        CheckCode.PASS == check.check(checkModel, context)
    }

    def "test check method with no delay dsp config"() {
        given:
        dspOrder.isCrossCityOrder() >> true
        context.getDelayDspConfigMap() >> [:]
        dspOrder.estimatedUseTime() >> new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2024-01-02 12:00:00"))

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check method with non-first class driver"() {
        given:
        dspOrder.isCrossCityOrder() >> true
        context.getDelayDspConfigMap() >> [(dspOrder.getDspOrderId()): new DelayDspConfigVO(taskDeadTime: "2023-12-31 23:59:59")]
        dspOrder.estimatedUseTime() >> new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2024-01-02 12:00:00"))
        context.getFirstClassDriverList() >> []
        driver.isRegisterDriver() >> false

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check method with valid time"() {
        given:
        dspOrder.isCrossCityOrder() >> true
        context.getDelayDspConfigMap() >> [(dspOrder.getDspOrderId()): new DelayDspConfigVO(taskDeadTime: "2099-12-31 23:59:59")]
        dspOrder.estimatedUseTime() >> new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2024-01-02 12:00:00"))
        context.getFirstClassDriverList() >> [driver.getDriverId()]
        driver.isRegisterDriver() >> true

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.CROSS_CITY_ORDER_NOT_DSP_FIRST_CLASS_DRIVER_BEFORE_DELAY_DSP
    }

    def "test check method with not valid time"() {
        given:
        dspOrder.isCrossCityOrder() >> true
        context.getDelayDspConfigMap() >> [(dspOrder.getDspOrderId()): new DelayDspConfigVO(taskDeadTime: "2023-12-31 23:59:59")]
        dspOrder.estimatedUseTime() >> new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2024-01-02 12:00:00"))
        context.getFirstClassDriverList() >> [driver.getDriverId()]
        driver.isRegisterDriver() >> true

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }
}
