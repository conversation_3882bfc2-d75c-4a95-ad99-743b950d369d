package com.ctrip.dcs.domain.common.value


import spock.lang.Specification

/**
 * <AUTHOR>
 */
class TransportGroupPhoneOrLanguageVOTest extends Specification {

    def "test "() {
        when:

        def phone = new TransportGroupPhoneOrLanguageVO()
        phone.setBackupPhoneBodyNumber("1")
        phone.setBackupPhoneCityCode("1")
        phone.setBackupPhoneCountryCode("1")
        phone.setMainPhoneBodyNumber("1")
        phone.setMainPhoneCityCode("1")
        phone.setMainPhoneBodyNumber("1")
        phone.setTransportGroupId(1L
        )
        phone.setSupportLanguageList(["1"])
        phone.setMainPhoneCountryCode("1")





        then:
        phone.getBackupPhoneBodyNumber()=="1"
        phone.getBackupPhoneCityCode()=="1"
        phone.getBackupPhoneCountryCode()=="1"
        phone.getMainPhoneBodyNumber()=="1"
        phone.getMainPhoneCityCode()=="1"
        phone.getMainPhoneBodyNumber()=="1"
        phone.getTransportGroupId()==1
        phone.getSupportLanguageList().size()==1
        phone.getMainPhoneCountryCode()=="1"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme