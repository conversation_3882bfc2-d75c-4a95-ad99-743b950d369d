package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.sort.feature.Feature
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.score.impl.WeightScorer
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Specification
import spock.lang.Unroll

import java.util.stream.Collectors

/**
 * <AUTHOR>
 */
class SorterTest extends Specification {

    def sortContext = Mock(SortContext)
    def sm1 = Mock(SortModel)
    def sm2 = Mock(SortModel)
    def feature = Mock(Feature)

    @Unroll
    def "test sort"() {

        given:
        Map<String, Double> weights = Maps.newHashMap()
        weights.put("1", 1D)
        weights.put("2", 0.9D)
        def score = new WeightScorer(weights)
        def sorter = new Sorter(score)
        sorter.addFeature(feature);
        when:
        List<SortModel> models = sorter.sort(Lists.newArrayList(sm1, sm2), sortContext)

        then:
        models.size() == 2
    }

    def "batchDeleteValuesAndScore"(){
        given:
        def dspOrder = Mock(DspOrderVO)
        def driver = Mock(DriverVO)
        SortModel sortModel = new SortModel(new DspModelVO(dspOrder, driver))
        sortModel.addValue(new Value("F38",41.0D))
        Map<String, Double> weights = new HashMap<>()
        weights.put("F38",1.0D)
        sortModel.score(new WeightScorer(weights))
        when:
        SortModel.batchDeleteValuesAndScore(Arrays.asList(sortModel))
        SortModel.batchLog(Arrays.asList(sortModel))
        then:
        sortModel.getScore() == 0.0
        sortModel.getValues().size() == 0
    }
}
