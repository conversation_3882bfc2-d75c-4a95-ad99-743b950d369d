package com.ctrip.dcs.domain.common.value

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.platform.dal.dao.helper.JsonUtils
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit

class DspOrderVOTest extends Specification {

    @Unroll
    def "test estimated Use Time"() {
        given:
        def dspOrderVO = new DspOrderVO(
                estimatedUseTime: estimatedUseTime
        )

        when:
        EstimatedUseTimeVO result = dspOrderVO.estimatedUseTime()

        then:
        result.timeInt == timeInt

        where:
        estimatedUseTime                      || timeInt
        null                                  || 800
        new Date(TimeUnit.HOURS.toMillis(8))  || 1600
        new Date(TimeUnit.HOURS.toMillis(23)) || 700
    }

    @Unroll
    def "test getValueFromExtendInfo"() {
        given:
        def dspOrderVO = new DspOrderVO(
                estimatedUseTime: new Date(TimeUnit.HOURS.toMillis(8))
        )
        dspOrderVO.setExtendInfo(extendInfo)

        when:
        Integer result = dspOrderVO.getValueFromExtendInfo("key", 1)

        then:
        result == timeInt

        where:
        extendInfo  || timeInt
        null   || 1
        getMap()|| 2
    }


    @Unroll
    def "test isTakenOrderStatus"() {
        given:
        def dspOrderVO = new DspOrderVO(
                orderStatus: orderStatus
        )
        when:
        def result = dspOrderVO.isTakenOrderStatus();

        then:
        Objects.equals(result, expect)

        where:
        orderStatus || expect
        220         || false
        240         || true
    }

    private String getMap(){
        Map<String, Integer> map = new HashMap<>();
        map.put("key", 2)
        return JsonUtils.toJson(map)
    }


    def "test isCharterOrder"() {
        given:
        def dspOrderVO = new DspOrderVO(
                "categoryCode": categoryCodeEnum
        )
        when:
        def result= dspOrderVO.isCharterOrder();

        then:
        Assert.assertTrue(Objects.equals(result, expect))

        where:
        categoryCodeEnum              || expect
        CategoryCodeEnum.FROM_AIRPORT || false
        CategoryCodeEnum.DAY_RENTAL   || true
        CategoryCodeEnum.C_DAY_RENTAL || true
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme