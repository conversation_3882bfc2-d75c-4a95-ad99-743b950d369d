package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DriverLeaveVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverLeaveCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    ConfigService configService
    @Mock
    DriverLeaveVO driverLeaveVO
    @InjectMocks
    DriverLeaveCheck driverLeaveCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(context.getConfigService()).thenReturn(configService)
    }

    def "司机无请假"() {
        given:
        Mockito.when(context.getDriverLeave(Mockito.any())).thenReturn(Optional.ofNullable(null))
        Mockito.when(order.getEstimatedUseTime()).thenReturn(DateUtil.parseDateStr2Date("2023-11-13 22:20:00"))
        Mockito.when(order.getPredicServiceStopTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 13:00:00"))

        when:
        CheckCode result = driverLeaveCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机有请假，但用车时间和结束时间不在范围"() {
        given:
        Mockito.when(context.getDriverLeave(Mockito.any())).thenReturn(Optional.ofNullable([driverLeaveVO]))
        Mockito.when(order.getEstimatedUseTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 12:00:00"))
        Mockito.when(order.getPredicServiceStopTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 13:00:00"))
        Mockito.when(driverLeaveVO.getLeaveBeginTime()).thenReturn("2023-05-13 10:00:00")
        Mockito.when(driverLeaveVO.getLeaveEndTime()).thenReturn("2023-05-13 11:00:00")

        when:
        CheckCode result = driverLeaveCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机有请假，用车时间或者结束时间在范围"() {
        given:
        Mockito.when(context.getDriverLeave(Mockito.any())).thenReturn(Optional.ofNullable([driverLeaveVO]))
        Mockito.when(order.getEstimatedUseTime()).thenReturn(DateUtil.parseDateStr2Date("2023-11-13 22:20:00"))
        Mockito.when(order.getPredicServiceStopTime()).thenReturn(DateUtil.parseDateStr2Date("2023-11-13 23:19:00"))
        Mockito.when(driverLeaveVO.getLeaveBeginTime()).thenReturn("2023-11-13 18:00:00")
        Mockito.when(driverLeaveVO.getLeaveEndTime()).thenReturn("2023-11-13 22:25:00")

        when:
        CheckCode result = driverLeaveCheck.check(checkModel, context)

        then:
        result == CheckCode.LEAVE_STATUS
    }

    def "司机有请假，用车时间和结束时间包含请假时间"() {
        given:
        Mockito.when(context.getDriverLeave(Mockito.any())).thenReturn(Optional.ofNullable([driverLeaveVO]))
        Mockito.when(order.getEstimatedUseTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 12:00:00"))
        Mockito.when(order.getPredicServiceStopTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 15:00:00"))
        Mockito.when(driverLeaveVO.getLeaveBeginTime()).thenReturn("2023-05-13 13:00:00")
        Mockito.when(driverLeaveVO.getLeaveEndTime()).thenReturn("2023-05-13 14:00:00")

        when:
        CheckCode result = driverLeaveCheck.check(checkModel, context)

        then:
        result == CheckCode.LEAVE_STATUS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme