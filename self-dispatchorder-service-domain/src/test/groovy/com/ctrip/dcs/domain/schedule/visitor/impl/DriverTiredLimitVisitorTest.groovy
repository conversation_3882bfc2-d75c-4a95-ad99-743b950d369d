package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.QueryDriverTiredService
import com.ctrip.dcs.domain.common.value.DriverTiredLimitVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.igt.framework.common.spring.InstanceLocator
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverTiredLimitVisitorTest extends Specification {
    @Mock
    DspOrderVO dspOrder
    @Mock
    List<DriverVO> drivers
    @Mock
    QueryDriverTiredService queryDriverTiredService
    @Mock
    CheckContext checkContext
    @Mock
    DriverTiredLimitVO driverTiredLimitVO
    Set<Long> set = new HashSet<>()

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(driverTiredLimitVO.getDriverId()).thenReturn(1L)
        when(driverTiredLimitVO.isTired()).thenReturn(true)
        when(checkContext.getTiredLimitDriverIds()).thenReturn(set)
        when(queryDriverTiredService.query(dspOrder, drivers)).thenReturn([driverTiredLimitVO])

        when:
        DriverTiredLimitVisitor visitor = new DriverTiredLimitVisitor(dspOrder, drivers, queryDriverTiredService)
        visitor.visit(checkContext)

        then:
        set.size() > 0
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme