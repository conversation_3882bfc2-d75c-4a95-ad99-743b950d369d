package com.ctrip.dcs.domain.schedule.exception

import com.ctrip.dcs.domain.schedule.check.CheckCode
import spock.lang.Specification

class CheckItemExceptionTest extends Specification {

    def "test get Check Code"() {
        given:
        def e = new CheckItemException(CheckCode.APPOINT_VEHICLE_SERIES_DRIVER)

        when:
        def code = e.getCheckCode()

        then:
        code == CheckCode.APPOINT_VEHICLE_SERIES_DRIVER
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme