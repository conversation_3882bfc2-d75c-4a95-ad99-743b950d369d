package com.ctrip.dcs.domain.schedule.check.item.impl


import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.XSkuCategoryCode
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class PickUpCardCheckTest extends Specification {
    @Mock
    Logger logger

    @Mock
    DspOrderVO dspOrder

    @Mock
    DriverVO driver

    @Mock
    TransportGroupVO transportGroup

    @Mock
    XproductVO xproduct;

    @InjectMocks
    PickUpCardCheck pickUpCardCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "非举牌接机订单"() {
        when:
        CheckCode result = pickUpCardCheck.check(new CheckModel(new DspModelVO(dspOrder, driver)), new CheckContext())

        then:
        result == CheckCode.PASS
    }

    def "举牌接机订单，司机支持"() {
        Mockito.when(dspOrder.getXproductList()).thenReturn(Lists.newArrayList(xproduct))
        Mockito.when(xproduct.getCategoryCode()).thenReturn(XSkuCategoryCode.PICK_UP_CARD.getCode())
        Mockito.when(driver.getRaisingPickUp()).thenReturn(true)
        when:
        CheckCode result = pickUpCardCheck.check(new CheckModel(new DspModelVO(dspOrder, driver)), new CheckContext())

        then:
        result == CheckCode.PASS
    }

    def "举牌接机订单，司机不支持"() {
        Mockito.when(dspOrder.getXproductList()).thenReturn(Lists.newArrayList(xproduct))
        Mockito.when(xproduct.getCategoryCode()).thenReturn(XSkuCategoryCode.PICK_UP_CARD.getCode())
        Mockito.when(driver.getRaisingPickUp()).thenReturn(false)
        when:
        CheckCode result = pickUpCardCheck.check(new CheckModel(new DspModelVO(dspOrder, driver)), new CheckContext())

        then:
        result == CheckCode.PICK_UP_CARD_LIMIT
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme