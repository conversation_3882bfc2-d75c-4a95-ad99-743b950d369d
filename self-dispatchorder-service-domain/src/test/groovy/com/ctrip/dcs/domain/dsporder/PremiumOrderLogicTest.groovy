package com.ctrip.dcs.domain.dsporder

import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.logic.PremiumOrderLogic
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class PremiumOrderLogicTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def dspOrderDetailRepository = Mock(DspOrderDetailRepository)
    def premiumOrderCarconfig = Mock(com.ctrip.dcs.domain.dsporder.carconfig.PremiumOrderCarconfig)


    def executor = new PremiumOrderLogic(
            dspOrderRepository: dspOrderRepository,
            dspOrderDetailRepository: dspOrderDetailRepository,
            premiumOrderCarconfig: premiumOrderCarconfig,

    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"

        dspOrderRepository.find(_) >> order

        premiumOrderCarconfig.matchPremiumOrderOk(_, _, _) >> flag


        when: "执行校验方法"
        executor.premiumOrderProcess("11")

        then: "验证校验结果"
        true

        where:
        order  | flag || _
        null   | _    || _
        get1() | _    || _
        get2() | _    || _
        get3() | null || _
        get3() | true || _

    }

    DspOrderDO get1() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "11", categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 10086, connectMode: 2)
        return dspOrderDO
    }

    DspOrderDO get2() {
        def dspOrderDO = get1()
        dspOrderDO.setCategoryCode("day_rental")
        return dspOrderDO
    }

    DspOrderDO get3() {
        def dspOrderDO = get1()
        dspOrderDO.setConnectMode(1)
        return dspOrderDO
    }
}
