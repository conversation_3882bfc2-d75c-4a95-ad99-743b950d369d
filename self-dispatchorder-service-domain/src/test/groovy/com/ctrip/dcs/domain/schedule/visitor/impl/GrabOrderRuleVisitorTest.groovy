package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverOrderLocationVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class GrabOrderRuleVisitorTest extends Specification {

    DriverDomainServiceGateway driverDomainServiceGateway = Mock(DriverDomainServiceGateway)

    DriverOrderLocationVO d1 = Mock(DriverOrderLocationVO)
    DriverOrderLocationVO d2 = Mock(DriverOrderLocationVO)

    GrabOrderRuleVisitor visitor = new GrabOrderRuleVisitor([d1, d2], driverDomainServiceGateway)

    def "test"() {

        given: "设定相关方法入参"
        CheckContext checkContext = Mock(CheckContext)
        checkContext.getDriverPushConfigMap() >> [1L: new DriverPushConfigVO()]
        d1.getDriverId() >> 1L
        d2.getDriverId() >> 2L
        driverDomainServiceGateway.query([2L]) >> [new DriverPushConfigVO(driverId: 2L)]

        when: "执行方法"
        visitor.visit(checkContext)

        then: "验证方法返回值"
        checkContext.getDriverPushConfigMap().containsKey(2L) == true
    }
}
