package com.ctrip.dcs.domain.schedule.process.impl

import cn.hutool.core.math.Money
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.exception.OrderStatusException
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.service.QueryVehicleService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyBoolean
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SystemAssignProcessTest extends Specification {
    @Mock
    Logger logger
    @Mock
    RecommendService recommendService
    @Mock
    CheckService checkService
    @Mock
    ConfirmDspOrderService confirmDspOrderService
    @Mock
    DriverOrderFactory driverOrderFactory
    @Mock
    QueryVehicleService queryVehicleService
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DriverVO driverVO
    @Mock
    TransportGroupVO transportGroupVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    SubSkuVO subSkuVO
    @Mock
    DriverOrderVO driverOrderVO
    @Mock
    ScheduleTaskDO scheduleTaskDO
    @Mock
    VehicleVO vehicleVO
    @Mock
    MessageProviderService messageProducer
    @Mock
    QueryDspOrderService queryDspOrderService

    @InjectMocks
    SystemAssignProcess systemAssignProcess

    def setup() {
        MockitoAnnotations.initMocks(this)
        when(transportGroupVO.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(dspModelVO.getTransportGroup()).thenReturn(transportGroupVO)
        when(driverVO.getTransportGroups()).thenReturn([transportGroupVO])
        when(subSkuVO.getSubSkuId()).thenReturn(1)
        when(subSkuVO.getTakenType()).thenReturn(TakenType.ASSISTANT)
        when(subSkuVO.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        when(scheduleTaskDO.getTaskId()).thenReturn(1L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(1L)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("1")
        when(scheduleTaskDO.getRound()).thenReturn(1)
        when(checkModel.isPass()).thenReturn(true)
        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(checkService.check(any() as TakenCheckCommand)).thenReturn(checkModel)
        when(recommendService.recommend(any(), any(), any())).thenReturn([new SortModel(dspModelVO)])
        when(queryDspOrderService.queryOrderDetail(anyString())).thenReturn(dspOrderVO)

    }

    def "test execute_xiao"() {
        given:
        when:
        def result = systemAssignProcess.execute(scheduleTaskDO, dspOrderVO)
        then:
        result == null
    }

    def "test execute_xiao1"() {
        given:
        when:
        def result = systemAssignProcess.execute(scheduleTaskDO, dspOrderVO)
        then:
        result == null
    }

    def "test execute 1"() {
        given:
        when(recommendService.recommend(any(), any(), any())).thenReturn([])
        when(scheduleTaskDO.getDspOrderId()).thenReturn("123")
        when(scheduleTaskDO.getTaskId()).thenReturn(123L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(123L)

        when:
        def result = systemAssignProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }

    def "test execute 2"() {
        given:
        when(checkModel.isPass()).thenReturn(false)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("123")
        when(scheduleTaskDO.getTaskId()).thenReturn(123L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(123L)

        when:
        def result = systemAssignProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }

    def "test execute 3"() {
        given:
        when(driverOrderFactory.create(any() as DspModelVO, any() as ScheduleTaskDO)).thenReturn(null)
        when(checkModel.isPass()).thenReturn(true)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("123")
        when(scheduleTaskDO.getTaskId()).thenReturn(123L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(123L)

        when:
        def result = systemAssignProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }

    def "test execute 4"() {
        given:
        when(queryVehicleService.query(any(),any())).thenReturn(vehicleVO)
        when(driverOrderFactory.create(any() as DspModelVO, any() as ScheduleTaskDO)).thenReturn(driverOrderVO)
        when(checkModel.isPass()).thenReturn(true)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("123")
        when(scheduleTaskDO.getTaskId()).thenReturn(123L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(123L)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(scheduleTaskDO.getReward()).thenReturn(new Money("10"))
        when(dspOrderVO.getDspOrderId()).thenReturn("123")
        when(dspOrderVO.getSpId()).thenReturn(123)
        when(dspOrderVO.getSpId()).thenReturn(123)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(dspModelVO.getTransportGroup()).thenReturn(transportGroupVO)
        when(subSkuVO.getSubSkuId()).thenReturn(1)
        when(subSkuVO.getDspType()).thenReturn(DspType.OFFLINE_ASSIGN)
        when(subSkuVO.getTakenType()).thenReturn(TakenType.ASSISTANT)
        when(driverVO.getCar()).thenReturn(new CarVO(carId: 1L))
        when(driverOrderVO.getDriverOrderId()).thenReturn("123")

        when:
        def result = systemAssignProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }

    def "test execute 5"() {
        given:
        when(driverOrderVO.getDriverId()).thenReturn(1L)
        when(driverOrderVO.getDspOrderId()).thenReturn("1")
        when(driverOrderVO.getDriverOrderId()).thenReturn("1")
        when(queryVehicleService.query(any(),any())).thenReturn(vehicleVO)
        when(driverOrderFactory.create(any() as DspModelVO, any() as ScheduleTaskDO)).thenReturn(driverOrderVO)
        when(checkModel.isPass()).thenReturn(true)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("123")
        when(scheduleTaskDO.getTaskId()).thenReturn(123L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(123L)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(scheduleTaskDO.getReward()).thenReturn(new Money("10"))
        when(dspOrderVO.getDspOrderId()).thenReturn("123")
        when(dspOrderVO.getSpId()).thenReturn(123)
        when(dspOrderVO.getSpId()).thenReturn(123)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(dspModelVO.getTransportGroup()).thenReturn(transportGroupVO)
        when(subSkuVO.getSubSkuId()).thenReturn(1)
        when(subSkuVO.getDspType()).thenReturn(DspType.OFFLINE_ASSIGN)
        when(subSkuVO.getTakenType()).thenReturn(TakenType.ASSISTANT)
        when(driverVO.getCar()).thenReturn(new CarVO(carId: 1L))
        when(driverOrderVO.getDriverOrderId()).thenReturn("123")
        when(confirmDspOrderService.confirm(any() as DriverAndCarConfirmVO)).thenThrow(new OrderStatusException(ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR))

        when:
        def result = systemAssignProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme