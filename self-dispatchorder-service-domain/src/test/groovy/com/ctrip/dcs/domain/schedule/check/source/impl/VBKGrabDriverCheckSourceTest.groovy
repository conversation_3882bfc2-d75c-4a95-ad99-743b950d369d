package com.ctrip.dcs.domain.schedule.check.source.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.dcs.domain.schedule.value.TransportGroupOrderConfig
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class VBKGrabDriverCheckSourceTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspContextService dspContextService
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driver
    @Mock
    DriverVO driver1
    @Mock
    TransportGroupVO transportGroup
    @InjectMocks
    VBKGrabDriverCheckSource vBKGrabDriverCheckSource

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test init"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([driver])

        when:
        List<CheckModel> result = vBKGrabDriverCheckSource.init(checkContext)

        then:
        result.size() == 0
    }

    def "test init empty"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([])

        when:
        List<CheckModel> result = vBKGrabDriverCheckSource.init(checkContext)

        then:
        result.size() == 0
    }

    def "test taken 1"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([])

        when:
        CheckModel result = vBKGrabDriverCheckSource.taken(checkContext)

        then:
        result.checkCode == CheckCode.NULL
    }

    def "test taken 2"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
//        when(checkContext.getDriver()).thenReturn(driver1)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([driver])
        when(driver.getDriverId()).thenReturn(0L)

        when:
        CheckModel result = vBKGrabDriverCheckSource.taken(checkContext)

        then:
        result.checkCode == CheckCode.NULL
    }

    def "test taken 3"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getDriver()).thenReturn(driver1)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([driver])
        when(driver.getDriverId()).thenReturn(0L)

        when:
        CheckModel result = vBKGrabDriverCheckSource.taken(checkContext)

        then:
        result.checkCode == CheckCode.NULL
    }

    def "test taken 4"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getDriver()).thenReturn(driver1)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([driver])
        when(driver.getDriverId()).thenReturn(0L)
        when(driver1.getDriverId()).thenReturn(1L)

        when:
        CheckModel result = vBKGrabDriverCheckSource.taken(checkContext)

        then:
        result.checkCode == CheckCode.NULL
    }

    def "test taken 5"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getDriver()).thenReturn(driver1)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([driver])
        when(driver.getDriverId()).thenReturn(0L)
        when(driver1.getDriverId()).thenReturn(0L)

        when:
        CheckModel result = vBKGrabDriverCheckSource.taken(checkContext)

        then:
        result.checkCode == CheckCode.NULL
    }

    def "test taken 6"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getDriver()).thenReturn(driver1)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([driver])
        when(driver.getDriverId()).thenReturn(0L)
        when(driver1.getDriverId()).thenReturn(0L)
        when(driver1.getTransportGroups()).thenReturn([transportGroup])

        when:
        CheckModel result = vBKGrabDriverCheckSource.taken(checkContext)

        then:
        result.checkCode == CheckCode.PASS
    }

    def "test trans Check Model"() {
        given:
        when(driver.getTransportGroups()).thenReturn([transportGroup])
        when:
        List<CheckModel> result = vBKGrabDriverCheckSource.transCheckModel([driver], dspOrder)

        then:
        result.size() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme