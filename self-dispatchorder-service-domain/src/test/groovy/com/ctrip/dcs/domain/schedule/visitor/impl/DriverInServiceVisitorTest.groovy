package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.DriverInServiceGateway
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverInServiceVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driverVO
    @Mock
    DriverInServiceGateway driverInServiceGateway

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(driverInServiceGateway.queryDriverInService(any())).thenReturn([(1l): Boolean.TRUE])
        DriverInServiceVisitor driverInServiceVisitor = new DriverInServiceVisitor([driverVO], driverInServiceGateway)
        when:
        driverInServiceVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme