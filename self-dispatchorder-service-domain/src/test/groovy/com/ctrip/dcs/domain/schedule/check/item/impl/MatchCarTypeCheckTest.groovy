package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.exception.CarTypeRelationsNotFoundException
import com.ctrip.dcs.domain.schedule.exception.CheckItemException
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class MatchCarTypeCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @InjectMocks
    MatchCarTypeCheck matchCarTypeCheck = new MatchCarTypeCheck()

    def "司机绑定车辆，车型<订单车型"() {
        given:

        Map<Long, CarTypeLevelRelation> map = Maps.newHashMap()
        map.put(121L, CarTypeLevelRelation.BELOW)
        map.put(118L, CarTypeLevelRelation.PARALLEL)
        map.put(117, CarTypeLevelRelation.HIGHER)
        CarTypeLevelRelationsVO carTypeLevelRelations = new CarTypeLevelRelationsVO(map)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(order.getCarTypeId()).thenReturn(118)
        Mockito.when(context.carTypeRelations(Mockito.any())).thenReturn(carTypeLevelRelations)


        when:
        CheckCode result = matchCarTypeCheck.check(checkModel, context)

        then:
        result == CheckCode.DRV_CAR_TYPE_NOT_MATCH
    }

    def "司机绑定车辆，车型>=订单车型"() {
        given:

        Map<Long, CarTypeLevelRelation> map = Maps.newHashMap()
        map.put(121L, CarTypeLevelRelation.BELOW)
        map.put(118L, CarTypeLevelRelation.PARALLEL)
        map.put(117, CarTypeLevelRelation.HIGHER)
        CarTypeLevelRelationsVO carTypeLevelRelations = new CarTypeLevelRelationsVO(map)
        Mockito.when(car.getCarTypeId()).thenReturn(118L)
        Mockito.when(order.getCarTypeId()).thenReturn(118)
        Mockito.when(context.carTypeRelations(Mockito.any())).thenReturn(carTypeLevelRelations)


        when:
        CheckCode result = matchCarTypeCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机没有绑定车辆"() {
        given:

        Map<Long, CarTypeLevelRelation> map = Maps.newHashMap()
        map.put(121L, CarTypeLevelRelation.BELOW)
        map.put(118L, CarTypeLevelRelation.PARALLEL)
        map.put(117, CarTypeLevelRelation.HIGHER)
        CarTypeLevelRelationsVO carTypeLevelRelations = new CarTypeLevelRelationsVO(map)
        Mockito.when(driverInfo.getCar()).thenReturn(null)
        Mockito.when(order.getCarTypeId()).thenReturn(118)
        Mockito.when(context.carTypeRelations(Mockito.any())).thenReturn(carTypeLevelRelations)


        when:
        CheckCode result = matchCarTypeCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }


    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    @Unroll
    def "test check0"() {
        given:
        CheckContext context = Mock()
        context.carTypeRelations(_) >> carTypeRelations

        when:
        matchCarTypeCheck.check(checkModel, context)

        then:
        thrown(e)

        where:
        checkModel                                                                                                                       | carTypeRelations || e
        new CheckModel(model: new DspModelVO(order: new DspOrderVO(carTypeId: 1), driver: new DriverVO(car: new CarVO(carTypeId: 117)))) | null             || CarTypeRelationsNotFoundException

    }

    @Unroll
    def "test check"() {
        given:
        CheckModel checkModel = new CheckModel(model: new DspModelVO(order: new DspOrderVO(carTypeId: 1), driver: new DriverVO(car: new CarVO(carTypeId: 1L))))
        CheckContext context = Mock()
        def carTypeRelations = Mock(CarTypeLevelRelationsVO)
        context.carTypeRelations(_) >> carTypeRelations
        carTypeRelations.otherCarTypeRelation(_) >> otherCarTypeRelation

        when:
        CheckCode result = matchCarTypeCheck.check(checkModel, context)

        then:
        result == r

        where:
        otherCarTypeRelation          || r
        CarTypeLevelRelation.BELOW    || CheckCode.PASS
        CarTypeLevelRelation.PARALLEL || CheckCode.PASS
        CarTypeLevelRelation.HIGHER   || CheckCode.DRV_CAR_TYPE_NOT_MATCH
    }

    def "test downgrade"() {
        when:
        CheckCode result = matchCarTypeCheck.downgrade()

        then:
        result == CheckCode.DRV_CAR_TYPE_NOT_MATCH
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme