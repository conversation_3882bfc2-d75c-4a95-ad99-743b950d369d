package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverOrderLocationVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class GrabOrderDriverCheckTest extends Specification {
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    Logger logger
    @Mock
    DriverPushConfigVO configVO
    @Mock
    Map<String, BigDecimal> driverToOrderDistanceMap
    @Mock
    Map<Long, DriverPushConfigVO> configVOMap
    @InjectMocks
    GrabOrderDriverCheck grabOrderDriverCheck;

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
        Mockito.when(order.getUserOrderId()).thenReturn("1")
    }
    def "transportGroup_BroadcastFalse"(){
        given:
        Mockito.when(configVO.getOrderPushStatus()).thenReturn(true)
        Mockito.when(context.getDriverPushConfigMap()).thenReturn(configVOMap)
        Mockito.when(configVOMap.get(Mockito.any())).thenReturn(configVO)
        Mockito.when(driverInfo.getBroadcast()).thenReturn(YesOrNo.NO)
        when:
        CheckCode result = grabOrderDriverCheck.check(checkModel, context);
        then:
        result == CheckCode.GRAB_ORDER_CHECK_TRANSPORT_GROUP_FALSE
    }
    def "configVO_null"(){
        given:
        Mockito.when(context.getDriverPushConfigMap()).thenReturn(new HashMap<Long, DriverPushConfigVO>())
        Mockito.when(driverInfo.getBroadcast()).thenReturn(YesOrNo.YES)
        when:
        CheckCode result = grabOrderDriverCheck.check(checkModel, context);
        then:
        result == CheckCode.GRAB_ORDER_CHECK_PUSH_STATUS_FALSE
    }
    def "distance_check_false"(){
        given:
        Mockito.when(configVO.getOrderPushStatus()).thenReturn(true)
        Mockito.when(context.getDriverPushConfigMap()).thenReturn(configVOMap)
        Mockito.when(configVOMap.get(Mockito.any())).thenReturn(configVO)
        Mockito.when(configVO.getDrvOrderDistance()).thenReturn(1)
        Mockito.when(context.getDriverToOrderDistanceMap()).thenReturn(driverToOrderDistanceMap)
        Mockito.when(driverToOrderDistanceMap.get(Mockito.any())).thenReturn(new BigDecimal(2))
        Mockito.when(driverInfo.getBroadcast()).thenReturn(YesOrNo.YES)
        when:
        CheckCode result = grabOrderDriverCheck.check(checkModel, context);
        then:
        result == CheckCode.GRAB_ORDER_CHECK_DISTANCE_FALSE
    }
    def "OrderTypes_check_false"(){
        given:
        Mockito.when(configVO.getOrderPushStatus()).thenReturn(true)
        Mockito.when(context.getDriverPushConfigMap()).thenReturn(configVOMap)
        Mockito.when(configVOMap.get(Mockito.any())).thenReturn(configVO)
        Mockito.when(configVO.getDrvOrderDistance()).thenReturn(3)
        Mockito.when(context.getDriverToOrderDistanceMap()).thenReturn(driverToOrderDistanceMap)
        Mockito.when(driverToOrderDistanceMap.get(Mockito.any())).thenReturn(new BigDecimal(2))
        Mockito.when(driverInfo.getBroadcast()).thenReturn(YesOrNo.YES)
        Mockito.when(configVO.getOrderTypes()).thenReturn(Arrays.asList(2,3))
        Mockito.when(order.getCategoryCode()).thenReturn(CategoryCodeEnum.TO_AIRPORT)
        when:
        CheckCode result = grabOrderDriverCheck.check(checkModel, context);
        then:
        result == CheckCode.GRAB_ORDER_CHECK_ORDER_TYPE_FALSE
    }
    def "BookTime_check_false"(){
        given:
        Mockito.when(configVO.getOrderPushStatus()).thenReturn(true)
        Mockito.when(context.getDriverPushConfigMap()).thenReturn(configVOMap)
        Mockito.when(configVOMap.get(Mockito.any())).thenReturn(configVO)
        Mockito.when(configVO.getDrvOrderDistance()).thenReturn(3)
        Mockito.when(context.getDriverToOrderDistanceMap()).thenReturn(driverToOrderDistanceMap)
        Mockito.when(driverToOrderDistanceMap.get(Mockito.any())).thenReturn(new BigDecimal(2))
        Mockito.when(driverInfo.getBroadcast()).thenReturn(YesOrNo.YES)
        Mockito.when(configVO.getOrderTypes()).thenReturn(Arrays.asList(2,1))
        Mockito.when(order.getCategoryCode()).thenReturn(CategoryCodeEnum.TO_AIRPORT)
        Mockito.when(configVO.getServiceTimeFrom()).thenReturn("11:29")
        Mockito.when(configVO.getServiceTimeTo()).thenReturn("12:29")
        Mockito.when(order.getEstimatedUseTimeBj()).thenReturn(DateUtil.parse("2023-09-27 11:10:00","yyyy-MM-dd HH:mm:ss"))
        when:
        CheckCode result = grabOrderDriverCheck.check(checkModel, context);
        then:
        result == CheckCode.GRAB_ORDER_CHECK_BOOK_TIME_FALSE
    }
    def "CarType_check_false"(){
        given:
        Mockito.when(configVO.getOrderPushStatus()).thenReturn(true)
        Mockito.when(context.getDriverPushConfigMap()).thenReturn(configVOMap)
        Mockito.when(configVOMap.get(Mockito.any())).thenReturn(configVO)
        Mockito.when(configVO.getDrvOrderDistance()).thenReturn(3)
        Mockito.when(context.getDriverToOrderDistanceMap()).thenReturn(driverToOrderDistanceMap)
        Mockito.when(driverToOrderDistanceMap.get(Mockito.any())).thenReturn(new BigDecimal(2))
        Mockito.when(driverInfo.getBroadcast()).thenReturn(YesOrNo.YES)
        Mockito.when(configVO.getOrderTypes()).thenReturn(Arrays.asList(2,1))
        Mockito.when(order.getCategoryCode()).thenReturn(CategoryCodeEnum.TO_AIRPORT)
        Mockito.when(configVO.getServiceTimeFrom()).thenReturn("11:29")
        Mockito.when(configVO.getServiceTimeTo()).thenReturn("12:29")
        Mockito.when(order.getEstimatedUseTimeBj()).thenReturn(DateUtil.parse("2023-09-27 11:40:00","yyyy-MM-dd HH:mm:ss"))
        Mockito.when(configVO.getDrvIntendVehicleTypes()).thenReturn(Arrays.asList(117))
        Mockito.when(order.getCarTypeId()).thenReturn(118)
        when:
        CheckCode result = grabOrderDriverCheck.check(checkModel, context);
        then:
        result == CheckCode.GRAB_ORDER_CHECK_CAR_TYPE_FALSE
    }
    def "load"(){
        given:
        Mockito.when(configVO.getOrderPushStatus()).thenReturn(true)
        Mockito.when(context.getDriverPushConfigMap()).thenReturn(configVOMap)
        Mockito.when(configVOMap.get(Mockito.any())).thenReturn(configVO)
        Mockito.when(configVO.getDrvOrderDistance()).thenReturn(3)
        Mockito.when(context.getDriverToOrderDistanceMap()).thenReturn(driverToOrderDistanceMap)
        Mockito.when(driverToOrderDistanceMap.get(Mockito.any())).thenReturn(new BigDecimal(2))
        Mockito.when(driverInfo.getBroadcast()).thenReturn(YesOrNo.YES)
        Mockito.when(configVO.getOrderTypes()).thenReturn(Arrays.asList(2,1))
        Mockito.when(order.getCategoryCode()).thenReturn(CategoryCodeEnum.TO_AIRPORT)
        Mockito.when(configVO.getServiceTimeFrom()).thenReturn("11:29")
        Mockito.when(configVO.getServiceTimeTo()).thenReturn("12:29")
        Mockito.when(order.getEstimatedUseTimeBj()).thenReturn(DateUtil.parse("2023-09-27 11:40:00","yyyy-MM-dd HH:mm:ss"))
        Mockito.when(configVO.getDrvIntendVehicleTypes()).thenReturn(Arrays.asList(118))
        Mockito.when(order.getCarTypeId()).thenReturn(118)
        when:
        List<DriverOrderLocationVO> locationVOList = grabOrderDriverCheck.getDriverIds(Arrays.asList(checkModel))
        CheckCode result = grabOrderDriverCheck.check(checkModel, context);
        then:
        locationVOList.size() == 1
        result == CheckCode.PASS
    }
}
