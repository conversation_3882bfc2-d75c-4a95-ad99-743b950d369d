package com.ctrip.dcs.domain.dsporder.handler.impl

import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.IdGeneratorService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.BatchConfirmDspOrderVO
import com.ctrip.dcs.domain.common.value.BatchConfirmDspOrderVO.ConfirmDspOrderDetailVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.ServiceProviderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.entity.BatchConfirmOrderDO
import com.ctrip.dcs.domain.dsporder.entity.BatchConfirmOrderDetailDO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.factory.BatchOrderConfirmFactory
import com.ctrip.dcs.domain.dsporder.factory.DspOrderConfirmRecordFactory
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusContext
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.GroupOrderConfirmRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO.DriverRecord
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import spock.lang.Specification

import java.util.concurrent.ExecutorService

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class BatchDriverAndCarConfirmOrderStatusHandlerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DistributedLockService distributedLockService
    @Mock
    DspOrderRepository dspOrderRepository
    @Mock
    DspOrderConfirmRecordFactory confirmRecordFactory
    @Mock
    DspOrderConfirmRecordRepository confirmRecordRepository
    @Mock
    PurchaseSupplyOrderGateway purchaseSupplyOrderGateway
    @Mock
    IdGeneratorService idGeneratorService
    @Mock
    GroupOrderConfirmRepository groupOrderConfirmRepository
    @Mock
    MessageProviderService messageProducer
    @Mock
    BatchOrderConfirmFactory batchOrderConfirmFactory
    @Mock
    OrderStatusContext orderStatusContext
    @Mock
    DistributedLockService.DistributedLock lock
    @Mock
    DspOrderDO dspOrderDO
    @Mock
    DspOrderConfirmRecordVO dspOrderConfirmRecordVO
    @Mock
    DriverRecord driverRecord
    @Mock
    TransportGroupVO transportGroupVO
    @Mock
    DriverVO driverVO
    @Mock
    BatchConfirmDspOrderVO batchConfirmDspOrderVO
    @Mock
    ConfirmDspOrderDetailVO confirmDspOrderDetailVO
    @Mock
    BatchConfirmOrderDO groupOrder
    @Mock
    BatchConfirmOrderDetailDO batchConfirmOrderDetailDO
    @Mock
    ExecutorService syncOrderThreadPool;
    @Mock
    ConfigService commonConfConfig;
    @InjectMocks
    BatchDriverAndCarConfirmOrderStatusHandler batchDriverAndCarConfirmOrderStatusHandler

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test handle"() {
        given:
        ServiceProviderVO serviceProviderVO = new ServiceProviderVO(1)
        when(dspOrderDO.getOrderStatus()).thenReturn(OrderStatusEnum.TO_BE_CONFIRMED.getCode())
        when(lock.tryLock()).thenReturn(true)
        when(orderStatusContext.getDspOrderId()).thenReturn("111")
        when(orderStatusContext.getFrom()).thenReturn(OrderStatusEnum.TO_BE_CONFIRMED)
        when(orderStatusContext.getTo()).thenReturn(OrderStatusEnum.DRIVER_CAR_CONFIRMED)
        when(orderStatusContext.getEvent()).thenReturn(OrderStatusEvent.SYSTEM_ASSIGN)
        when(orderStatusContext.getDspOrder()).thenReturn(dspOrderDO)
        when(orderStatusContext.getServiceProvider()).thenReturn(serviceProviderVO)
        when(orderStatusContext.getTransportGroup()).thenReturn(transportGroupVO)
        when(orderStatusContext.getDriver()).thenReturn(driverVO)
        when(orderStatusContext.getBatchDspOrder()).thenReturn(batchConfirmDspOrderVO)
        when(dspOrderConfirmRecordVO.getDriverInfo()).thenReturn(driverRecord)
        when(batchConfirmDspOrderVO.getDetails()).thenReturn([confirmDspOrderDetailVO])
        when(groupOrder.getDetails()).thenReturn([batchConfirmOrderDetailDO])
        when(batchConfirmOrderDetailDO.getDspOrder()).thenReturn(dspOrderDO)
        when(batchConfirmOrderDetailDO.getConfirmRecord()).thenReturn(dspOrderConfirmRecordVO)
        when(dspOrderRepository.find(anyString())).thenReturn(dspOrderDO)
        when(distributedLockService.getLock(anyList())).thenReturn(lock)
        when(confirmRecordFactory.createDriverCarConfirmRecord(orderStatusContext)).thenReturn(dspOrderConfirmRecordVO)
        when(batchOrderConfirmFactory.create(any())).thenReturn(groupOrder)

        when:
        batchDriverAndCarConfirmOrderStatusHandler.handle(orderStatusContext)

        then:
        serviceProviderVO.getServiceProviderId() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme