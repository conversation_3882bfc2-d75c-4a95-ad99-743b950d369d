package com.ctrip.dcs.domain.schedule.sort.feature.impl

import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.carconfig.NearPickUpTimeOrderConfigValueVO
import spock.lang.Specification
import spock.lang.Unroll

class NearOrderFeatureTest extends Specification {
    def testObj = new NearOrderFeature()

    @Unroll
    def "valueTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        def model = Mock(SortModel)
        def context = Mock(SortContext)

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.isNearOrder(_, _) >> isNearOrder
        spy.queryEmptyDrivingDuration(_, _) >> 123
        when:
        def result = spy.value(model, context)

        then: "验证返回结果里属性值是否符合预期"
        result.getValue() == expectedResult
        where: "表格方式验证多种分支调用场景"
        isNearOrder || expectedResult
        false       || 0
        true        || 123
    }

    @Unroll
    def "normalizeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        def model = Mock(SortModel)
        def context = Mock(SortContext)
        context.getDspOrder() >> new DspOrderVO(dspOrderId: "12345", cityId: 1)
        context.getCityProperties(_, _, _) >> 52
        and: "Spy相关接口"
        def spy = Spy(testObj)
        when:
        def result = spy.normalize(context, [new Value("F39", 52)])

        then: "验证返回结果里属性值是否符合预期"
        context.getDspOrder().getCityId() == 1
    }

    @Unroll
    def "should return true when order is near based on config value"() {
        given:
        def model = Mock(SortModel)
        def context = Mock(SortContext)
        def order = Mock(DspOrderVO)
        def nearTime = Mock(NearPickUpTimeOrderConfigValueVO)

        model.getModel() >> Mock(DspModelVO) {
            getOrder() >> order
        }
        context.getDspContext() >> Mock(DspContext) {
            getService() >> Mock(DspContextService) {
                queryNearPickUpTimeOrderConfig(order) >> nearTime
            }
        }
        nearTime.getNearPickUpTimeMinute() >> nearTimeMin
        order.getEstimatedUseTimeBj() >> useTime  // 20 minutes from now

        when:
        def result = testObj.isNearOrder(model, context)

        then:
        result == expectedResult

        where:
        nearTimeMin | useTime                                               || expectedResult
        30          | new Date(System.currentTimeMillis() + 20 * 60 * 1000) || true
//        30          | new Date(System.currentTimeMillis() + 40 * 60 * 1000) || false
    }
}
