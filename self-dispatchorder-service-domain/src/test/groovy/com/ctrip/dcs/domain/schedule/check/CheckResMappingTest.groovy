package com.ctrip.dcs.domain.schedule.check

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.OrderSettlePriceVO
import spock.lang.*

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class CheckResMappingTest extends Specification {

    def "test DrivInventoryStatus mapping"() {

        when:
        CheckResMapping.DrivInventoryStatus result = CheckResMapping.DrivInventoryStatus.mapping(code)

        then:
        result == status

        where:
        code || status
        "0"  || CheckResMapping.DrivInventoryStatus.NORMAL
        "1"  || CheckResMapping.DrivInventoryStatus.NORMAL
        "2"  || CheckResMapping.DrivInventoryStatus.BOOK_TIME_CONFLICT_STATUS
        "3"  || CheckResMapping.DrivInventoryStatus.DRIVE_QUERTZ_CONFLIT
        "4"  || CheckResMapping.DrivInventoryStatus.LOC_QUERTZ_CONFLIT
        "5"  || CheckResMapping.DrivInventoryStatus.FLIGHT_CONFLICT
        "6"  || CheckResMapping.DrivInventoryStatus.LOCATION_FAIL
        "7"  || CheckResMapping.DrivInventoryStatus.LBS_FAIL
        "8"  || null
        "a"  || null
        null || null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme