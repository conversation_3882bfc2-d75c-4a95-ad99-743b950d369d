package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.exception.CheckItemValidateException
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class TransGroupOrderRatePlanIdCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    TransportGroupVO transportGroup
    @InjectMocks
    TransGroupOrderRatePlanIdLimitCheck transGroupOrderNumLimitCheck = new TransGroupOrderRatePlanIdLimitCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
        Mockito.when(order.getRatePlanId()).thenReturn(1)
    }

    def "司机运力组时段内接单数>=阈值 1"() {
        given:
        Mockito.when(context.transGroupRemainingInventory(Mockito.any())).thenReturn(0)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getTransportGroupId()).thenReturn(1L)
        Mockito.when(order.getRatePlanId()).thenReturn(null)
        when:
        CheckCode result = transGroupOrderNumLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机运力组时段内接单数>=阈值"() {
        given:
        Mockito.when(context.transGroupRemainingInventory(Mockito.any())).thenReturn(0)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getTransportGroupId()).thenReturn(1L)

        when:
        CheckCode result = transGroupOrderNumLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.OVER_TRANS_GROUP_ORDERS_RATE_PLAN_ID_LIMIT
    }

    def "司机运力组时段内接单数<阈值"() {
        given:
        Mockito.when(context.transGroupRemainingInventory(Mockito.any())).thenReturn(1)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getTransportGroupId()).thenReturn(1L)

        when:
        CheckCode result = transGroupOrderNumLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check"() {
        given:
        def context = Mock(CheckContext)
        context.transGroupRemainingInventory(_) >> transGroupRemainingInventory
        def model = new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(transportGroupId: 1L),order: new DspOrderVO(ratePlanId: 1)))
        when:
        CheckCode result = transGroupOrderNumLimitCheck.check(model, context)

        then:
        result == r

        where:
        transGroupRemainingInventory || r
        0                            || CheckCode.OVER_TRANS_GROUP_ORDERS_RATE_PLAN_ID_LIMIT
        1                            || CheckCode.PASS
    }

    def "test downgrade"() {
        when:
        CheckCode result = transGroupOrderNumLimitCheck.downgrade()

        then:
        result == CheckCode.OVER_TRANS_GROUP_ORDERS_RATE_PLAN_ID_LIMIT
    }

    @Unroll
    def "test validate"() {
        when:
        transGroupOrderNumLimitCheck.validate(checkModel, Mock(CheckContext))

        then:
        thrown(e)

        where:
        checkModel                                                                                                                                                                                    || e
        new CheckModel()                                                                                                                                                                              || CheckItemValidateException
        new CheckModel(model: new DspModelVO())                                                                                                                                                       || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO()))                                                                                                                 || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO())))                                                             || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(), transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)))    || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(), transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST))) || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(), transportGroupMode: TransportGroupMode.REGISTER_DISPATCH)))   || CheckItemValidateException
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme