package com.ctrip.dcs.domain.dsporder

import com.ctrip.dcs.domain.dsporder.entity.*
import com.ctrip.dcs.domain.dsporder.gateway.RhbSettlementRuleGateway
import com.ctrip.dcs.domain.dsporder.logic.PlatformPriceStrategyLogic
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.repository.PlatformPriceStrategyRepository
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class PlatformPriceStrategyLogicTest extends Specification {

    def dspOrderRepository = Mock(DspOrderRepository)
    def platformPriceStrategyRepository = Mock(PlatformPriceStrategyRepository)
    def rhbSettlementRuleGateway = Mock(RhbSettlementRuleGateway)
    def dspOrderFeeRepository = Mock(DspOrderFeeRepository)
    def dspOrderDetailRepository = Mock(DspOrderDetailRepository)


    def executor = new PlatformPriceStrategyLogic(
            dspOrderRepository: dspOrderRepository,
            platformPriceStrategyRepository: platformPriceStrategyRepository,
            rhbSettlementRuleGateway: rhbSettlementRuleGateway,
            dspOrderFeeRepository: dspOrderFeeRepository,
            dspOrderDetailRepository: dspOrderDetailRepository

    )

    @Unroll
    def "test execute"() {

        given: "Mock数据"
        dspOrderRepository.find(_) >> null
        dspOrderFeeRepository.find(_) >> null
        dspOrderDetailRepository.find(_) >> null


        when: "执行校验方法"
        executor.savePriceStrategy("11")

        then: "验证校验结果"
        def ex = thrown(BizException)
        ex.code == "09015101"


    }

    @Unroll
    def "test execute1"() {

        given: "Mock数据"

        dspOrderRepository.find(_) >> order
        dspOrderFeeRepository.find(_) >> orderFee
        dspOrderDetailRepository.find(_) >> orderDetail
        platformPriceStrategyRepository.find(_) >> strategy1
        rhbSettlementRuleGateway.calculateSettleAmount(_) >> settle

        when: "执行校验方法"
        executor.savePriceStrategy("11")

        then: "验证校验结果"
        true

        where:
        order  | orderFee | orderDetail | strategy1     | settle    || _
        get1() | getFee() | getDetail() | getStrategy() | _         || _
        get1() | getFee() | getDetail() | null          | _         || _
        get1() | getFee() | getDetail() | null          | getBill() || _


    }

    StrategyBillRspDTO getBill() {
        def bill = new StrategyBillRspDTO()
        return bill
    }

    DspOrderDetailDO getDetail() {
        def detail = new DspOrderDetailDO()
        return detail
    }

    DspOrderFeeDO getFee() {
        def fee = new DspOrderFeeDO()
        return fee
    }

    PlatformPriceStrategyDO getStrategy() {
        def strategyDO = new PlatformPriceStrategyDO(source: 0, expectSettlementResult: "1", actualSettlementResult: "1")

        return strategyDO
    }

    DspOrderDO get1() {
        def useDays = new UseDays(BigDecimal.valueOf(1))
        def dspOrderDO = new DspOrderDO(userOrderId: "11", categoryCode: "airport_dropoff", cityId: 2, fromCityId: 2, toCityId: 2, vehicleGroupId: 117
                , estimatedUseTime: new Timestamp(System.currentTimeMillis()), estimatedUseTimeBj: new Timestamp(System.currentTimeMillis())
                , predicServiceStopTime: new Timestamp(System.currentTimeMillis()), predicServiceStopTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTime: new Timestamp(System.currentTimeMillis()), lastConfirmTimeBj: new Timestamp(System.currentTimeMillis())
                , lastConfirmCarTimeBj: new Timestamp(System.currentTimeMillis()), lastConfirmTime: new Timestamp(System.currentTimeMillis())
                , useDays: useDays, estimatedKm: BigDecimal.valueOf(100), estimatedMin: BigDecimal.valueOf(200), supplierId: 10086, connectMode: 2
                , spId: 100000)
        return dspOrderDO
    }

    DspOrderDO get2() {
        def dspOrderDO = get1()
        dspOrderDO.setCategoryCode("day_rental")
        return dspOrderDO
    }

    DspOrderDO get3() {
        def dspOrderDO = get1()
        dspOrderDO.setConnectMode(1)
        return dspOrderDO
    }
}
