package com.ctrip.dcs.domain.schedule.value

import cn.hutool.core.math.Money
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class RewardVOTest extends Specification {
    @Mock
    Logger logger
    @Mock
    Set<DspType> DSP_TYPE
    @Mock
    Set<DspType> VBK_DSP_TYPE
    @Mock
    Money ZERO
    @Mock
    Money reward
    @Mock
    Money vbkReward
    @Mock
    Date vbkRewardTimeBj
    @Mock
    DspOrderVO dspOrder
    @Mock
    ScheduleTaskDO scheduleTask
    @Mock
    SubSkuVO subSku
    @InjectMocks
    RewardVO rewardVO

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test reward"() {
        given:
        RewardVO reward = new RewardVO(BigDecimal.TEN,"", [])
        when(dspOrder.getCategoryCode()).thenReturn(categoryCode)
        when(scheduleTask.getSubSku()).thenReturn(subSku)
        when(subSku.getDspType()).thenReturn(dspType)
        when:
        Money result = reward.reward(dspOrder, scheduleTask)

        then:
        result.getAmount() == money

        where:
        categoryCode                  | dspType                 || money
        CategoryCodeEnum.DAY_RENTAL   | DspType.SYSTEM_ASSIGN    | BigDecimal.ZERO
        CategoryCodeEnum.FROM_AIRPORT | DspType.SYSTEM_ASSIGN    | BigDecimal.ZERO
        CategoryCodeEnum.FROM_AIRPORT | DspType.SYSTEM_BROADCAST | BigDecimal.TEN
    }

    def "test vbk Reward"() {
        given:
        RewardVO reward = new RewardVO(BigDecimal.TEN, BigDecimal.ONE, DateUtil.addMinutes(new Date(), minutes),"", [])
        when(scheduleTask.getSubSku()).thenReturn(subSku)
        when(subSku.getDspType()).thenReturn(dspType)

        when:
        Money result = reward.vbkReward(scheduleTask)

        then:
        result.getAmount() == money

        where:
        minutes | dspType               || money
        10      | DspType.SYSTEM_ASSIGN || BigDecimal.ZERO
        10      | DspType.VBK_BROADCAST || BigDecimal.ZERO
        -10     | DspType.VBK_BROADCAST || BigDecimal.ONE
    }


    def "test vbk Reward 111"() {
        given:

        RewardVO reward = new RewardVO(BigDecimal.TEN, BigDecimal.ONE, DateUtil.addMinutes(new Date(), 1), "", [])

        when:
        boolean result = reward.checkBroadcastAddFeeOffline(1)

        then:
        !result

    }

    def "test vbk Reward 112"() {
        given:

        RewardVO reward = new RewardVO(BigDecimal.TEN, BigDecimal.ONE, DateUtil.addMinutes(new Date(), 1), "ON", [])

        when:
        boolean result = reward.checkBroadcastAddFeeOffline(1)

        then:
        result == true

    }


    def "test vbk Reward 113"() {
        given:

        RewardVO reward = new RewardVO(BigDecimal.TEN, BigDecimal.ONE, DateUtil.addMinutes(new Date(), 1), "1", [])

        when:
        boolean result = reward.checkBroadcastAddFeeOffline(1)

        then:
        result == true

    }

    def "test vbk Reward 114"() {
        given:

        RewardVO reward = new RewardVO(BigDecimal.TEN, BigDecimal.ONE, DateUtil.addMinutes(new Date(), 1), "2", [])

        when:
        boolean result = reward.checkBroadcastAddFeeOffline(1)

        then:
        !result

    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme