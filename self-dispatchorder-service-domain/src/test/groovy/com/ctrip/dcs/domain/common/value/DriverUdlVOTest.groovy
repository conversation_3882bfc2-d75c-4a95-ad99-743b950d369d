package com.ctrip.dcs.domain.common.value

import org.junit.Assert
import spock.lang.Specification

/**
 * <AUTHOR>
 * @since 2025/4/30 17:05
 */
class DriverUdlVOTest extends Specification {
    def testObj = new DriverUdlVO()

    def "test"() {
        given:
        when:
        DriverUdlVO driverUdlVO = new DriverUdlVO();
        driverUdlVO.setDriverid("1111")
        driverUdlVO.setUdl("udl")
        driverUdlVO.setUid("uid")
        driverUdlVO.setOversea(Boolean.TRUE)
        driverUdlVO.setRequestFrom("requestFrom")
        then:
        Assert.assertTrue(Objects.equals(driverUdlVO.getDriverid(),"1111"))
        Assert.assertTrue(Objects.equals(driverUdlVO.getUdl(),"udl"))
        Assert.assertTrue(Objects.equals(driverUdlVO.getUid(),"uid"))
        Assert.assertTrue(Objects.equals(driverUdlVO.getOversea(),Boolean.TRUE))
        Assert.assertTrue(Objects.equals(driverUdlVO.getRequestFrom(),"requestFrom"))
    }
}
