package com.ctrip.dcs.domain.schedule.check

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import spock.lang.Specification

class CheckContextTest extends Specification {

    def "queryLogTag"(){
        given:
        def  context = Mock(DspContext)
        def configService = Mock(ConfigService)
        def subSku = Mock(SubSkuVO)
        def dspOrder = Mock(DspOrderVO)
        def driver = Mock(DriverVO)
        def transportGroup = Mock(TransportGroupVO)
        def duid = Mock(DuidVO)
        def car = Mock(CarVO)
        def checkContext = new CheckContext(context, configService,subSku,dspOrder,driver,transportGroup,DspStage.TAKEN, Arrays.asList(driver), duid,car)
        when:
        subSku.getDspType() >> DspType.DISPATCHER_ASSIGN
        subSku.getSubSkuId() >> 4656
        dspOrder.getCityId() >> 466
        def result = checkContext.queryLogTag()
        then:
        result.size() > 0
    }
}
