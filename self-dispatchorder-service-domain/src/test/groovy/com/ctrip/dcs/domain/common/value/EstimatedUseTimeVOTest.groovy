package com.ctrip.dcs.domain.common.value


import spock.lang.Specification
import spock.lang.Unroll

class EstimatedUseTimeVOTest extends Specification {

    @Unroll
    def "test in"() {
        given:
        def estimatedUseTimeVO = new EstimatedUseTimeVO(estimatedUseTime)

        when:
        boolean result = estimatedUseTimeVO.in(new PeriodsVO(["10:00~20:00"]))

        then:
        result == r

        where:
        estimatedUseTime                                                 || r
        new GregorianCalendar(2023, Calendar.APRIL, 1, 13, 53).getTime() || true
        null                                                             || false
        new GregorianCalendar(2023, Calendar.APRIL, 1, 20, 00).getTime() || false
        new GregorianCalendar(2023, Calendar.APRIL, 1, 10, 00).getTime() || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme