package com.ctrip.dcs.domain.common.value

import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.sort.SortRecord
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.assertj.core.util.Lists
import org.mockito.Mockito
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class SortRecordTest extends Specification {
//    "f9": {
//        "v": "7450",//局部时间间隔，单位 秒
//
//        "f": "7000",//前向时间间隔，单位 秒
//
//        "a": "3000",//后向时间间隔，单位 秒
//
//        "default": "0",//默认时间间隔，单位 秒
//    },

    def "test"() {
        given:
        SortModel model = Mock()
        DspModelVO modelVO = Mock()
        SortContext context = Mock()
        List<Value> values = Lists.newArrayList()
        Map<String, Object> valueDetails = new HashMap<>()
        valueDetails.put("f", 100)
        valueDetails.put("a", 300)
        Value v1 = new Value("F19", 2.0, valueDetails)
        values.add(v1)
        Map<String, Object> valueDetails1 = new HashMap<>()
        valueDetails1.put("default", 0)
        Value v2 = new Value("F9", 2.0, valueDetails1)
        values.add(v2)

//        Mockito.when(model.getValues()).thenReturn(values)

        SortModel model1 = new SortModel (modelVO)
        model1.addValue(v1)

        when:
        SortRecord sortRecord = new SortRecord(model1, context);


        then:
        sortRecord.toMap() != null


    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme