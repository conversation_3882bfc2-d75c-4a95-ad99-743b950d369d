package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyList
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverOrderListVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO order
    @Mock
    DriverVO driver
    @Mock
    DriverWorkTimeVO driverWorkTimeVO
    @Mock
    QueryDspOrderService queryDspOrderService

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 57).getTime())
        when(queryDspOrderService.queryOrderList(anyList() as List<DriverVO>, any() as Date, any() as Date, anyList() as List<OrderStatusEnum>)).thenReturn([(1l): [order]])
        DriverOrderListVisitor driverOrderListVisitor = new DriverOrderListVisitor(order, [driver], queryDspOrderService, null)
        when:
        def result = driverOrderListVisitor.visit(checkContext)

        then:
        result == null
    }

    def "test visit 2"() {
        given:
        when(driver.findHitWorkTime(order)).thenReturn(driverWorkTimeVO)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 57).getTime())
        when(queryDspOrderService.queryOrderList(anyList() as List<DriverVO>, any() as Date, any() as Date, anyList() as List<OrderStatusEnum>)).thenReturn([(1l): [order]])
        DriverOrderListVisitor driverOrderListVisitor = new DriverOrderListVisitor(order, [driver], queryDspOrderService, "")
        when:
        def result = driverOrderListVisitor.visit(checkContext)

        then:
        result == null
    }

    def "test query Order List"() {
        given:
        when(driverWorkTimeVO.getStart()).thenReturn(new Date())
        when(driverWorkTimeVO.getEnd()).thenReturn(new Date())
        when(driver.findHitWorkTime(order)).thenReturn(driverWorkTimeVO)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 57).getTime())
        when(queryDspOrderService.queryOrderList(anyList() as List<DriverVO>, any() as Date, any() as Date, anyList() as List<OrderStatusEnum>)).thenReturn([(1l): [order]])
        DriverOrderListVisitor driverOrderListVisitor = new DriverOrderListVisitor(order, [driver], queryDspOrderService, "")
        when:
        def result = driverOrderListVisitor.visit(checkContext)

        then:
        result == null
    }


    def "test query Order List12"() {
        given:
        when(driverWorkTimeVO.getStart()).thenReturn(new Date())
        when(driverWorkTimeVO.getEnd()).thenReturn(new Date())
        when(driver.findHitWorkTime(order)).thenReturn(driverWorkTimeVO)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 57).getTime())
        when(order.getCityId()).thenReturn(1)
        when(queryDspOrderService.queryOrderList(anyList() as List<DriverVO>, any() as Date, any() as Date, anyList() as List<OrderStatusEnum>)).thenReturn([(1l): [order]])
        DriverOrderListVisitor driverOrderListVisitor = new DriverOrderListVisitor(order, [driver], queryDspOrderService, "1,2")
        when:
        def result = driverOrderListVisitor.visit(checkContext)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme