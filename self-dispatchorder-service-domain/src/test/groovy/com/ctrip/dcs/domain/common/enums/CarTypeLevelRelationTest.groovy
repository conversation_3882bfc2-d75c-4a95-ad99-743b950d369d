package com.ctrip.dcs.domain.common.enums


import spock.lang.Specification
import spock.lang.Unroll

class CarTypeLevelRelationTest extends Specification {

    @Unroll
    def "test get Instance"() {
        when:
        CarTypeLevelRelation result = CarTypeLevelRelation.getInstance(code)

        then:
        result == r

        where:
        code || r
        0    || CarTypeLevelRelation.PARALLEL
        2    || CarTypeLevelRelation.UNKOWN
    }

    def "test other Not Lower Current 1"() {
        when:
        boolean result = CarTypeLevelRelation.PARALLEL.otherNotLowerCurrent()

        then:
        result
    }

    def "test other Not Lower Current 2"() {
        when:
        boolean result = CarTypeLevelRelation.BELOW.otherNotLowerCurrent()

        then:
        result
    }

    def "test other Not Lower Current 3"() {
        when:
        boolean result = CarTypeLevelRelation.HIGHER.otherNotLowerCurrent()

        then:
        !result
    }

    def "test equal Level1"() {
        when:
        boolean result = CarTypeLevelRelation.PARALLEL.equalLevel()

        then:
        result
    }

    def "test equal Level2"() {
        when:
        boolean result = CarTypeLevelRelation.HIGHER.equalLevel()

        then:
        !result
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme