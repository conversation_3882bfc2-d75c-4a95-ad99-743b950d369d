package com.ctrip.dcs.domain.schedule.process.impl

import cn.hutool.core.math.Money
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.GrabOrderCode
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.dsporder.factory.BatchConfirmDspOrderFactory
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.service.SortService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import java.util.concurrent.ExecutorService

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SelectVBKGrabOrderProcessTest extends Specification {
    @Mock
    Logger logger
    @Mock
    SelectGrabOrderRepository selectGrabOrderRepository
    @Mock
    ScheduleTaskRepository scheduleTaskRepository
    @Mock
    RecommendService recommendService
    @Mock
    SortService sortService
    @Mock
    CheckService checkService
    @Mock
    BatchConfirmDspOrderFactory dspOrderGroupFactory
    @Mock
    DriverOrderFactory driverOrderFactory
    @Mock
    QueryDriverService queryDriverService
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    ConfirmDspOrderService confirmDspOrderService
    @Mock
    ConfirmDspOrderService confirmSaasDspOrderService
    @Mock
    ConfirmDspOrderService confirmOldOrderService
    @Mock
    ConflictGateway conflictGateway
    @Mock
    QueryVehicleService queryVehicleService
    @Mock
    QueryTransportGroupService queryTransportGroupService
    @Mock
    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    MessageProviderService messageProducer
    @Mock
    SelfOrderQueryGateway selfOrderQueryGateway
    @Mock
    ExecutorService queryGrabDriverListThreadPool
    @Mock
    GrabOrderDetailRepository grabOrderDetailRepository
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driver
    @Mock
    CarVO carVO
    @Mock
    SupplierVO supplierVO
    @Mock
    VehicleVO vehicle
    @Mock
    TransportGroupVO transportGroup
    @Mock
    DriverOrderVO driverOrder
    @Mock
    ScheduleTaskDO scheduleTask
    @Mock
    SubSkuVO subSku
    @Mock
    DriverAndCarConfirmVO driverAndCarConfirm
    @Mock
    DspModelVO dspModel
    @Mock
    GrabOrderDO grabOrder
    @InjectMocks
    SelectVBKGrabOrderProcess selectVBKGrabOrderProcess

    def setup() {
        MockitoAnnotations.initMocks(this)
    }
//
    def "test execute"() {
        given:
        when(selectGrabOrderRepository.find(any(), any())).thenReturn([grabOrder])
        when(grabOrder.getDriverId()).thenReturn(1L)
        when(grabOrder.getDuid()).thenReturn("0-0-0-0-0-0-0")
        when(dspOrder.getOrderStatus()).thenReturn(220)
        when(scheduleTaskRepository.query(any())).thenReturn(scheduleTask)
        when(sortService.sort(any(), any(), any())).thenReturn([driver])
        when(checkService.check(any() as TakenCheckCommand)).thenReturn(new CheckModel(dspModel, CheckCode.PASS))
        when(driverOrderFactory.create(any(), any(), any(), any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))
        when(queryDriverService.queryDriver(anySet() , any(), anyLong())).thenReturn([driver])
        when(queryTransportGroupService.queryTransportGroup(any())).thenReturn(transportGroup)
        when(vbkDriverGrabOrderRepository.queryBySupplierId(any(), any())).thenReturn(new VBKDriverGrabOrderDO())
        when(taskRepository.query(any())).thenReturn(scheduleTask)
        when(driver.getDriverId()).thenReturn(1L)
        when(carVO.getCarId()).thenReturn(1L)
        when(driver.getCar()).thenReturn(carVO)
        when(driver.getDriverId()).thenReturn(1L)
        when(queryVehicleService.query(any(), any())).thenReturn(vehicle)
        when(dspModel.getOrder()).thenReturn(dspOrder)
        when(dspOrder.getSpId()).thenReturn(1)
        when(driver.getSupplier()).thenReturn(supplierVO)
        when(scheduleTask.getSubSku()).thenReturn(subSku)
        when(scheduleTask.getReward()).thenReturn(new Money())
        when(scheduleTask.getVbkReward()).thenReturn(new Money())
        when(subSku.getSubSkuId()).thenReturn(1)
        when(subSku.getDspType()).thenReturn(DspType.VBK_GRAB_ORDER)
        when(subSku.getTakenType()).thenReturn(TakenType.VBK_DRIVER_GRAB)

        when:
        selectVBKGrabOrderProcess.execute(dspOrder, subSku)

        then:
        subSku.getSubSkuId() == 1
    }

    def "test query VBK Driver Grab Order"() {
        given:
        when(vbkDriverGrabOrderRepository.queryBySupplierId(any(), any())).thenReturn(new VBKDriverGrabOrderDO())

        when:
        VBKDriverGrabOrderDO result = selectVBKGrabOrderProcess.queryVBKDriverGrabOrder(dspOrder)

        then:
        result != null
    }

    def "test query Transport Group"() {
        given:
        when(queryTransportGroupService.queryTransportGroup(any())).thenReturn(transportGroup)

        when:
        TransportGroupVO result = selectVBKGrabOrderProcess.queryTransportGroup(dspOrder)

        then:
        result != null
    }

    def "test create Driver Order"() {
        given:
        when(dspModel.getOrder()).thenReturn(dspOrder)
        when(dspModel.getDriver()).thenReturn(driver)
        when(dspOrder.getDspOrderId()).thenReturn("dspOrderId")
        when(driver.getDriverId()).thenReturn(1L)
        when(driverOrderFactory.create(any(), any(), any(), any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))
        when(vbkDriverGrabOrderRepository.queryBySupplierId(anyString(), anyInt())).thenReturn(new VBKDriverGrabOrderDO())

        when:
        DriverOrderVO result = selectVBKGrabOrderProcess.createDriverOrder(dspModel, transportGroup, scheduleTask)

        then:
        result.getDspOrderId() == dspOrderId
        result.getDriverOrderId() == driverOrderId

        where:
        dspOrderId   | driverOrderId
        "dspOrderId" | "driverOrderId"
    }

    def "test confirm Order"() {
        given:
        when(dspOrder.getSpId()).thenReturn(1)
        when(driver.getSupplier()).thenReturn(supplierVO)
        when(scheduleTask.getSubSku()).thenReturn(subSku)
        when(scheduleTask.getReward()).thenReturn(new Money())
        when(scheduleTask.getVbkReward()).thenReturn(new Money())
        when(subSku.getSubSkuId()).thenReturn(1)
        when(subSku.getDspType()).thenReturn(DspType.VBK_GRAB_ORDER)
        when(subSku.getTakenType()).thenReturn(TakenType.VBK_DRIVER_GRAB)
        when(driverOrder.getDriverOrderId()).thenReturn("1")

        when:
        GrabOrderCode result = selectVBKGrabOrderProcess.confirmOrder(dspOrder, driver, vehicle, transportGroup, driverOrder, scheduleTask)

        then:
        result == GrabOrderCode.SUCCESS
    }

    def "test confirm Order fail"() {
        given:
        when(dspOrder.getSpId()).thenReturn(1)
        when(driver.getSupplier()).thenReturn(supplierVO)
        when(scheduleTask.getSubSku()).thenReturn(subSku)
        when(scheduleTask.getReward()).thenReturn(new Money())
        when(scheduleTask.getVbkReward()).thenReturn(new Money())
        when(subSku.getSubSkuId()).thenReturn(1)
        when(subSku.getDspType()).thenReturn(DspType.VBK_GRAB_ORDER)
        when(subSku.getTakenType()).thenReturn(TakenType.VBK_DRIVER_GRAB)

        when:
        GrabOrderCode result = selectVBKGrabOrderProcess.confirmOrder(dspOrder, driver, vehicle, transportGroup, driverOrder, scheduleTask)

        then:
        result == GrabOrderCode.ORDER_TAKEN_FAIL
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme