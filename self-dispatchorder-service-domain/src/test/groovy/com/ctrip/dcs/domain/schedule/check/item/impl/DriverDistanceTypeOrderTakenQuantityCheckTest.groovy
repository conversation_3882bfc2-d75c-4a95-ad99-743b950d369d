package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification

class DriverDistanceTypeOrderTakenQuantityCheckTest extends Specification {

    DriverDistanceTypeOrderTakenQuantityCheck instance = new DriverDistanceTypeOrderTakenQuantityCheck()

    DspContext dispatchContext = Mock()
    CheckContext checkContext = Mock()
    CheckModel checkModel = Mock()
    DspModelVO dispatchModel = Mock()
    DspOrderVO dispatchOrder = Mock()
    DriverVO driverModel = Mock()

    def "test check"() {
        given:
        checkContext.getContext() >> dispatchContext
        checkModel.getModel() >> dispatchModel
        dispatchModel.getOrder() >> dispatchOrder
        dispatchModel.getDriver() >> driverModel
        dispatchOrder.getShortDisOrder() >> orderDistanceType
        dispatchOrder.getEstimatedUseTimeBj() >> new Date()
        driverModel.getDriverId() >> driverId
        dispatchContext.getDriverDistanceOrderCountMap() >> Map.of(driverId, Map.of(0, longOrderCount, 1, shortOrderCount))
        dispatchContext.getDriverDistanceTypeMap() >> Map.of(driverId, driverDistanceType)
        checkContext.queryDriverTakenDistanceOrderThreshold(_, _, _, _, _) >> limitOrderCount
        when:
        CheckCode actual = instance.check(checkModel, checkContext)
        then:
        actual == expected
        where:
        driverId | orderDistanceType | driverDistanceType | shortOrderCount | longOrderCount | limitOrderCount | expected
        1L       | null              | -1                 | 0L              | 3L             | 5               | CheckCode.PASS
        1L       | null              | -1                 | 0L              | 5L             | 5               | CheckCode.DRIVER_DISTANCE_TYPE_ORDER_TAKEN_EXCEEDED_THRESHOLD
        1L       | null              | -1                 | 0L              | 5L             | 6               | CheckCode.PASS
        1L       | 0                 | 0                  | 0L              | 5L             | 6               | CheckCode.PASS
        1L       | 0                 | 0                  | 0L              | 6L             | 6               | CheckCode.DRIVER_DISTANCE_TYPE_ORDER_TAKEN_EXCEEDED_THRESHOLD
        1L       | 0                 | 1                  | 0L              | 100L           | 6               | CheckCode.DRIVER_DISTANCE_TYPE_ORDER_TAKEN_EXCEEDED_THRESHOLD
        1L       | 0                 | 1                  | 0L              | 50L            | 50              | CheckCode.DRIVER_DISTANCE_TYPE_ORDER_TAKEN_EXCEEDED_THRESHOLD
        1L       | 0                 | 1                  | 49L             | 0L             | 50              | CheckCode.PASS
        1L       | 1                 | 1                  | 49L             | 0L             | 50              | CheckCode.PASS
        1L       | 1                 | 1                  | 50L             | 50L            | 50              | CheckCode.DRIVER_DISTANCE_TYPE_ORDER_TAKEN_EXCEEDED_THRESHOLD
    }

}