package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckConfig
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.exception.CheckItemException
import com.ctrip.dcs.domain.schedule.exception.CheckItemValidateException
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class AbstractCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverVO driverVO
    @Mock
    TransportGroupVO transportGroupVO
    @Mock
    CheckContext checkContext

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test check"() {
        given:
        AbstractCheck abstractCheck = new AbstractCheck() {

            @Override
            protected CheckCode check(CheckModel checkModel, CheckContext context) throws CheckItemException {
                return CheckCode.PASS
            }

            @Override
            protected CheckCode downgrade() {
                return CheckCode.PASS
            }
        }
        when:
        List<CheckModel> result = abstractCheck.check([new CheckModel(new DspModelVO(dspOrderVO, driverVO, transportGroupVO))], new CheckContext())

        then:
        result.get(0).getCheckCode() == CheckCode.PASS
    }

    def "test check 1"() {
        given:
        AbstractCheck abstractCheck = new AbstractCheck() {

            @Override
            protected void load(List<CheckModel> models, CheckContext context) {
                throw new BizException("500", "test")
            }

            @Override
            protected CheckCode check(CheckModel checkModel, CheckContext context) throws CheckItemException {
                return CheckCode.PASS
            }

            @Override
            protected CheckCode downgrade() {
                return CheckCode.PASS
            }
        }
        when:
        List<CheckModel> result = abstractCheck.check([new CheckModel(new DspModelVO(dspOrderVO, driverVO, transportGroupVO))], new CheckContext())

        then:
        result.get(0).getCheckCode() == CheckCode.PASS
    }

    def "test check 2"() {
        given:
        AbstractCheck abstractCheck = new AbstractCheck() {

            @Override
            protected void validate(CheckModel checkModel, CheckContext context) throws CheckItemValidateException {
                throw new CheckItemValidateException()
            }

            @Override
            protected CheckCode check(CheckModel checkModel, CheckContext context) throws CheckItemException {
                return CheckCode.PASS
            }

            @Override
            protected CheckCode downgrade() {
                return CheckCode.PASS
            }
        }
        when:
        List<CheckModel> result = abstractCheck.check([new CheckModel(new DspModelVO(dspOrderVO, driverVO, transportGroupVO))], new CheckContext())

        then:
        result.get(0).getCheckCode() == CheckCode.PASS
    }

    def "test check 3"() {
        given:
        AbstractCheck abstractCheck = new AbstractCheck() {

            @Override
            protected CheckCode check(CheckModel checkModel, CheckContext context) throws CheckItemException {
                throw new CheckItemException(CheckCode.VEHICLE_OFFLINE)
            }

            @Override
            protected CheckCode downgrade() {
                return CheckCode.PASS
            }
        }
        when:
        List<CheckModel> result = abstractCheck.check([new CheckModel(new DspModelVO(dspOrderVO, driverVO, transportGroupVO))], new CheckContext())

        then:
        result.get(0).getCheckCode() == CheckCode.VEHICLE_OFFLINE
    }

    def "test check 4"() {
        given:
        AbstractCheck abstractCheck = new AbstractCheck() {

            @Override
            protected CheckCode check(CheckModel checkModel, CheckContext context) throws CheckItemException {
                throw new BizException("500", "error")
            }

            @Override
            protected CheckCode downgrade() {
                return CheckCode.PASS
            }
        }
        when:
        List<CheckModel> result = abstractCheck.check([new CheckModel(new DspModelVO(dspOrderVO, driverVO, transportGroupVO))], new CheckContext())

        then:
        result.get(0).getCheckCode() == CheckCode.PASS
    }

    def "test isLastCheck"() {
        given:
        AbstractCheck abstractCheck = new AbstractCheck() {

            @Override
            protected CheckCode check(CheckModel checkModel, CheckContext context) throws CheckItemException {
                throw new BizException("500", "error")
            }

            @Override
            protected CheckCode downgrade() {
                return CheckCode.PASS
            }

            @Override
            String getCheckId() {
                return "I1"
            }
        }
        def subSku = SubSkuVO.builder().check(CheckConfig.builder().checkId("1").dspCheckItem(["I1", "I2"]).takenCheckItem(["I2", "I1"]).build()).build()
        when:
        boolean result = abstractCheck.isLastCheck(subSku, stage)

        then:
        result == isLastCheck

        where:
        stage          || isLastCheck
        DspStage.DSP   || false
        DspStage.TAKEN || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme