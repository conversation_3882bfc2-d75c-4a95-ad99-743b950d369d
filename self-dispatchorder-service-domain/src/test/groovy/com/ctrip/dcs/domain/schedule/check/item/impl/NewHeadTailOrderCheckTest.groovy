package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverHeadTailOrderVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.value.carconfig.HeadTailLimitValueVO
import spock.lang.*


/**
 *
 *
 * <AUTHOR>
 * @date 2024/12/13 11:19:11
 * @version 1.0
 */
class NewHeadTailOrderCheckTest extends Specification {
    def testObj = new NewHeadTailOrderCheck()


    @Unroll
    def "checkTailTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"

        when:
        def result = spy.checkTail(limitValue, headTail, order)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult

        where: "表格方式验证多种分支调用场景"
        limitValue                               | headTail                                                                                                                              | order                                                                                                                                                  || expectedResult
//        null                                     | new DriverHeadTailOrderVO(endWorkTime: new Date(), tailLbsDuration: 0D, tailLbsDistance: 0D)                                          | new DspOrderVO(predicServiceStopTime: new Date(), estimatedKm: 0 as BigDecimal, costAmount: 0 as BigDecimal)                                           || CheckCode.PASS
//        new HeadTailLimitValueVO(1d, null, 2d,1d)   | new DriverHeadTailOrderVO(endWorkTime: new Date(), tailLbsDuration: 0D, tailLbsDistance: 0D)                                          | new DspOrderVO(predicServiceStopTime: new Date(), estimatedKm: 0 as BigDecimal, costAmount: 0 as BigDecimal)                                           || CheckCode.PASS
//        new HeadTailLimitValueVO(1d, -1d, 2d,1d)    | new DriverHeadTailOrderVO(endWorkTime: new Date(), tailLbsDuration: 0D, tailLbsDistance: 0D)                                          | new DspOrderVO(predicServiceStopTime: new Date(), estimatedKm: 0 as BigDecimal, costAmount: 0 as BigDecimal)                                           || CheckCode.PASS
//        new HeadTailLimitValueVO(60d, 30D, 0.5d,1d) | new DriverHeadTailOrderVO(endWorkTime: DateUtil.parseDateStr2Date("2024-12-20 23:00:00"), tailLbsDuration: 65D*60, tailLbsDistance: 0D)  | new DspOrderVO(predicServiceStopTime: DateUtil.parseDateStr2Date("2024-12-20 22:30:00"), estimatedKm: 0 as BigDecimal, costAmount: 0 as BigDecimal)    || CheckCode.TAIL_ORDER_LIMIT
//        new HeadTailLimitValueVO(60d, 30D, 0,null) | new DriverHeadTailOrderVO(endWorkTime: DateUtil.parseDateStr2Date("2024-12-20 23:00:00"), tailLbsDuration: 65D*60, tailLbsDistance: 10D)  | new DspOrderVO(predicServiceStopTime: DateUtil.parseDateStr2Date("2024-12-20 22:00:00"), estimatedKm: 10 as BigDecimal, costAmount: 0 as BigDecimal)    || CheckCode.PASS
//        new HeadTailLimitValueVO(60d, 30D, 0d,0d)   | new DriverHeadTailOrderVO(endWorkTime: DateUtil.parseDateStr2Date("2024-12-20 23:00:00"), tailLbsDuration: 65D*60, tailLbsDistance: 10D)  | new DspOrderVO(predicServiceStopTime: DateUtil.parseDateStr2Date("2024-12-20 22:00:00"), estimatedKm: 10 as BigDecimal, costAmount: 0 as BigDecimal)    || CheckCode.PASS
//        new HeadTailLimitValueVO(60d, 30D, 0.5d,1d) | new DriverHeadTailOrderVO(endWorkTime: DateUtil.parseDateStr2Date("2024-12-20 23:00:00"), tailLbsDuration: 65D*60, tailLbsDistance: 60D*1000) | new DspOrderVO(predicServiceStopTime: DateUtil.parseDateStr2Date("2024-12-20 22:00:00"), estimatedKm: 80 as BigDecimal, costAmount: 100 as BigDecimal) || CheckCode.PASS
        new HeadTailLimitValueVO(60d, 30D, 0.9d,1d) | new DriverHeadTailOrderVO(endWorkTime: DateUtil.parseDateStr2Date("2024-12-20 23:00:00"), tailLbsDuration: 65D*60, tailLbsDistance: 60D*1000) | new DspOrderVO(predicServiceStopTime: DateUtil.parseDateStr2Date("2024-12-20 22:00:00"), estimatedKm: 80 as BigDecimal, costAmount: 100 as BigDecimal) || CheckCode.TAIL_ORDER_MILEAGE_VALUE_LIMIT
//        new HeadTailLimitValueVO(60d, 30D, 0.9d,1d) | new DriverHeadTailOrderVO(endWorkTime: DateUtil.parseDateStr2Date("2024-12-20 23:00:00"), tailLbsDuration: 65D*60, tailLbsDistance: 60D*1000) | new DspOrderVO(predicServiceStopTime: DateUtil.parseDateStr2Date("2024-12-20 22:00:00"), estimatedKm: 80 as BigDecimal, costAmount: 100 as BigDecimal,shortDisOrder: 1) || CheckCode.TAIL_ORDER_MILEAGE_VALUE_LIMIT

    }


}

