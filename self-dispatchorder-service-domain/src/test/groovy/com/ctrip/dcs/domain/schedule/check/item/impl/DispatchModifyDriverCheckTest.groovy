package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class DispatchModifyDriverCheckTest extends Specification{
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    Logger logger
    @Mock
    SupplierVO supplier
    @Mock
    TransportGroupVO transportGroup
    @InjectMocks
    DispatchModifyDriverCheck check

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
        Mockito.when(order.getUserOrderId()).thenReturn("1")
    }
    def "supplier_check_false"(){
        given:
        Mockito.when(context.getOrderDispatchModifySupplierIds()).thenReturn(Arrays.asList("1","2"))
        Mockito.when(driverInfo.getSupplier()).thenReturn(supplier)
        Mockito.when(supplier.getSupplierId()).thenReturn(1L)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getSupplierId()).thenReturn(1L)
        when:
        CheckCode result = check.check(checkModel, context);
        then:
        result == CheckCode.DSP_MODIFY_DRV_BY_VBK_SUPPLIER
    }
    def "supplier_check_false2"(){
        given:
        Mockito.when(context.getOrderDispatchModifySupplierIds()).thenReturn(Arrays.asList("1","2"))
        Mockito.when(driverInfo.getSupplier()).thenReturn(supplier)
        Mockito.when(supplier.getSupplierId()).thenReturn(3L)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getSupplierId()).thenReturn(1L)
        when:
        CheckCode result = check.check(checkModel, context);
        then:
        result == CheckCode.DSP_MODIFY_DRV_BY_VBK_SUPPLIER
    }

    def "driver_check_false"(){
        given:
        Mockito.when(context.getOrderDispatchModifySupplierIds()).thenReturn(new ArrayList<String>())
        Mockito.when(context.getOrderDispatchModifyDriverIds()).thenReturn(Arrays.asList("1","2"))
        when:
        CheckCode result = check.check(checkModel, context);
        then:
        result == CheckCode.DSP_MODIFY_DRV_BY_DRIVER
    }
    def "pass"(){
        given:
        Mockito.when(context.getOrderDispatchModifySupplierIds()).thenReturn(new ArrayList<String>())
        Mockito.when(context.getOrderDispatchModifyDriverIds()).thenReturn(new ArrayList<String>())
        when:
        CheckCode result = check.check(checkModel, context);
        then:
        result == CheckCode.PASS
    }
}
