package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.google.common.collect.Sets
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class UrgentOrderSupplierVisitorTest extends Specification {
    @Mock
    SelfOrderQueryGateway selfOrderQueryGateway
    Set<Long> set = Sets.newHashSet()
    @Mock
    CheckContext context

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {

        given:
        UrgentOrderSupplierVisitor visitor = new UrgentOrderSupplierVisitor(selfOrderQueryGateway)
        when(selfOrderQueryGateway.queryDispatcherGrabOrderSupplierIds(anyBoolean())).thenReturn([1l])
        when(context.getUrgentOrderSupplierIds()).thenReturn(set)

        when:
        visitor.visit(context)
        visitor.visit(context)

        then:
        set.size() == 1
        set.contains(1l)

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme