package com.ctrip.dcs.domain.schedule.check.source.impl


import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class VBKDriverCheckSourceTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspContextService dspContextService
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverVO driver1
    @Mock
    DriverVO driver2
    @Mock
    TransportGroupVO transportGroupVO
    @InjectMocks
    VBKGrabDriverCheckSource vBKDriverCheckSource

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test init"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrderVO)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([]).thenReturn([driver1, driver2])
        when(driver1.getTransportGroups()).thenReturn([transportGroupVO])

        when:
        vBKDriverCheckSource.init(checkContext)
        List<CheckModel> result = vBKDriverCheckSource.init(checkContext)

        then:
        result.size() == 1
        result.get(0).getCheckCode() == CheckCode.PASS
    }

    def "test taken"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrderVO)
        when(checkContext.getDriver()).thenReturn(driver1)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(dspContextService.queryVBKGrabDrivers(any())).thenReturn([]).thenReturn([driver2]).thenReturn([driver1, driver2])
        when(driver1.getTransportGroups()).thenReturn([]).thenReturn([transportGroupVO])
        when(driver1.getDriverId()).thenReturn(1L)
        when(driver2.getDriverId()).thenReturn(2L)

        when:
        CheckModel r1 = vBKDriverCheckSource.taken(checkContext)
        CheckModel r2 = vBKDriverCheckSource.taken(checkContext)
        CheckModel r3 = vBKDriverCheckSource.taken(checkContext)
        CheckModel r4 = vBKDriverCheckSource.taken(checkContext)

        then:
        r1.getCheckCode() == CheckCode.NULL
        r2.getCheckCode() == CheckCode.NULL
        r3.getCheckCode() == CheckCode.NULL
        r4.getCheckCode() == CheckCode.PASS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme