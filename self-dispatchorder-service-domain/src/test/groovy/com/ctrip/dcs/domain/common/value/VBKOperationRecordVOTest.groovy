package com.ctrip.dcs.domain.common.value


import spock.lang.Specification

/**
 * <AUTHOR>
 */
class VBKOperationRecordVOTest extends Specification {

    def "test builder"() {
        when:
        VBKOperationRecordVO result = VBKOperationRecordVO.builder()
                .id(1L)
                .supplyOrderId("1")
                .userOrderId("2")
                .sysUserAccount("3")
                .operUserName("4")
                .operUserType("5")
                .operateType("6")
                .operateName("7")
                .timeZone(10D)
                .operateLocalTime(1L)
                .recordType("8")
                .beforeOperateData("9")
                .afterOperateData("10")
                .comment("11")
                .supplierId(2)
                .vendorOrderId("12")
                .build()
        def map = result.toMap()
        then:
        result.getId() == 1L
        result.getSupplyOrderId() == map.get("sub_order_id")
        result.getUserOrderId() == map.get("order_id")
        result.getSysUserAccount() == map.get("operate_user_account")
        result.getOperUserName() == map.get("operate_user_name")
        result.getOperUserType() == map.get("operate_user_type")
        result.getOperateType() == map.get("operate_type")
        result.getOperateName() == map.get("operate_name")
        result.getTimeZone().toString() == map.get("operate_time_zone")
        result.getOperateLocalTime().toString() == map.get("operate_time")
        result.getRecordType() == map.get("record_type")
        result.getBeforeOperateData() == map.get("before_oper_data")
        result.getAfterOperateData() == map.get("after_oper_data")
        result.getComment() == map.get("comment")
        result.getSupplierId().toString() == map.get("supplier_id")
        result.getVendorOrderId() == map.get("vendor_order_id")
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme