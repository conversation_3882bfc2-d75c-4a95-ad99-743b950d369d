package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.QueryDriverMileageProfitService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.HighPriorityDriverGateway
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class HighPriorityDriverMileageProfitVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driver
    @Mock
    DriverPointsVisitor driverPointsVisitor
    @Mock
    HighPriorityDriverGateway highPriorityDriverGateway
    @Mock
    QueryDriverMileageProfitService queryDriverMileageProfitService

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspOrder.getCityId()).thenReturn(0)
        when(driverPointsVisitor.filter(any(), any())).thenReturn([driver])
        when(highPriorityDriverGateway.queryHighPriorityDriver(any(), anyInt(), any())).thenReturn([1l])
        when(queryDriverMileageProfitService.queryTodayDriverMileageProfit(any(), any())).thenReturn([new DriverMileageProfitVO(1l, 1, 1, 1, 1D, 1D, 1D, 1D)])
        HighPriorityDriverMileageProfitVisitor highPriorityDriverMileageProfitVisitor = new HighPriorityDriverMileageProfitVisitor(dspOrder, [driver], driverPointsVisitor, highPriorityDriverGateway, queryDriverMileageProfitService)

        when:
        def result = highPriorityDriverMileageProfitVisitor.visit(checkContext)

        then:
        result == null
    }

    def "test query High Priority Driver Profit"() {
        given:
        when(dspContext.getDriverPointsMap()).thenReturn([1L: new DriverPointsVO()])
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspOrder.getCityId()).thenReturn(0)
        when(driverPointsVisitor.filter(any(), any())).thenReturn([driver])
        when(highPriorityDriverGateway.queryHighPriorityDriver(any(), anyInt(), any())).thenReturn([1l])
        when(queryDriverMileageProfitService.queryTodayDriverMileageProfit(any(), any())).thenReturn([])
        HighPriorityDriverMileageProfitVisitor highPriorityDriverMileageProfitVisitor = new HighPriorityDriverMileageProfitVisitor(dspOrder, [driver], driverPointsVisitor, highPriorityDriverGateway, queryDriverMileageProfitService)

        when:
        def result = highPriorityDriverMileageProfitVisitor.visit(checkContext)

        then:
        result == null
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme