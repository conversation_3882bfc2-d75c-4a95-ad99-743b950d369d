package com.ctrip.dcs.domain.common.value


import spock.lang.Specification
import spock.lang.Unroll

class PeriodsVOTest extends Specification {

    @Unroll
    def "test contains"() {
        given:
        PeriodsVO periodsVO = new PeriodsVO(periods)

        when:
        boolean result = periodsVO.contains(0)

        then:
        result == r

        where:
        periods       || r
        []            || false
        ["1:00~2:00"] || false
        ["2:00~1:59"] || true

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme