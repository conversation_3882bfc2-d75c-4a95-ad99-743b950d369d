package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.value.DriverMileageProfitVO
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverAddProfitFeature
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class DriverAddProfitFeatureSortTest extends Specification {
    @Mock
    SortModel model
    @Mock
    SortContext context
    @Mock
    DriverVO driver
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverMileageProfitVO dmp

    @InjectMocks
    DriverAddProfitFeature feature = new DriverAddProfitFeature()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(model.getModel()).thenReturn(dspModelVO)
        Mockito.when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getDspOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getExpectDriverMileageProfit(Mockito.any())).thenReturn(dmp)
        Mockito.when(dspModelVO.getDriver()).thenReturn(driver)
        Mockito.when(driver.getDriverId()).thenReturn(1L)
    }

    @Unroll
    def "test value"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()

        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)
        Mockito.when(dmp.getProfit()).thenReturn(11.0D)
        Mockito.when(dmp.expect(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(9.0D)


        when:
        Value res = feature.value(model, context)

        then:
        res.value == 2.0


    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme