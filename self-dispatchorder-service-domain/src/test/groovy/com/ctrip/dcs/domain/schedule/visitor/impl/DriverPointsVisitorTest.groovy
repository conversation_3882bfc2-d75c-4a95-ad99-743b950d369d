package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverPointsVisitorTest extends Specification {
    @Mock
    DriverVO driver
    @Mock
    DriverPointsGateway dcsDriverLevelServiceGateway
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DriverPointsVO driverPointsVO
    @InjectMocks
    DriverPointsVisitor driverPointsVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getDriverPointsMap()).thenReturn([1L:driverPointsVO])
        DriverPointsVisitor driverPointsVisitor = new DriverPointsVisitor([driver], dcsDriverLevelServiceGateway)

        when:
        driverPointsVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }

    def "test visit 2"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(dspContext.getDriverPointsMap()).thenReturn([1L:driverPointsVO])
        when(dcsDriverLevelServiceGateway.batchQueryDriverTotalPoints(any())).thenReturn([driverPointsVO])
        DriverPointsVisitor driverPointsVisitor = new DriverPointsVisitor([driver], dcsDriverLevelServiceGateway)

        when:
        driverPointsVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme