package com.ctrip.dcs.domain.dsporder.value

import org.junit.Assert
import spock.lang.Specification

import java.sql.Date
import java.sql.Timestamp

/**
 * <AUTHOR>
 * @since 2025/4/30 17:40
 */
class DrvGuidVehVehicleVOTest extends Specification {
    def testObj = new DrvGuidVehVehicleVO()
    def regstDate = Mock(Date)
    def datachangeCreatetime = Mock(Timestamp)
    def datachangeLasttime = Mock(Timestamp)
    def temporaryDispatchEndDatetime = Mock(Timestamp)

    def setup() {
        testObj.regstDate = regstDate
        testObj.temporaryDispatchEndDatetime = temporaryDispatchEndDatetime
        testObj.datachangeCreatetime = datachangeCreatetime
        testObj.datachangeLasttime = datachangeLasttime
    }

    def "test"() {
        given:
        when:
        DrvGuidVehVehicleVO vehVehicleVO = new DrvGuidVehVehicleVO();
        vehVehicleVO.setVehicleId(1L);
        vehVehicleVO.setVehicleLicense("vehicleLicense");
        vehVehicleVO.setSupplierId(2L);
        vehVehicleVO.setCountryId(3L);
        vehVehicleVO.setCountryName("countryName");
        vehVehicleVO.setCityId(4L);
        vehVehicleVO.setVehicleTypeId(5L);
        vehVehicleVO.setVehicleBrandId(6L);
        vehVehicleVO.setVehicleSeries(7L);
        vehVehicleVO.setVehicleColorId(8L);
        vehVehicleVO.setVehicleEnergyType(9);
        vehVehicleVO.setVin("vin");
        vehVehicleVO.setRegstDate(new Date(new java.util.Date().getTime()));
        vehVehicleVO.setUsingNature(10);
        vehVehicleVO.setNetTansCtfctImg("netTansCtfctImg");
        vehVehicleVO.setVehicleCertiImg("vehicleCertiImg");
        vehVehicleVO.setVehicleFullImg("vehicleFullImg");
        vehVehicleVO.setVehicleFrontImg("vehicleFrontImg");
        vehVehicleVO.setVehicleBackImg("vehicleBackImg");
        vehVehicleVO.setVehicleTrunkImg("vehicleTrunkImg");
        vehVehicleVO.setHasDrv(false);
        vehVehicleVO.setComments("comments");
        vehVehicleVO.setDatachangeCreatetime(new Timestamp(new java.util.Date().getTime()));
        vehVehicleVO.setCreateUser("createUser");
        vehVehicleVO.setModifyUser("modifyUser");
        vehVehicleVO.setDatachangeLasttime(new Timestamp(new java.util.Date().getTime()));
        vehVehicleVO.setVehicleStatus(11);
        vehVehicleVO.setVehicleLicenseOwner("vehicleLicenseOwner");
        vehVehicleVO.setCategorySynthesizeCode(12);
        vehVehicleVO.setApproveStatus(13);
        vehVehicleVO.setNetAppealMaterials("netAppealMaterials");
        vehVehicleVO.setVehicleLicenseCityId(14L);
        vehVehicleVO.setVersionFlag(15);
        vehVehicleVO.setCertificateConfig("certificateConfig");
        vehVehicleVO.setOcrFieldValue("ocrFieldValue");
        vehVehicleVO.setVehicleLicenseAppealMaterials("vehicleLicenseAppealMaterials");
        vehVehicleVO.setVehicleNetCertNo("vehicleNetCertNo");
        vehVehicleVO.setActive(false);
        vehVehicleVO.setOcrPassStatus(false);
        vehVehicleVO.setOcrPassStatusJson("ocrPassStatusJson");
        vehVehicleVO.setAuditStatus(1);
        vehVehicleVO.setTemporaryDispatchMark(0);
        vehVehicleVO.setVehicleAgeType("vehicleAgeType");
        vehVehicleVO.setTemporaryDispatchEndDatetime(new Timestamp(new java.util.Date().getTime()));
        vehVehicleVO.setVehicleBrandName("vehicleBrandName");
        vehVehicleVO.setVehicleSeriesName("vehicleSeriesName");
        vehVehicleVO.setVehicleTypeName("vehicleTypeName");
        vehVehicleVO.setVehicleCategory(0);
        vehVehicleVO.setVehicleColorName("vehicleColorName");
        vehVehicleVO.setSupplierName("supplierName");

        then:
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleId(),1L))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleLicense(),"vehicleLicense"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getSupplierId(),2L))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getCountryName(),"countryName"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getCityId(),4L))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleTypeId(),5L))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleBrandId(),6L))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleSeries(),7L))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleColorId(),8L))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleEnergyType(),9))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVin(),"vin"))
        Assert.assertTrue(Objects.nonNull(vehVehicleVO.getRegstDate()))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getUsingNature(),10))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getNetTansCtfctImg(),"netTansCtfctImg"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleCertiImg(),"vehicleCertiImg"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleFullImg(),"vehicleFullImg"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleFrontImg(),"vehicleFrontImg"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleBackImg(),"vehicleBackImg"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleTrunkImg(),"vehicleTrunkImg"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getHasDrv(),false))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getComments(),"comments"))
        Assert.assertTrue(Objects.nonNull(vehVehicleVO.getDatachangeCreatetime()))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getCreateUser(),"createUser"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getModifyUser(),"modifyUser"))
        Assert.assertTrue(Objects.nonNull(vehVehicleVO.getDatachangeLasttime()))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleStatus(),11))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleLicenseOwner(),"vehicleLicenseOwner"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getCategorySynthesizeCode(),12))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getApproveStatus(),13))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getNetAppealMaterials(),"netAppealMaterials"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleLicenseCityId(),14L))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVersionFlag(),15))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getCertificateConfig(),"certificateConfig"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getOcrFieldValue(),"ocrFieldValue"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleLicenseAppealMaterials(),"vehicleLicenseAppealMaterials"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleNetCertNo(),"vehicleNetCertNo"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getActive(),false))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getOcrPassStatus(),false))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getActive(),false))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getOcrPassStatusJson(),"ocrPassStatusJson"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getAuditStatus(),1))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getTemporaryDispatchMark(),0))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleAgeType(),"vehicleAgeType"))
        Assert.assertTrue(Objects.nonNull(vehVehicleVO.getTemporaryDispatchEndDatetime()))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleBrandName(),"vehicleBrandName"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleSeriesName(),"vehicleSeriesName"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleTypeName(),"vehicleTypeName"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleCategory(),0))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getVehicleColorName(),"vehicleColorName"))
        Assert.assertTrue(Objects.equals(vehVehicleVO.getSupplierName(),"supplierName"))
    }
}
