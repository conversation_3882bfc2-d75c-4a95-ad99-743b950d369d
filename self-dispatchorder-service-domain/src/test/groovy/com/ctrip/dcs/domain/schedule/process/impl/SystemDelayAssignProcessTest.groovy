package com.ctrip.dcs.domain.schedule.process.impl

import cn.hutool.core.math.Money
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.exception.OrderStatusException
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryVehicleService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckConfig
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand
import com.ctrip.dcs.domain.schedule.check.source.CheckSourceConfig
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.DspDelayGateway
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DelayOrderVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.never
import static org.mockito.Mockito.times
import static org.mockito.Mockito.verify
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SystemDelayAssignProcessTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    RecommendService recommendService
    @Mock
    ConfirmDspOrderService confirmDspOrderService
    @Mock
    MessageProviderService messageProducer
    @Mock
    DspDelayGateway dspDelayGateway
    @Mock
    DriverOrderFactory driverOrderFactory
    @Mock
    CheckService checkService
    @Mock
    ScheduleTaskDO scheduleTaskDO
    @Mock
    DspOrderVO dspOrderVO;
    @Mock
    SubSkuVO subSkuVO
    @Mock
    CheckConfig check
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DriverVO driverVO;
    @Mock
    TransportGroupVO transportGroupVO;
    @Mock
    private QueryVehicleService queryVehicleService;
    @Mock
    SupplierVO supplierVO
    @InjectMocks
    SystemDelayAssignProcess systemDelayAssignProcess

    def setup() {
        MockitoAnnotations.initMocks(this)
        when(transportGroupVO.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(dspModelVO.getTransportGroup()).thenReturn(transportGroupVO)
        when(driverVO.getTransportGroups()).thenReturn([transportGroupVO])
        when(driverVO.getSupplier()).thenReturn(supplierVO)
        when(subSkuVO.getSubSkuId()).thenReturn(1)
        when(subSkuVO.getTakenType()).thenReturn(TakenType.ASSISTANT)
        when(subSkuVO.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        when(scheduleTaskDO.getTaskId()).thenReturn(1L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(1L)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("1")
        when(scheduleTaskDO.getRound()).thenReturn(1)
        when(checkModel.isPass()).thenReturn(true)
        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(subSkuVO.getCheck()).thenReturn(check)
        when(check.getCheckSourceId()).thenReturn("S1")
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(checkService.check(any() as DspCheckCommand)).thenReturn([checkModel])
        when(recommendService.recommend(any(), any(), any())).thenReturn([new SortModel(dspModelVO)])
    }

    def "test execute short"() {
        given:
        def res = 1;

        when(check.getCheckSourceId()).thenReturn("S7")
        when(dspDelayGateway.insert(any(), any())).thenReturn(new DelayOrderVO("dspOrderId", 0, 0, "executeTime", "executeTimeDeadline"))
        when(driverOrderFactory.create(any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))

        when:
        systemDelayAssignProcess.execute(scheduleTaskDO, dspOrderVO)
        res++
        then:
        res > 1
    }

    def "test execute short_1"() {
        given:
        def res = 1;

        when(check.getCheckSourceId()).thenReturn("S1")
        when(dspDelayGateway.insert(any(), any())).thenReturn(new DelayOrderVO("dspOrderId", 0, 0, "executeTime", "executeTimeDeadline"))
        when(driverOrderFactory.create(any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))

        when(dspOrderVO.getShortDisOrder()).thenReturn(1)
        when:
        systemDelayAssignProcess.execute(scheduleTaskDO, dspOrderVO)
        res++
        then:
        res > 1
    }

    def "test execute short_2"() {
        given:
        def res = 1;

        when(check.getCheckSourceId()).thenReturn("S2")
        when(dspDelayGateway.insert(any(), any())).thenReturn(new DelayOrderVO("dspOrderId", 0, 0, "executeTime", "executeTimeDeadline"))
        when(driverOrderFactory.create(any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))

        when(dspOrderVO.getShortDisOrder()).thenReturn(1)
        when:
        systemDelayAssignProcess.execute(scheduleTaskDO, dspOrderVO)
        res++
        then:
        res > 1
    }

    def "test execute"() {
        given:
        def res = 1;

        when(dspDelayGateway.insert(any(), any())).thenReturn(new DelayOrderVO("dspOrderId", 0, 0, "executeTime", "executeTimeDeadline"))
        when(driverOrderFactory.create(any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))

        when:
        systemDelayAssignProcess.execute(scheduleTaskDO, dspOrderVO)
        res++
        then:
        res > 1
    }

    def "test execute 2"() {
        given:
        def res = 1;

        when(dspDelayGateway.insert(any(), any())).thenReturn(new DelayOrderVO("dspOrderId", 1, 0, "executeTime", "executeTimeDeadline"))
        when(driverOrderFactory.create(any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))

        when:
        systemDelayAssignProcess.execute(scheduleTaskDO, dspOrderVO)
        res++

        then:
        res > 1
    }

    def "test execute 3"() {
        given:
        def res = 1;

        when(dspDelayGateway.insert(any(), any())).thenReturn(new DelayOrderVO("dspOrderId", 1, 1, "executeTime", "executeTimeDeadline"))
        when(driverOrderFactory.create(any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))

        when:
        systemDelayAssignProcess.execute(scheduleTaskDO, dspOrderVO)
        res++

        then:
        res > 1
    }

    def "test execute 4"() {
        given:
        when(dspDelayGateway.insert(any(), any())).thenReturn(new DelayOrderVO("dspOrderId", 1, 1, "executeTime", "executeTimeDeadline"))
        when(driverOrderFactory.create(any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))
        when(confirmDspOrderService.confirm(any() as ServiceProviderConfirmVO)).thenThrow(new OrderStatusException(ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR))

        when:
        systemDelayAssignProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        def exception = thrown(BizException)
        exception.getCode() == ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR.getCode()
    }

    def "test dspTakenExecute Exception"() {
        given:
        ScheduleTaskDO scheduleTaskDO = ScheduleTaskDO.builder().taskId(1L).scheduleId(1L).dspOrderId("1").round(1).subSku(SubSkuVO.builder().subSkuId(1).takenType(TakenType.ASSISTANT).dspType(DspType.DELAY_ASSIGN).subSkuName("111").build()).reward(new Money()).build()
        CheckModel checkModel1 = new CheckModel(new DspModelVO(), CheckCode.NULL);
        DspModelVO dspModelVO1 = new DspModelVO()
        dspModelVO1.setTransportGroup(new TransportGroupVO())
        dspModelVO1.setOrder(new DspOrderVO())
        DriverVO driverVO1 = new DriverVO()
        driverVO1.setSupplier(new SupplierVO())
        dspModelVO1.setDriver(driverVO1)
        when(checkService.check(any() as TakenCheckCommand)).thenReturn(checkModel1)
        when(driverOrderFactory.createForDelay(any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))
        when(confirmDspOrderService.confirm(any() as ServiceProviderConfirmVO)).thenThrow(new OrderStatusException(ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR))

        when:
        systemDelayAssignProcess.dspTakenExecute(scheduleTaskDO, dspModelVO1)

        then:
        def exception = thrown(BizException)
        exception.getCode() == "1"
    }


    def "test dspTakenExecute Exception2"() {
        given:
        ScheduleTaskDO scheduleTaskDO = ScheduleTaskDO.builder().taskId(1L).scheduleId(1L).dspOrderId("1").round(1).subSku(SubSkuVO.builder().subSkuId(1).takenType(TakenType.ASSISTANT).dspType(DspType.DELAY_ASSIGN).subSkuName("111").build()).reward(new Money()).build()
        CheckModel checkModel1 = new CheckModel(new DspModelVO(), CheckCode.PASS);
        DspModelVO dspModelVO1 = new DspModelVO()
        dspModelVO1.setTransportGroup(new TransportGroupVO())
        dspModelVO1.setOrder(new DspOrderVO())
        DriverVO driverVO1 = new DriverVO()
        driverVO1.setSupplier(new SupplierVO())
        dspModelVO1.setDriver(driverVO1)
        when(checkService.check(any() as TakenCheckCommand)).thenReturn(checkModel1)
        when(driverOrderFactory.createForDelay(any(), any())).thenReturn(null)
        when(confirmDspOrderService.confirm(any() as ServiceProviderConfirmVO)).thenThrow(new OrderStatusException(ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR))

        when:
        systemDelayAssignProcess.dspTakenExecute(scheduleTaskDO, dspModelVO1)

        then:
        def exception = thrown(BizException)
        exception.getCode() == "09015201"
    }


    def "test dspTakenExecute "() {
        given:
        ScheduleTaskDO scheduleTaskDO = ScheduleTaskDO.builder().taskId(1L).scheduleId(1L).dspOrderId("1").round(1).subSku(SubSkuVO.builder().subSkuId(1).takenType(TakenType.ASSISTANT).dspType(DspType.DELAY_ASSIGN).subSkuName("111").build()).reward(new Money()).build()
        CheckModel checkModel1 = new CheckModel(new DspModelVO(), CheckCode.PASS)
        DspModelVO dspModelVO1 = new DspModelVO()
        dspModelVO1.setTransportGroup(new TransportGroupVO())
        dspModelVO1.setOrder(new DspOrderVO())
        DriverVO driverVO1 = new DriverVO()
        driverVO1.setSupplier(new SupplierVO())
        CarVO carVO = new CarVO()
        carVO.setCarId(1)
        driverVO1.setCar(carVO)
        dspModelVO1.setDriver(driverVO1)
        when(checkService.check(any() as TakenCheckCommand)).thenReturn(checkModel1)
        when(driverOrderFactory.createForDelay(any(), any())).thenReturn(new DriverOrderVO("driverOrderId", "dspOrderId", 1l))
        when(confirmDspOrderService.confirm(any() as ServiceProviderConfirmVO)).thenThrow(new OrderStatusException(ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR))
        when(queryVehicleService.query(1L, null)).thenReturn(new VehicleVO())

        when:
        def execute = systemDelayAssignProcess.dspTakenExecute(scheduleTaskDO, dspModelVO1)

        then:
        execute == true
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme