package com.ctrip.dcs.domain.schedule.process.impl

import com.ctrip.dcs.domain.common.constants.ConfigKey
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotTypeEnum
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.enums.ScheduleType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.GrabDspOrderSnapshotRecordService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryGuideServiceCityService
import com.ctrip.dcs.domain.common.service.QueryOrderSettlePriceService
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabDriverDO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabDriverRepository
import com.ctrip.dcs.domain.dsporder.repository.VBKDriverGrabOrderRepository
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.ScheduleDO
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway
import com.ctrip.dcs.domain.schedule.handler.GrabOrderPushRuleContext
import com.ctrip.dcs.domain.schedule.handler.GrabOrderPushRuleHandler
import com.ctrip.dcs.domain.schedule.repository.DspOrderRewardStrategyRepository
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.google.common.collect.Sets
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class GrabBroadcastProcessTest extends Specification {

    ScheduleDO schedule = Mock(ScheduleDO);

    ScheduleTaskDO scheduleTask = Mock(ScheduleTaskDO);

    SubSkuVO subSku = Mock(SubSkuVO);

    TransportGroupVO transportGroup = Mock(TransportGroupVO);

    DistributedLockService.DistributedLock lock = Mock(DistributedLockService.DistributedLock);

    DspOrderVO order = Mock(DspOrderVO);

    QueryTransportGroupService queryTransportGroupService = Mock(QueryTransportGroupService)

    GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository = Mock(GrabDspOrderSnapshotRepository)

    GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository = Mock(GrabDspOrderDriverIndexRepository)

    ScheduleRepository scheduleRepository = Mock(ScheduleRepository)

    DistributedLockService distributedLockService = Mock(DistributedLockService)

    MessageProviderService messageProviderService = Mock(MessageProviderService)

    QueryDriverService queryDriverService = Mock(QueryDriverService)

    QueryOrderSettlePriceService queryOrderSettlePriceService = Mock(QueryOrderSettlePriceService)

    DriverPointsGateway driverPointsGateway = Mock(DriverPointsGateway)

    ConfigService broadcastGrabConfig = Mock(ConfigService)

    private ConfigService broadcastSelectTimeConfig = Mock(ConfigService)

    VBKDriverGrabOrderRepository vbkDriverGrabOrderRepository = Mock(VBKDriverGrabOrderRepository)

    VBKDriverGrabDriverRepository vbkDriverGrabDriverRepository = Mock(VBKDriverGrabDriverRepository)

    private GrabDspOrderSnapshotRecordService grabDspOrderSnapshotRecordService = Mock(GrabDspOrderSnapshotRecordService)

    private DspOrderRewardStrategyRepository dspOrderRewardStrategyRepository = Mock(DspOrderRewardStrategyRepository)

    private QueryGuideServiceCityService queryGuideServiceCityService = Mock(QueryGuideServiceCityService)

    protected ScheduleTaskRepository taskRepository = Mock(ScheduleTaskRepository)

    GrabBroadcastProcess process = new GrabBroadcastProcess(
            "queryTransportGroupService": queryTransportGroupService,
            "queryDriverService": queryDriverService,
            "queryOrderSettlePriceService": queryOrderSettlePriceService,
            "driverPointsGateway": driverPointsGateway,
            "broadcastGrabConfig": broadcastGrabConfig,
            "grabDspOrderSnapshotRepository": grabDspOrderSnapshotRepository,
            "grabDspOrderDriverIndexRepository": grabDspOrderDriverIndexRepository,
            "messageProviderService": messageProviderService,
            "messageProducer": messageProviderService,
            "distributedLockService": distributedLockService,
            "scheduleRepository": scheduleRepository,
            "grabDspOrderSnapshotRecordService": grabDspOrderSnapshotRecordService,
            "vbkDriverGrabOrderRepository": vbkDriverGrabOrderRepository,
            "vbkDriverGrabDriverRepository": vbkDriverGrabDriverRepository,
            "broadcastSelectTimeConfig": broadcastSelectTimeConfig,
            "taskRepository": taskRepository,
            "dspOrderRewardStrategyRepository": dspOrderRewardStrategyRepository,
            "queryGuideServiceCityService": queryGuideServiceCityService,
    )

    def "Execute"() {
        given:
        order.getDspOrderId() >> "1"
        order.getSkuId() >> 1
        order.getUseDays() >> new UseDays(BigDecimal.ONE)
        schedule.getType() >> ScheduleType.SYSTEM
        scheduleTask.getSubSku() >> subSku
        subSku.getSubSkuId() >> 1
        subSku.getDspType() >> DspType.GRAB_BROADCAST
        subSku.getTakenType() >> TakenType.ASSISTANT
        order.getSettleToDriver() >> 1
        order.getOrderStatus() >> OrderStatusEnum.DISPATCH_CONFIRMED.getCode()
        order.getDspOrderId() >> "1"
        order.getCarTypeId() >> 117
        transportGroup.getSupplierId() >> 1L
        broadcastGrabConfig.getInteger(ConfigKey.BROADCAST_SELECT_TIMING_KEY, 10) >> 10
        order.getSettleToDriver() >> 1
        order.getDspOrderId() >> "1"
        order.getCarTypeId() >> 117
        transportGroup.getSupplierId() >> 1L
        driverPointsGateway.querySpecialTimePoint("1") >> BigDecimal.ZERO
        order.getSettleToDriver() >> 1
        order.getDspOrderId() >> "1"
        order.getCarTypeId() >> 117
        transportGroup.getSupplierId() >> 1L
        queryOrderSettlePriceService.queryOrderDriverSettlePriceListBeforeTaken("1", 117, Sets.newHashSet(1L), "") >> []
        order.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        transportGroup.getTransportGroupId() >> 1L
        queryDriverService.queryDriverIds([1L], ParentCategoryEnum.JNT) >> [1L]
        schedule.isVBKType() >> true
        schedule.getScheduleId() >> 1L
        order.getSupplierId() >> 1
        order.getSkuId() >> 1
        transportGroup.getSupplierId() >> 1L
        queryTransportGroupService.queryTransportGroups(1, 1, null) >> [transportGroup]
        distributedLockService.getLock("100041593_GRAB_BROADCAST_PROCESS_1") >> lock
        lock.tryLock() >> true
        scheduleTask.getScheduleId() >> 1L
        scheduleTask.getDspOrderId() >> "1"
        scheduleRepository.find(1L) >> schedule
        GrabOrderPushRuleContext.context.put(GrabDspOrderSnapshotTypeEnum.SYSTEM, new GrabOrderPushRuleHandler() {

            @Override
            Boolean handle(GrabDspOrderSnapshotDO snapshot, List<GrabDspOrderDriverIndexDO> indexes) {
                return true
            }

            @Override
            GrabDspOrderSnapshotTypeEnum type() {
                return GrabDspOrderSnapshotTypeEnum.SYSTEM
            }
        })
        when:
        def result = process.execute(scheduleTask, order)

        then:
        result == null
    }

    def "Execute detail"() {
        given:

        order.getSkuId() >> 1
        order.getUseDays() >> new UseDays(BigDecimal.ONE)
        order.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        schedule.getType() >> ScheduleType.SYSTEM
        scheduleTask.getSubSku() >> subSku
        subSku.getSubSkuId() >> 1
        subSku.getDspType() >> DspType.GRAB_BROADCAST
        subSku.getTakenType() >> TakenType.ASSISTANT
        GrabOrderPushRuleContext.context.put(GrabDspOrderSnapshotTypeEnum.SYSTEM, new GrabOrderPushRuleHandler() {

            @Override
            Boolean handle(GrabDspOrderSnapshotDO snapshot, List<GrabDspOrderDriverIndexDO> indexes) {
                return true
            }

            @Override
            GrabDspOrderSnapshotTypeEnum type() {
                return GrabDspOrderSnapshotTypeEnum.SYSTEM
            }
        })
        when:
        def result = process.execute(schedule, scheduleTask, order, [1L], BigDecimal.ONE, [], 10)

        then:
        result == null
    }

    def "QuerySelectTipsTime"() {

        given:
        order.getSettleToDriver() >> 1
        order.getDspOrderId() >> "1"
        order.getCarTypeId() >> 117
        order.getCityId() >> 117
        order.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        transportGroup.getSupplierId() >> 1L
        broadcastGrabConfig.getInteger(ConfigKey.BROADCAST_SELECT_TIMING_KEY, 10) >> 10
        broadcastSelectTimeConfig.getInteger(_, _) >> 10

        when:
        def result = process.querySelectTipsTime(order)

        then:
        result == 10
    }

    def "QuerySpecialTimePoint"() {

        given:
        order.getSettleToDriver() >> 1
        order.getDspOrderId() >> "1"
        order.getCarTypeId() >> 117
        order.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        transportGroup.getSupplierId() >> 1L
        driverPointsGateway.querySpecialTimePoint("1") >> BigDecimal.ZERO

        when:
        def result = process.querySpecialTimePoint(order)

        then:
        result == BigDecimal.ZERO
    }

    def "QueryOrderSettlePrice"() {

        given:
        order.getSettleToDriver() >> 1
        order.getDspOrderId() >> "1"
        order.getCarTypeId() >> 117
        order.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        transportGroup.getSupplierId() >> 1L
        scheduleTask.getSubSku() >> subSku
        subSku.getDspType() >> DspType.SYSTEM_BROADCAST
        subSku.getTakenType() >> TakenType.BROADCAST
        queryOrderSettlePriceService.queryOrderDriverSettlePriceListBeforeTaken("1", 117, CategoryCodeEnum.FROM_AIRPORT.getType(), Sets.newHashSet(1L), "") >> []

        when:
        def result = process.queryOrderSettlePrice(order, schedule, scheduleTask, [transportGroup])

        then:
        result == null
    }

    def "QueryDriverIds"() {

        given:
        order.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        transportGroup.getTransportGroupId() >> 1L
        queryDriverService.queryDriverIds([1L], ParentCategoryEnum.JNT) >> [1L]

        when:
        def result = process.queryDriverIds(order, schedule, [transportGroup])

        then:
        result == [1L]
    }

    def "queryDriverIdsFromVBKTask"() {

        given:
        order.getCategoryCode() >> CategoryCodeEnum.FROM_AIRPORT
        order.getDspOrderId() >> "1"
        order.getSupplierId() >> 1
        transportGroup.getTransportGroupId() >> 1L
        queryDriverService.queryDriverIds([1L], ParentCategoryEnum.JNT) >> [1L]
        vbkDriverGrabOrderRepository.queryBySupplierId("1", 1) >> new VBKDriverGrabOrderDO(vbkGrabTaskId: "1")
        vbkDriverGrabDriverRepository.queryDriverListByTaskId("1") >> [new VBKDriverGrabDriverDO(driverId: "1")]

        when:
        def result = process.queryDriverIdsFromVBKTask(order)

        then:
        result == [1L]
    }

    def "QueryTransportGroups vbk"() {

        given:
        schedule.isVBKType() >> true
        order.getSupplierId() >> 1
        order.getSkuId() >> 1
        transportGroup.getSupplierId() >> 1L
        queryTransportGroupService.queryTransportGroups(1, 1, null) >> [transportGroup]

        when:
        def result = process.queryTransportGroups(schedule, order)

        then:
        result.size() == 1
    }

    def "QueryTransportGroups system"() {

        given:
        schedule.isVBKType() >> false
        order.getSupplierId() >> 1
        order.getSkuId() >> 1
        transportGroup.getSupplierId() >> 1L
        transportGroup.getTransportGroupMode() >> TransportGroupMode.PART_TIME_BROADCAST
        queryTransportGroupService.queryTransportGroups(order) >> [transportGroup]

        when:
        def result = process.queryTransportGroups(schedule, order)

        then:
        result.size() == 1
    }

    def "QueryTransportGroups system111"() {

        given:
        schedule.isVBKType() >> true
        order.getSupplierId() >> 1
        order.getSkuId() >> 1
        transportGroup.getSupplierId() >> 1L
        transportGroup.getTransportGroupMode() >> TransportGroupMode.PART_TIME_BROADCAST
        queryTransportGroupService.queryTransportGroups(order) >> [transportGroup]

        when:
        def result = process.queryOrderSettleToSupplierPrice(order, schedule, scheduleTask)

        then:
        result.size() == 1
    }

    def "QueryOrderSettleToSupplierPrice"() {
        given:
        def order = Mock(DspOrderVO)
        def schedule = Mock(ScheduleDO)
        def task = Mock(ScheduleTaskDO)
        def rewardStrategy = Mock(DspOrderRewardStrategyDO)

        order.getSupplierId() >> 1
        order.getDspOrderId() >> "1"
        order.getSettleToDriver() >> 0
        schedule.getType() >> ScheduleType.VBK
        task.getDspRewardStrategyId() >> 1L
        dspOrderRewardStrategyRepository.find(1L) >> rewardStrategy
        rewardStrategy.getDspRewardAmount() >> BigDecimal.TEN

        when:
        def result = process.queryOrderSettleToSupplierPrice(order, schedule, task)

        then:
        result.size() == 1
        result[0].getPreRewardAmount() == BigDecimal.TEN
    }

    def "QueryOrderSettleToSupplierPrice VBK_SYSTEM"() {
        given:
        def order = Mock(DspOrderVO)
        def schedule = Mock(ScheduleDO)
        def task = Mock(ScheduleTaskDO)
        def rewardStrategy = Mock(DspOrderRewardStrategyDO)

        order.getSupplierId() >> 1
        order.getDspOrderId() >> "1"
        order.getSettleToDriver() >> 0
        schedule.getType() >> ScheduleType.VBK_SYSTEM
        task.getDspRewardStrategyId() >> 1L
        dspOrderRewardStrategyRepository.find(1L) >> rewardStrategy
        rewardStrategy.getDspRewardAmount() >> BigDecimal.TEN

        when:
        def result = process.queryOrderSettleToSupplierPrice(order, schedule, task)

        then:
        result.size() == 1
        result[0].getPreRewardAmount() == BigDecimal.TEN
    }
}
