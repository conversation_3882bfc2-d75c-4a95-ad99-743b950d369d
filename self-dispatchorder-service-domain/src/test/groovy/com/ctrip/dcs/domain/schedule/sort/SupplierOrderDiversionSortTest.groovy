package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.enums.DiversionMatchEnum
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverMileageProfitVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.SupplierOrderDiversionFeature
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SupplierDiversionVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class SupplierOrderDiversionSortTest extends Specification {
    @Mock
    SortModel model
    @Mock
    SortContext context
    @Mock
    DriverVO driver
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverMileageProfitVO dmp
    @Mock
    CarVO car
    @Mock
    TransportGroupVO transportGroup

    @InjectMocks
    SupplierOrderDiversionFeature feature = new SupplierOrderDiversionFeature()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(model.getModel()).thenReturn(dspModelVO)
        Mockito.when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        Mockito.when(dspModelVO.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getSupplierId()).thenReturn(1L)
        Mockito.when(context.getDspOrder()).thenReturn(dspOrderVO)
    }

    @Unroll
    def "test value"() {
        given:
        Map<AbstractMap.SimpleEntry<Integer, Integer>, SupplierDiversionVO> supplierOrderDiversionMap = new HashMap<>();
        supplierOrderDiversionMap.put(new AbstractMap.SimpleEntry<>(1, 1), new SupplierDiversionVO(1l, 1, BigDecimal.ONE, 1, DiversionMatchEnum.MATCH_ALL, Boolean.TRUE, true))
        supplierOrderDiversionMap.put(new AbstractMap.SimpleEntry<>(2, 1), new SupplierDiversionVO(2l, 1, BigDecimal.ONE, 1, DiversionMatchEnum.MATCH_ALL, Boolean.TRUE, true))
        Mockito.when(context.getSupplierOrderDiversionMap()).thenReturn(supplierOrderDiversionMap)

        Mockito.when(dspOrderVO.getRatePlanId()).thenReturn(1)

        when:
        Value res = feature.value(model, context)

        then:
        res.value != 2.2
    }

    @Unroll
    def "test value1"() {
        given:
        Map<AbstractMap.SimpleEntry<Integer, Integer>, SupplierDiversionVO> supplierOrderDiversionMap = new HashMap<>();
        supplierOrderDiversionMap.put(new AbstractMap.SimpleEntry<>(1, 1), new SupplierDiversionVO(1l, 1, BigDecimal.ONE, 1, DiversionMatchEnum.MATCH_ALL, Boolean.TRUE, true))
        supplierOrderDiversionMap.put(new AbstractMap.SimpleEntry<>(2, 1), new SupplierDiversionVO(2l, 1, BigDecimal.ONE, 1, DiversionMatchEnum.MATCH_ALL, Boolean.TRUE, true))
        Mockito.when(context.getSupplierOrderDiversionMap()).thenReturn(supplierOrderDiversionMap)
        Mockito.when(transportGroup.getSupplierId()).thenReturn(2L)

        when:
        Value res = feature.value(model, context)

        then:
        res.value != 2.2
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme