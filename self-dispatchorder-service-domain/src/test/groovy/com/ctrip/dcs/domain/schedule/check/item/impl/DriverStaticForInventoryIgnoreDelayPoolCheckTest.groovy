package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class DriverStaticForInventoryIgnoreDelayPoolCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @InjectMocks
    DriverStaticForInventoryIgnoreDelayPoolCheck driverStaticForInventoryIgnoreDelayPoolCheck = new DriverStaticForInventoryIgnoreDelayPoolCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "订单预估用车时间和预估时长的司机静态运力没有被占用"() {
        given:

        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(context.getDriverStaticInventoryIgnoreDelay()).thenReturn(Lists.newArrayList(1L))

        when:
        CheckCode result = driverStaticForInventoryIgnoreDelayPoolCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "订单预估用车时间和预估时长的司机静态运力被占用"() {
        given:

        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(context.getDriverStaticInventoryIgnoreDelay()).thenReturn(Lists.newArrayList(2L))

        when:
        CheckCode result = driverStaticForInventoryIgnoreDelayPoolCheck.check(checkModel, context)

        then:
        result == CheckCode.BOOK_TIME_CONFLICT_STATUS
    }

    def "test check"() {
        given:
        def context = Mock(CheckContext)
        context.getDriverStaticInventoryIgnoreDelay() >> getInventory()
        def checkModel = new CheckModel(model: new DspModelVO(new DspOrderVO(), new DriverVO(driverId: driverId)))

        when:
        CheckCode result = driverStaticForInventoryIgnoreDelayPoolCheck.check(checkModel, context)

        then:
        result == res

        where:
        driverId || res
        1L       || CheckCode.PASS
        4L       || CheckCode.BOOK_TIME_CONFLICT_STATUS
    }

    def getInventory() {
        return Arrays.asList(1L, 2L, 3L)
    }

    def "test downgrade"() {
        when:
        CheckCode result = driverStaticForInventoryIgnoreDelayPoolCheck.downgrade()

        then:
        result == CheckCode.BOOK_TIME_CONFLICT_STATUS
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme