package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DiversionMatchEnum
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.SupplierOrderDiversionGateway
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.value.SupplierDiversionVO
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class SupplierOrderDiversionVisitorTest extends Specification {
    @Mock
    SortContext sortContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    TransportGroupVO transportGroup
    @Mock
    SupplierOrderDiversionGateway supplierOrderDiversionGateway

    Map<AbstractMap.SimpleEntry<Integer, Integer>, SupplierDiversionVO> map = new HashMap<>()

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        SupplierOrderDiversionVisitor visitor = new SupplierOrderDiversionVisitor(dspOrder, [transportGroup], supplierOrderDiversionGateway)
        when(sortContext.getDspOrder()).thenReturn(dspOrder)
        when(dspOrder.getCategoryCode()).thenReturn(CategoryCodeEnum.ALL)
        when(dspOrder.getCityId()).thenReturn(0)
        when(dspOrder.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.DECEMBER, 18, 17, 48).getTime())
        when(supplierOrderDiversionGateway.query(anyInt(), anyString(), any(), any(), any(), anyInt())).thenReturn([new SupplierDiversionVO(1l, 1, BigDecimal.ONE, 1, DiversionMatchEnum.MATCH_ALL, Boolean.TRUE, true)])
        when(sortContext.getSupplierOrderDiversionMap()).thenReturn(map)
        when(transportGroup.getSupplierId()).thenReturn(1L)

        when:
        visitor.visit(sortContext)
        visitor.visit(sortContext)

        then:
        !map.isEmpty()
        map.get(new AbstractMap.SimpleEntry<Integer, Integer>(1, 0)) != null == true
        map.containsKey(new AbstractMap.SimpleEntry<Integer, Integer>(1, 0))
    }

    def "test visit 1"() {
        given:
        SupplierOrderDiversionVisitor visitor = new SupplierOrderDiversionVisitor(dspOrder, [transportGroup], supplierOrderDiversionGateway)
        when(sortContext.getDspOrder()).thenReturn(dspOrder)
        when(dspOrder.getCategoryCode()).thenReturn(CategoryCodeEnum.ALL)
        when(dspOrder.getCityId()).thenReturn(0)
        when(dspOrder.getRatePlanId()).thenReturn(1)
        when(dspOrder.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.DECEMBER, 18, 17, 48).getTime())
        when(supplierOrderDiversionGateway.query(anyInt(), anyString(), any(), any(), any(), anyInt())).thenReturn([new SupplierDiversionVO(1l, 1, BigDecimal.ONE, 1, DiversionMatchEnum.MATCH_ALL, Boolean.TRUE, true)])
        when(sortContext.getSupplierOrderDiversionMap()).thenReturn(map)
        when(transportGroup.getSupplierId()).thenReturn(1L)

        when:
        visitor.visit(sortContext)
//        visitor.visit(sortContext)

        then:
        !map.isEmpty()
        map.get(new AbstractMap.SimpleEntry<Integer, Integer>(1, 1)) != null == true
        map.containsKey(new AbstractMap.SimpleEntry<Integer, Integer>(1, 1))
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme