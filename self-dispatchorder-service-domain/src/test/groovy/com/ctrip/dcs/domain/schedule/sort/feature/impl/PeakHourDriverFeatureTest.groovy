package com.ctrip.dcs.domain.schedule.sort.feature.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.sort.feature.FeatureItemId
import com.ctrip.dcs.domain.schedule.sort.feature.Normalizer
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification
import spock.lang.Unroll

/**
 * PeakHourDriverFeature 单元测试
 */
class PeakHourDriverFeatureTest extends Specification {

    def testObj = new PeakHourDriverFeature()

    @Unroll
    def "测试 value 方法 - 司机是否为高峰时段司机"() {
        given: "设定相关方法入参"
        def model = Mock(SortModel)
        def context = Mock(SortContext)
        def driver = Mock(DriverVO)
        def dspOrder = Mock(DspOrderVO)
        def dspModel = Mock(DspModelVO)
        def dspContext = Mock(DspContext)
        def service = Mock(DspContextService)

        model.getModel() >> dspModel
        dspModel.getDriver() >> driver
        driver.getDriverTag() >> driverTag
        context.getDspOrder() >> dspOrder
        driver.isRegisterPartTimeDriver() >> isRegisterPartTimeDriver
        dspOrder.getCarId() >> 2
        context.getDspContext() >> dspContext
        dspContext.getService() >> service
        service.queryPeakHourDriverFeatureConfigValue(_) >> 5000.0

        when: "调用 value 方法"
        def result = testObj.value(model, context)

        then: "验证返回结果"
        result.getId() == FeatureItemId.F42.name()
        result.getValue() == expectedValue

        where: "测试不同司机标签场景"
        isRegisterPartTimeDriver | driverTag                   || expectedValue
        true                     | [peakHour: "1"]             || 5000.0
        false                    | [peakHour: "0"]             || 0.0
        true                     | [peakHour: "2"]             || 0.0
        true                     | [peakHour: ""]              || 0.0
        false                    | [peakHour: null]            || 0.0
        true                     | [peakHour: "1", other: "x"] || 5000.0
        true                     | [other: "x"]                || 0.0
        true                     | null                        || 0.0
        true                     | [:]                         || 0.0
        false                    | [:]                         || 0.0
    }

    def "测试 normalizer 方法"() {
        when: "调用 normalizer 方法"
        def result = testObj.normalizer()

        then: "验证返回结果"
        result == Normalizer.EMPTY
    }

    def "测试 load 方法"() {
        given: "设定相关方法入参"
        def models = [Mock(SortModel), Mock(SortModel)]
        def context = Mock(SortContext)

        when: "调用 load 方法"
        testObj.load(models, context)

        then: "验证方法正常执行，无异常抛出"
        noExceptionThrown()
    }
}
