package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.value.SeriesVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.CarSeriesGateway
import com.ctrip.dcs.domain.schedule.gateway.ManualSubSkuConfigGateway
import com.google.common.collect.Sets
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class ManualSubSkuConfigVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO order
    @Mock
    ManualSubSkuConfigGateway manualSubSkuConfigGateway
    @InjectMocks
    ManualSubSkuConfigVisitor manualSubSkuConfigVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(checkContext.getManualSubSkuConfigMap()).thenReturn(["111":[1,2]])
        when(order.getDspOrderId()).thenReturn("111")
        when(manualSubSkuConfigGateway.queryManualSubSkuList()).thenReturn([1,2,3])

        when:
        manualSubSkuConfigVisitor.visit(checkContext)

        then:
        checkContext.getManualSubSkuConfigMap().isEmpty() == false
    }

    def "test visit 2"() {
        given:
        when(checkContext.getManualSubSkuConfigMap()).thenReturn(["111":[1,2]])
        when(order.getDspOrderId()).thenReturn("112")
        when(manualSubSkuConfigGateway.queryManualSubSkuList()).thenReturn([1,2,3])

        when:
        manualSubSkuConfigVisitor.visit(checkContext)

        then:
        checkContext.getManualSubSkuConfigMap().isEmpty() == false
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme