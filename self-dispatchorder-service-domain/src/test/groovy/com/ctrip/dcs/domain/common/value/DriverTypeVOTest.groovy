package com.ctrip.dcs.domain.common.value


import spock.lang.Specification
import spock.lang.Unroll

class DriverTypeVOTest extends Specification {

    @Unroll
    def "test grab Work Time"() {
        given:
        EstimatedUseTimeVO estimatedUseTime = Mock()
        PeriodsVO workTimes = Mock()
        estimatedUseTime.in(workTimes) >> inWorkTimes
        PeriodsVO bookTimeHour = Mock()
        estimatedUseTime.in(bookTimeHour) >> inBookTimeHour
        when:
        boolean result = driverTypeVO.grabWorkTime(estimatedUseTime, workTimes, bookTimeHour)

        then:
        result == r

        where:
        driverTypeVO           | inWorkTimes | inBookTimeHour || r
        DriverTypeVO.FULL_TIME | true        | false          || false
        DriverTypeVO.FULL_TIME | true        | true           || false
        DriverTypeVO.FULL_TIME | false       | true           || true
        DriverTypeVO.FULL_TIME | false       | false          || false
        DriverTypeVO.PART_TIME | false       | false          || false
    }
}

//Generated with love by <PERSON>Me :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme