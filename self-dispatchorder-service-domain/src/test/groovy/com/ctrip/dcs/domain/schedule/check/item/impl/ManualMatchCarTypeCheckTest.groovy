package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.dcs.domain.schedule.visitor.Visitor
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ManualMatchCarTypeCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    CheckContext checkContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    CarTypeLevelRelationsVO relations
    @Mock
    CarVO car
    @InjectMocks
    ManualMatchCarTypeCheck manualMatchCarTypeCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test load"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(dspOrder.isDesignatedVehicleOrder()).thenReturn(true)

        when:
        manualMatchCarTypeCheck.load([checkModel], checkContext)

        then:
        verify(checkContext, never()).accept(any() as Visitor)
    }

    def "test check"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.carTypeRelations(any())).thenReturn(relations)
        when(checkModel.getModel()).thenReturn(dspModel)
        when(dspModel.getOrder()).thenReturn(dspOrder)
        when(dspModel.getCar()).thenReturn(car)
        when(car.getCarTypeId()).thenReturn(carId)
        when(dspOrder.isDesignatedVehicleOrder()).thenReturn(isDesignatedVehicleOrder)
        when(relations.otherCarTypeRelation(any())).thenReturn(relation)

        when:
        CheckCode result = manualMatchCarTypeCheck.check(checkModel, checkContext)

        then:
        result == code

        where:
        isDesignatedVehicleOrder | carId | relation                      || code
        true                     | null  | null                          || CheckCode.PASS
        false                    | null  | null                          || CheckCode.PASS
        false                    | 117L  | CarTypeLevelRelation.BELOW    || CheckCode.PASS
        false                    | 117L  | CarTypeLevelRelation.PARALLEL || CheckCode.PASS
        false                    | 117L  | CarTypeLevelRelation.HIGHER   || CheckCode.DRV_CAR_TYPE_NOT_MATCH
    }

    def "test downgrade"() {
        when:
        CheckCode result = manualMatchCarTypeCheck.downgrade()

        then:
        result == CheckCode.DRV_CAR_TYPE_NOT_MATCH
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme