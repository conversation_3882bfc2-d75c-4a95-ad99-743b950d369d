package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.value.OrderPriorityType
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class HighPriorityDriverCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    DriverMileageProfitVO driverMileageProfitVO
    @InjectMocks
    HighPriorityDriverCheck highPriorityDriverCheck = new HighPriorityDriverCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "头部司机满足接单"() {
        given:
        Map<Long, DriverMileageProfitVO> map = new HashMap<>()
        map.put(1L, driverMileageProfitVO)
        Mockito.when(context.getOrderPriorityType(Mockito.any())).thenReturn(OrderPriorityType.MEDIUM)
        Mockito.when(context.getHighPriorityDriverMileageProfitMap()).thenReturn(map)
        Mockito.when(context.getHighProfitLineDay(Mockito.any(), Mockito.any())).thenReturn(600)
        Mockito.when(driverMileageProfitVO.getProfit()).thenReturn(400D)

        when:
        CheckCode result = highPriorityDriverCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "头部司机不满足接单"() {
        given:
        Map<Long, DriverMileageProfitVO> map = new HashMap<>()
        map.put(1L, driverMileageProfitVO)
        Mockito.when(context.getOrderPriorityType(Mockito.any())).thenReturn(OrderPriorityType.HIGH)
        Mockito.when(context.getHighPriorityDriverMileageProfitMap()).thenReturn(map)
        Mockito.when(context.getHighProfitLineDay(Mockito.any(), Mockito.any())).thenReturn(600)
        Mockito.when(driverMileageProfitVO.getProfit()).thenReturn(700D)

        when:
        CheckCode result = highPriorityDriverCheck.check(checkModel, context)

        then:
        result == CheckCode.OVER_DRIVER_PROFIT
    }

    def "test check"() {
        def context = Mock(CheckContext)
        context.getOrderPriorityType(_) >> orderPrioType
        context.getHighPriorityDriverMileageProfitMap() >> resMap
        context.getHighProfitLineDay(_, _) >> 10
        def mockOrder = new DspOrderVO(estimatedUseTime: DateUtil.parseDateStr2Date("2023-04-19 10:00:00"),
                predicServiceStopTime: DateUtil.parseDateStr2Date("2023-04-19 16:00:00"))
        def mockDriver = new DriverVO(driverId: driverId, cityId: 1, car: new CarVO(carTypeId: 117),
                transportGroups: Arrays.asList(new TransportGroupVO(transportGroupMode: TransportGroupMode.REGISTER_DISPATCH)))
        def checkModel = new CheckModel(model: new DspModelVO(mockOrder, mockDriver))

        when:
        CheckCode result = highPriorityDriverCheck.check(checkModel, context)
        then:
        result == checkRes

        where:
        orderPrioType            | resMap            | driverId || checkRes
        OrderPriorityType.LOW    | null              | null     || CheckCode.LOW_PRIORITY_ORDER
        OrderPriorityType.HIGH   | null              | 1L       || CheckCode.NO_HIGH_PRIORITY_DRIVER
        OrderPriorityType.HIGH   | Maps.newHashMap() | 1L       || CheckCode.NO_HIGH_PRIORITY_DRIVER
        OrderPriorityType.HIGH   | getMap()          | 1L       || CheckCode.OVER_DRIVER_PROFIT
        OrderPriorityType.HIGH   | getMap()          | 2L       || CheckCode.PASS
        OrderPriorityType.MEDIUM | getMap()          | 3L       || CheckCode.MEDIUM_PRIORITY_COUNT_OVER

    }

    def getMap() {
        Map<Long, DriverMileageProfitVO> map = Maps.newHashMap();
        map.put(1L, DriverMileageProfitVO.builder()
                .driverId(1L)
                .highPriorityOrderCounts(3)
                .mediumPriorityOrderCounts(3)
                .emptyMileage(0)
                .income(0)
                .orderCounts(0)
                .orderMileage(0)
                .profit(11d)
                .build())
        map.put(2L, DriverMileageProfitVO.builder()
                .driverId(2L)
                .highPriorityOrderCounts(0)
                .mediumPriorityOrderCounts(4)
                .emptyMileage(0)
                .income(0)
                .orderCounts(0)
                .orderMileage(0)
                .profit(9d)
                .build())
        map.put(3L, DriverMileageProfitVO.builder()
                .driverId(3L)
                .highPriorityOrderCounts(0)
                .mediumPriorityOrderCounts(3)
                .emptyMileage(0)
                .income(0)
                .orderCounts(0)
                .orderMileage(0)
                .profit(9d)
                .build())
        return map
    }

    def "test downgrade"() {
        when:
        CheckCode result = highPriorityDriverCheck.downgrade()

        then:
        result == CheckCode.PASS
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme