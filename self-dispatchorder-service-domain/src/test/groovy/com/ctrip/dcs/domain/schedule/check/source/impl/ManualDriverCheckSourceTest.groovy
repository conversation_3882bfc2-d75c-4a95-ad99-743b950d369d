package com.ctrip.dcs.domain.schedule.check.source.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import java.util.function.BiFunction
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ManualDriverCheckSourceTest extends Specification {

    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspContextService dspContextService
    @Mock
    TransportGroupVO transportGroup
    @Mock
    DriverVO driver
    @Mock
    DspOrderVO dspOrder

    @InjectMocks
    ManualDriverCheckSource manualDriverCheckSource

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test init"() {
        when:
        manualDriverCheckSource.init(new CheckContext())

        then:
        BizException e = thrown(BizException)
        e.getCode() == ErrorCode.BUILD_CHECK_SOURCE_ERROR.getCode()
    }

    def "test taken"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(checkContext.getDriver()).thenReturn(driver)
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(driver.getTransportGroups()).thenReturn([transportGroup])
        when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(transportGroup.getSupplierId()).thenReturn(1L)
        when(dspOrder.getSupplierId()).thenReturn(1)
        when(transportGroup.getTransportGroupId()).thenReturn(1L)
        when(dspContextService.queryTransports(any())).thenReturn([]).thenReturn([transportGroup])

        when:
        CheckModel result1 = manualDriverCheckSource.taken(checkContext)
        CheckModel result2 = manualDriverCheckSource.taken(checkContext)

        then:
        result1.getCheckCode() == CheckCode.NULL_TRANSPORT_GROUP
        result2.getCheckCode() == CheckCode.PASS
    }

    def "test get Transport Group"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(checkContext.getDriver()).thenReturn(driver)
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(driver.getTransportGroups()).thenReturn([transportGroup])
        when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(transportGroup.getSupplierId()).thenReturn(1L)
        when(dspOrder.getSupplierId()).thenReturn(1)
        when(transportGroup.getTransportGroupId()).thenReturn(1L)
        when(dspContextService.queryTransports(any())).thenReturn([]).thenReturn([transportGroup])

        when:
        TransportGroupVO result1 = manualDriverCheckSource.getTransportGroup(checkContext)
        TransportGroupVO result2 = manualDriverCheckSource.getTransportGroup(checkContext)

        then:
        result1 == null
        result2.getTransportGroupId() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme