package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.QueryOrderTakenCostGateway
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.value.TakenCostVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverTakenCostsVisitorTest extends Specification {
    @Mock
    SortContext sortContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driverVO
    @Mock
    QueryOrderTakenCostGateway queryOrderTakenCostGateway
    @InjectMocks
    DriverTakenCostsVisitor driverTakenCostsVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(sortContext.getDriverTakenCostMap()).thenReturn([1L:new TakenCostVO()])
        when(dspOrder.getDspOrderId()).thenReturn("getDspOrderIdResponse")
        when(queryOrderTakenCostGateway.queryOrderTakenCost(anyString(), any())).thenReturn([new TakenCostVO(drvId: 0)])
        DriverTakenCostsVisitor driverTakenCostsVisitor = new DriverTakenCostsVisitor(dspOrder, [driverVO], queryOrderTakenCostGateway)

        when:
        driverTakenCostsVisitor.visit(sortContext)

        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme