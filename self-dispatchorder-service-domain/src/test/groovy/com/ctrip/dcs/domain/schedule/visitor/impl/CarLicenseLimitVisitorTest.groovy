package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.TrafficControlGateway
import com.ctrip.dcs.domain.schedule.sort.SortContext
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class CarLicenseLimitVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    SortContext sortContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO order
    @Mock
    DriverVO driverVO
    @Mock
    CarVO carVO
    @Mock
    TrafficControlGateway trafficControlGateway
    @InjectMocks
    CarLicenseLimitVisitor carLicenseLimitVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(driverVO.getCar()).thenReturn(carVO)
        when(trafficControlGateway.queryDriverTrafficControl(any(),any(), any())).thenReturn(["String"])
        CarLicenseLimitVisitor carLicenseLimitVisitor = new CarLicenseLimitVisitor(order, [driverVO], trafficControlGateway)

        when:
        carLicenseLimitVisitor.visit(checkContext)

        then:
        def  res = carLicenseLimitVisitor.visit(checkContext)

        then:
        res == null//todo - validate something
    }

    def "test visit1"() {
        given:
        when(driverVO.getCar()).thenReturn(carVO)
        when(trafficControlGateway.queryDriverTrafficControl(any(),any(), any())).thenReturn(["String"])
        CarLicenseLimitVisitor carLicenseLimitVisitor = new CarLicenseLimitVisitor(order, [driverVO], trafficControlGateway)

        when:
        carLicenseLimitVisitor.visit(checkContext)

        then:
        def  res = carLicenseLimitVisitor.visit(checkContext)

        then:
        res == null//todo - validate something
    }
    def "test visit3"() {
        given:
        when(driverVO.getCar()).thenReturn(carVO)
        when(trafficControlGateway.queryDriverTrafficControl(any(),any(), any())).thenReturn(["String"])
        DspOrderVO order1 = new DspOrderVO();
        order1.setFromCityId(1)
        order1.setToCityId(2)
        CarLicenseLimitVisitor carLicenseLimitVisitor = new CarLicenseLimitVisitor(order1, [driverVO], trafficControlGateway)

        when:
        carLicenseLimitVisitor.visit(checkContext)

        then:
        def  res = carLicenseLimitVisitor.visit(checkContext)

        then:
        res == null//todo - validate something
    }
    def "test visit4"() {
        given:
        when(driverVO.getCar()).thenReturn(carVO)
        when(trafficControlGateway.queryDriverTrafficControl(any(),any(), any())).thenReturn(["String"])
        DspOrderVO order1 = new DspOrderVO();
        order1.setFromCityId(null)
        order1.setToCityId(2)
        order1.setCarTypeId(1)
        CarLicenseLimitVisitor carLicenseLimitVisitor = new CarLicenseLimitVisitor(order1, [driverVO], trafficControlGateway)

        when:
        carLicenseLimitVisitor.visit(checkContext)

        then:
        def  res = carLicenseLimitVisitor.visit(checkContext)

        then:
        res == null//todo - validate something
    }

    def "test visit5"() {
        given:
        when(driverVO.getCar()).thenReturn(carVO)
        when(trafficControlGateway.queryDriverTrafficControl(any(),any(), any())).thenReturn(["String"])
        DspOrderVO order1 = new DspOrderVO();
        order1.setFromCityId(1)
        order1.setToCityId(null)
        order1.setCarTypeId(1)
        CarLicenseLimitVisitor carLicenseLimitVisitor = new CarLicenseLimitVisitor(order1, [driverVO], trafficControlGateway)

        when:
        carLicenseLimitVisitor.visit(checkContext)

        then:
        def  res = carLicenseLimitVisitor.visit(checkContext)

        then:
        res == null//todo - validate something
    }

    def "test visit 2"() {
        given:
        when(checkContext.getLimitLicensePlateNumberList()).thenReturn(["abc"])
        when(driverVO.getCar()).thenReturn(carVO)
        when(carVO.getCarLicense()).thenReturn("abc")
        when(trafficControlGateway.queryDriverTrafficControl(any(),any(), any())).thenReturn(["String"])
        CarLicenseLimitVisitor carLicenseLimitVisitor = new CarLicenseLimitVisitor(order, [driverVO], trafficControlGateway)

        when:
        def  res = carLicenseLimitVisitor.visit(checkContext)

        then:
        res == null//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme