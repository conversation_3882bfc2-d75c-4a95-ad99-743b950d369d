package com.ctrip.dcs.domain.orderPriority.service

import com.ctrip.dcs.domain.common.value.DspOrderFeeQuantileVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderFeeDO
import com.ctrip.dcs.domain.dsporder.repository.DspOrderFeeRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.orderPriority.entity.OrderFeeQuantileDO
import com.ctrip.dcs.domain.orderPriority.repository.OrderPriorityRepository
import com.ctrip.dcs.domain.schedule.check.value.OrderPriorityType
import org.mockito.InjectMocks
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class OrderFeePriorityServiceImplTest extends Specification {


    DspOrderRepository dspOrderRepository = Mock()

    DspOrderFeeRepository dspOrderFeeRepository = Mock()

    OrderPriorityRepository orderPriorityRepository = Mock()
    @InjectMocks
    OrderFeePriorityServiceImpl orderFeePriorityServiceImpl = new OrderFeePriorityServiceImpl
            (dspOrderRepository: dspOrderRepository,
                    dspOrderFeeRepository: dspOrderFeeRepository,
                    orderPriorityRepository: orderPriorityRepository)

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test deal Order Fee Priority"() {
        given:
        dspOrderRepository.queryYesterdayOrders() >> orderList
        dspOrderFeeRepository.find(_) >> orderFee
        new DspOrderFeeDO(costAmount: 100 as BigDecimal)

        when:
        orderFeePriorityServiceImpl.dealOrderFeePriority(0.8d, 0.5d)

        then:
        res * orderPriorityRepository.save(_)

        where:
        orderList      | orderFee       || res
        null           | null           || 0
        getOrderList() | getOrderFee(1) || 1


    }

    def getOrderList() {
        return [new DspOrderDO(dspOrderId: "12345", cityId: 1, vehicleGroupId: 117),
                new DspOrderDO(dspOrderId: "12345", cityId: 2, vehicleGroupId: 118),
                new DspOrderDO(dspOrderId: "12345", cityId: 2, vehicleGroupId: 118),
                new DspOrderDO(dspOrderId: "12345", cityId: 2, vehicleGroupId: 119),
                new DspOrderDO(dspOrderId: "12345", cityId: 2, vehicleGroupId: 120),
                new DspOrderDO(dspOrderId: "12345", cityId: 3, vehicleGroupId: 117)]
    }

    def getOrderFee(rate) {
        return new DspOrderFeeDO(costAmount: 10 * rate as BigDecimal)
    }


    def "test query Order Fee Quantile"() {
        given:
        orderPriorityRepository.find(_) >> list


        when:
        List<DspOrderFeeQuantileVO> result = orderFeePriorityServiceImpl.queryOrderFeeQuantile(1)

        then:
        result.size() == queryRes


        where:
        list                   || queryRes
        []                     || 0
        OrderFeeQuantileList() || 1
    }

    def OrderFeeQuantileList() {
        return [new OrderFeeQuantileDO(cityId: 0, carTypeId: 0, high: 0 as BigDecimal, medium: 0 as BigDecimal)]
    }


    def "test query Order Priority Type"() {
        given:
        orderPriorityRepository.find(_) >> orderFeeQuantileList

        when:
        OrderPriorityType result = orderFeePriorityServiceImpl.queryOrderPriorityType(order)

        then:
        result == res

        where:
        order                                                      | orderFeeQuantileList || res
        new DspOrderVO(cityId: 1, costAmount: 100, carTypeId: 118) | null                 || OrderPriorityType.HIGH
        new DspOrderVO(cityId: 1, costAmount: 100, carTypeId: 118) | getList(1)            || OrderPriorityType.HIGH
        new DspOrderVO(cityId: 2, costAmount: 100, carTypeId: 118) | getList(2)            || OrderPriorityType.MEDIUM
        new DspOrderVO(cityId: 3, costAmount: 100, carTypeId: 118) | getList(3)            || OrderPriorityType.LOW


    }

    def getList(cityId) {
        if(cityId ==1){
            return [new OrderFeeQuantileDO(cityId: 1, carTypeId: 118, high: 50 as BigDecimal, medium: 0 as BigDecimal)]
        }
        if(cityId == 2){
            return [new OrderFeeQuantileDO(cityId: 2, carTypeId: 118, high: 200 as BigDecimal, medium: 50 as BigDecimal)]
        }
        if(cityId == 3){
            return [new OrderFeeQuantileDO(cityId: 3, carTypeId: 118, high: 2000 as BigDecimal, medium: 2000 as BigDecimal)]
        }
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme