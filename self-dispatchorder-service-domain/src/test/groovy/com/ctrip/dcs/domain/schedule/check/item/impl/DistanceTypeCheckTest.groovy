package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification

class DistanceTypeCheckTest extends Specification {

    DistanceTypeCheck instance = new DistanceTypeCheck()

    CheckModel checkModel = Mock()
    DspModelVO dispatchModel = Mock()
    DspOrderVO dispatchOrder = Mock()
    DriverVO driverModel = Mock()
    CheckContext checkContext = Mock()
    DspContext dispatchContext = Mock()

    def "test load"() {
        given:
        checkContext.getContext() >> dispatchContext
        checkModel.getModel() >> dispatchModel
        dispatchModel.getOrder() >> dispatchOrder
        dispatchModel.getDriver() >> driverModel
        driverModel.getDriverId() >> driverId
        dispatchOrder.getShortDisOrder() >> orderDistanceType
        dispatchContext.getDriverDistanceTypeMap() >> Map.of(driverId, driverDistanceType)
        when:
        CheckCode actual = instance.check(checkModel, checkContext)
        then:
        actual == expected
        where:
        driverId | orderDistanceType | driverDistanceType | expected
        99L      | -1                | -1                 | CheckCode.PASS
        99L      | 1                 | -1                 | CheckCode.DISTANCE_TYPE_MISMATCHED
        99L      | -1                | 1                  | CheckCode.DISTANCE_TYPE_MISMATCHED
        99L      | 0                 | 0                  | CheckCode.PASS
        99L      | 0                 | 1                  | CheckCode.DISTANCE_TYPE_MISMATCHED
        99L      | 1                 | 0                  | CheckCode.DISTANCE_TYPE_MISMATCHED
        99L      | 1                 | 1                  | CheckCode.PASS
    }

}