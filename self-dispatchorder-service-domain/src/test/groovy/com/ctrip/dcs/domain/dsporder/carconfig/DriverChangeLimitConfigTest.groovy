package com.ctrip.dcs.domain.dsporder.carconfig

import spock.lang.Specification
import spock.lang.Unroll

class DriverChangeLimitConfigTest extends Specification {

    DriverChangeLimitConfig config

    def setup() {
        config = new DriverChangeLimitConfig()

        // 使用反射为私有字段赋值
        def contextField = DriverChangeLimitConfig.class.getDeclaredField("context")
        contextField.accessible = true

        // 准备测试数据
        def testContext = [
                "100_200_in": "2",
                "100_200_out": "3",
                "101_201_in": "5",
                "102_202_out": "7",
                "invalid_value_in": "abc"
        ]
        contextField.set(config, testContext)
    }

    def "getDriverChangeInLimit returns correct value when key exists"() {
        when:
        def result = config.getDriverChangeInLimit(100, 200)

        then:
        result == 2
    }

    def "getDriverChangeInLimit returns 0 when key does not exist"() {
        when:
        def result = config.getDriverChangeInLimit(999, 999)

        then:
        result == 0
    }

    def "getDriverChangeOutLimit returns correct value when key exists"() {
        when:
        def result = config.getDriverChangeOutLimit(100, 200)

        then:
        result == 3
    }

    def "getDriverChangeOutLimit returns 0 when key does not exist"() {
        when:
        def result = config.getDriverChangeOutLimit(999, 999)

        then:
        result == 0
    }

    @Unroll
    def "getDriverChangeInLimit with cityId=#cityId and supplierId=#supplierId returns #expected"() {
        when:
        def result = config.getDriverChangeInLimit(cityId, supplierId)

        then:
        result == expected

        where:
        cityId | supplierId | expected
        100    | 200        | 2
        101    | 201        | 5
        102    | 202        | 0  // 不存在 in 限制
        null   | 200        | 0  // 空值测试
        100    | null       | 0  // 空值测试
    }

    @Unroll
    def "getDriverChangeOutLimit with cityId=#cityId and supplierId=#supplierId returns #expected"() {
        when:
        def result = config.getDriverChangeOutLimit(cityId, supplierId)

        then:
        result == expected

        where:
        cityId | supplierId | expected
        100    | 200        | 3
        101    | 201        | 0  // 不存在 out 限制
        102    | 202        | 7
        null   | 200        | 0  // 空值测试
        100    | null       | 0  // 空值测试
    }

    def "toKey generates correct format"() {
        when:
        // 通过反射调用私有方法
        def method = DriverChangeLimitConfig.class.getDeclaredMethod("toKey", Integer.class, Integer.class, String.class)
        method.accessible = true
        def result = method.invoke(config, cityId, supplierId, type)

        then:
        result == expected

        where:
        cityId | supplierId | type   | expected
        100    | 200        | "in"   | "100_200_in"
        101    | 201        | "out"  | "101_201_out"
        0      | 0          | "test" | "0_0_test"
        null   | 200        | "in"   | "null_200_in"  // 处理 null 值
    }

    def "context is correctly initialized"() {
        expect:
        config.context != null
        config.context.size() == 5
        config.context.containsKey("100_200_in")
        config.context.containsKey("100_200_out")
    }
}
