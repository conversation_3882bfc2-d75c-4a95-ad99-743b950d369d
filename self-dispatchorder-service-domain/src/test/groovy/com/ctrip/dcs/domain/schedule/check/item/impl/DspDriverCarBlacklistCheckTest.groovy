package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.carconfig.DspDriverCarBlacklistValueVO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DspDriverCarBlacklistCheckTest extends Specification {

    DspDriverCarBlacklistCheck check = new DspDriverCarBlacklistCheck()


    def "Check"() {
        DspOrderVO dspOrder = Mock(DspOrderVO)
        DriverVO driver = Mock(DriverVO)
        DspModelVO dspModel = Mock(DspModelVO)
        CheckModel checkModel = Mock(CheckModel)
        CheckContext checkContext = Mock(CheckContext)

        checkModel.getModel() >> dspModel
        dspModel.getOrder() >> dspOrder
        checkContext.getDspOrder() >> dspOrder
        dspOrder.getEstimatedUseTime() >> DateUtil.parseDateStr2Date(estimatedUseTime)
        dspModel.getDriver() >> driver
        driver.getDriverId() >> 1L
        driver.getCar() >> new CarVO(carLicense: "carLicense")
        checkContext.getCarConfig(_, _) >> [new DspDriverCarBlacklistValueVO("2025-05-20 18:00:00", "2025-05-31 18:00:00")]


        when: "执行方法"
        CheckCode result = check.check(checkModel, checkContext)

        then: "验证方法返回值"
        code == result

        where:
        estimatedUseTime      || code
        "2025-05-20 18:00:00" || CheckCode.DSP_DRIVER_CAR_BLACKLIST_LIMIT
        "2025-05-31 18:00:00" || CheckCode.DSP_DRIVER_CAR_BLACKLIST_LIMIT
        "2025-05-21 18:00:00" || CheckCode.DSP_DRIVER_CAR_BLACKLIST_LIMIT
        "2025-05-18 18:00:00" || CheckCode.PASS
    }
}
