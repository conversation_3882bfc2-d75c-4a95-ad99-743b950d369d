package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverOrderTimeConflictVisitorTest extends Specification {
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driver
    @Mock
    ConflictGateway conflictGateway
    @InjectMocks
    DriverOrderTimeConflictVisitor driverOrderTimeConflictVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(conflictGateway.checkDriverOrderTimeConflict(any(), any())).thenReturn([1L])
        when(driver.getDriverId()).thenReturn(1L)
        CheckContext checkContext = new CheckContext()
        DriverOrderTimeConflictVisitor visitor1 = new DriverOrderTimeConflictVisitor(dspOrder: null, drivers: [], conflictGateway: conflictGateway)
        DriverOrderTimeConflictVisitor visitor2 = new DriverOrderTimeConflictVisitor(dspOrder: dspOrder, drivers: [], conflictGateway: conflictGateway)
        DriverOrderTimeConflictVisitor visitor3 = new DriverOrderTimeConflictVisitor(dspOrder: dspOrder, drivers: [driver], conflictGateway: conflictGateway)

        when:
        visitor1.visit(checkContext)
        int s1 = checkContext.getTimeConflictDriverIds().size()
        visitor2.visit(checkContext)
        int s2 = checkContext.getTimeConflictDriverIds().size()
        visitor3.visit(checkContext)
        int s3 = checkContext.getTimeConflictDriverIds().size()

        then:
        s1 == 0
        s2 == 0
        s3 == 1
    }

    def "test visit 2"() {
        given:
        CheckContext checkContext = new CheckContext()
        when(driver.getDriverId()).thenReturn(1L)
        checkContext.getTimeConflictDriverIds().add(1L)
        DriverOrderTimeConflictVisitor visitor = new DriverOrderTimeConflictVisitor(dspOrder: dspOrder, drivers: [driver], conflictGateway: conflictGateway)
        when:
        visitor.visit(checkContext)
        then:
        checkContext.getTimeConflictDriverIds().size() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme