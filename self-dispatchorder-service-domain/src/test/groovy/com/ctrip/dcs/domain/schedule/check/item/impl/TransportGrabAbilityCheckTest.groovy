package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification
import spock.lang.Unroll

class TransportGrabAbilityCheckTest extends Specification {

    TransportGrabAbilityCheck transportGrabAbilityCheck = new TransportGrabAbilityCheck()

    @Unroll
    def "test check"() {
        when:
        CheckCode result = transportGrabAbilityCheck.check(checkModel, Mock(CheckContext))

        then:
        result == r

        where:
        checkModel                                                                          || r
        new CheckModel(model: new DspModelVO(driver: new DriverVO(broadcast: YesOrNo.YES))) || CheckCode.PASS
        new CheckModel(model: new DspModelVO(driver: new DriverVO(broadcast: YesOrNo.NO)))  || CheckCode.GRAB_SWITCH
    }

    def "test downgrade"() {
        when:
        CheckCode result = transportGrabAbilityCheck.downgrade()

        then:
        result == CheckCode.GRAB_SWITCH
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme