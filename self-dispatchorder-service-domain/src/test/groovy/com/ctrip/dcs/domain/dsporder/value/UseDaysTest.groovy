package com.ctrip.dcs.domain.dsporder.value

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import spock.lang.Specification
import spock.lang.Unroll

class UseDaysTest extends Specification {

    @Unroll
    def "test days"() {
        given:
        def useDays = new UseDays(days)

        when:
        Integer result = useDays.days(categoryCodeEnum)

        then:
        result == r

        where:
        days                   | categoryCodeEnum              || r
        BigDecimal.TEN         | CategoryCodeEnum.ALL          || 1
        BigDecimal.ZERO        | CategoryCodeEnum.ALL          || 1
        BigDecimal.TEN         | CategoryCodeEnum.DAY_RENTAL   || 10
        BigDecimal.TEN         | CategoryCodeEnum.C_DAY_RENTAL || 10
        new BigDecimal("10.5") | CategoryCodeEnum.C_DAY_RENTAL || 10
    }
}

//Generated with love by <PERSON>Me :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme