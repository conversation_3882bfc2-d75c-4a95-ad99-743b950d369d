package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.service.QueryDriverLocationService
import com.ctrip.dcs.domain.common.value.DriverLocationVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverLocationVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    SortContext sortContext
    @Mock
    DspContext dspContext
    @Mock
    DriverVO driverVO
    @Mock
    QueryDriverLocationService queryDriverLocationService
    @InjectMocks
    DriverLocationVisitor driverLocationVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(queryDriverLocationService.queryDriverLocation(any())).thenReturn([new DriverLocationVO(1l, [new DriverLocationVO.LocationVO(1l, 0 as BigDecimal, 0 as BigDecimal, "coordSys", 0d, 0d, 0d, 0d, 1l)])])
        DriverLocationVisitor driverLocationVisitor = new DriverLocationVisitor([driverVO], queryDriverLocationService)

        when:
        driverLocationVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme