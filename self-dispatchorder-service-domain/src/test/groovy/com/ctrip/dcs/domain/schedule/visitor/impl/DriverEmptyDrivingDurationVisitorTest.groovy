package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.QueryDriverLocationService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverLocationVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.dsporder.entity.QueryDistanceResDTO
import com.ctrip.dcs.domain.dsporder.gateway.PlatformGeoServiceGateway
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.dto.BufferConfig
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.google.common.collect.Maps
import org.apache.commons.lang3.time.DateUtils
import spock.lang.Specification

import java.util.function.Function
import java.util.stream.Collectors

class DriverEmptyDrivingDurationVisitorTest extends Specification {

    def driverLocationService = Mock(QueryDriverLocationService.class)
    def geoServiceGateway = Mock(PlatformGeoServiceGateway.class)
    def dispatchOrderService = Mock(QueryDspOrderService.class)
    def conflictGateway = Mock(ConflictGateway.class)

    def "visit"() {
        given:
        var dispatchOrder = Mock(DspOrderVO.class)
        var backwardOrder = Mock(DspOrderVO.class)
        var forwardOrder = Mock(DspOrderVO.class)
        var driver = new DriverVO(driverId: driverId, addressLatitude: 99D, addressLongitude: 99D, workTimes: new PeriodsVO(driverWorkTimes))
        var dispatchContext = Mock(DspContext.class)
        var sortContext = Mock(SortContext.class)
        var driverEmptyDurationMap = Maps.newHashMap()
        var driverForwardOrderMap = Maps.newHashMap()
        var driverBackwardOrderMap = Maps.newHashMap()
        var driverLocation = DriverLocationVO.LocationVO.builder().driverId(driverId).longitude(new BigDecimal("99")).latitude(new BigDecimal("99")).build()
        when:
        sortContext.getDspContext() >> dispatchContext
        sortContext.getDriverEmptyDurationMap() >> driverEmptyDurationMap
        sortContext.getCityProperties(_, _, _) >> { String key, Integer cityId, Integer defaultValue -> defaultValue }
        dispatchContext.getDriverForwardOrderMap() >> driverForwardOrderMap
        dispatchContext.getDriverBackwardOrderMap() >> driverBackwardOrderMap
        dispatchOrder.getEstimatedUseTimeBj() >> dispatchOrderEstimatedUseTime
        dispatchOrder.getEstimatedUseTime() >> dispatchOrderEstimatedUseTime
        dispatchOrder.getCategoryCode() >> categoryCode
        dispatchOrder.getCityId() >> 2L
        dispatchOrder.getActualFromLatitude() >> new BigDecimal("99")
        dispatchOrder.getActualFromLongitude() >> new BigDecimal("99")
        dispatchOrder.getActualToLatitude() >> new BigDecimal("99")
        dispatchOrder.getActualToLongitude() >> new BigDecimal("99")
        forwardOrder.getActualToLatitude() >> new BigDecimal("99")
        forwardOrder.getActualToLongitude() >> new BigDecimal("99")
        backwardOrder.getActualFromLatitude() >> new BigDecimal("99")
        backwardOrder.getActualFromLongitude() >> new BigDecimal("99")
        and:
        driverLocationService.queryDriverLocation(_) >> [new DriverLocationVO(driverId, [driverLocation])]
        and:
        if (hasForwardOrder) {
            driverForwardOrderMap.put(driverId, forwardOrder)
        }
        if (hasBackwardOrder) {
            driverBackwardOrderMap.put(driverId, backwardOrder)
        }
        and:
        geoServiceGateway.batchQueryPreBackDistance(_) >> [new QueryDistanceResDTO(originLatitude: 99D, originLongitude: 99D, destinationLatitude: 99D, destinationLongitude: 99D, duration: eEstimatePathDurationSeconds)]
        and:
        conflictGateway.queryBufferConfig(_) >> { args -> ((List<BufferConfig.Request>) args[0]).stream().collect(Collectors.toMap(Function.identity(), it -> BufferConfig.Response.builder().airportBufferSeconds(airportBufferSeconds).stationBufferSeconds(stationBufferSeconds).trafficBufferSeconds(trafficBufferSeconds).build())) }
        and:
        var target = new DriverEmptyDrivingDurationVisitor(dispatchOrder, [driver], conflictGateway, dispatchOrderService, geoServiceGateway, driverLocationService)
        then:
        target.visit(sortContext)
        expect:
        target.getRelatedOrderScene(driver) == expectScene
        sortContext.getDriverEmptyDurationMap().get(driverId) == expectSeconds
        where:
        description | driverId | driverWorkTimes | categoryCode | dispatchOrderEstimatedUseTime | hasForwardOrder | hasBackwardOrder | eEstimatePathDurationSeconds | airportBufferSeconds | stationBufferSeconds | trafficBufferSeconds | expectScene | expectSeconds
        "【无前后向单】【派单时间距离用车时间 = 4小时】" | 1L | [] | CategoryCodeEnum.FROM_AIRPORT | DateUtils.addHours(new Date(), 4) | false | false | 500 | 100 | 200 | 300 | DriverEmptyDrivingDurationVisitor.RelatedOrderScene.NoneRelatedOrderB | 1100
        "【无前后向单】【派单时间距离用车时间 > 4小时】" | 2L | [] | CategoryCodeEnum.TO_AIRPORT | DateUtils.addHours(new Date(), 5) | false | false | 1000 | 100 | 200 | 300 | DriverEmptyDrivingDurationVisitor.RelatedOrderScene.NoneRelatedOrderA | 1600
        "【无前后向单】【派单时间距离用车时间 < 4小时】" | 3L | [] |CategoryCodeEnum.TO_STATION | DateUtils.addHours(new Date(), 3) | false | false | 2000 | 100 | 200 | 300 |DriverEmptyDrivingDurationVisitor.RelatedOrderScene.NoneRelatedOrderB |  2600
        "【无前向单 + 有后向单】【待派订单预估用车时间 ≥ 司机工作开始时间 + 2小时】" | 4L | ["08:00~12:00"] | CategoryCodeEnum.FROM_AIRPORT | DateUtil.parse("2025-01-20 11:00", DateUtil.DATE_MINUTES_FORMAT) | false | true | 3000 | 100 | 200 | 300 | DriverEmptyDrivingDurationVisitor.RelatedOrderScene.OnlyBackwardOrderA | 3600
        "【无前向单 + 有后向单】【待派订单预估用车时间 < 司机工作开始时间 + 2小时】" | 5L | ["08:00~12:00"] | CategoryCodeEnum.FROM_AIRPORT | DateUtil.parse("2025-01-20 08:30", DateUtil.DATE_MINUTES_FORMAT) | false | true | 4000 | 100 | 200 | 300 | DriverEmptyDrivingDurationVisitor.RelatedOrderScene.OnlyBackwardOrderB | 4600
        "【无前向单 + 有后向单】【待派订单预估用车时间 不在 司机工作时间范围内】" | 6L | ["08:00~12:00"] | CategoryCodeEnum.FROM_AIRPORT | DateUtil.parse("2025-01-20 07:30", DateUtil.DATE_MINUTES_FORMAT) | false | true | null | null | null | null | DriverEmptyDrivingDurationVisitor.RelatedOrderScene.MissData | null
        "【有前向单 + 无后向单】" | 7L | [] | CategoryCodeEnum.FROM_AIRPORT | DateUtil.parse("2025-01-20 08:30", DateUtil.DATE_MINUTES_FORMAT) | true | false | 5000 | 100 | 200 | 300 | DriverEmptyDrivingDurationVisitor.RelatedOrderScene.OnlyForwardOrder | 5600
        "【有前向单 + 有后向单】" | 8L | [] | CategoryCodeEnum.FROM_AIRPORT | DateUtil.parse("2025-01-20 08:30", DateUtil.DATE_MINUTES_FORMAT) | true | true | 6000 | 100 | 200 | 300 | DriverEmptyDrivingDurationVisitor.RelatedOrderScene.BothForwardAndBackwardOrder | 6600
    }

}