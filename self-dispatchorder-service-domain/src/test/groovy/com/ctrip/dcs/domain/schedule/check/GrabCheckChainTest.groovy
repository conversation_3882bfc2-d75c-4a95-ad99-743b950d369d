package com.ctrip.dcs.domain.schedule.check

import cn.hutool.core.lang.Pair
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.dsporder.value.BusinessTravelDistributionChannelVO
import com.ctrip.dcs.domain.schedule.check.item.Check
import com.ctrip.dcs.domain.schedule.check.source.CheckSource
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.assertj.core.util.Lists
import org.mockito.Mock
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class GrabCheckChainTest extends Specification {


    def order = new DspOrderVO()

    def driver = new DriverVO()

    def dspModel = new DspModelVO(order, driver)

    def checkModel = new CheckModel(dspModel, CheckCode.PASS)

    def source = new CheckSource() {

        @Override
        List<CheckModel> init(CheckContext context) {
            return Lists.newArrayList(checkModel)
        }

        @Override
        CheckModel taken(CheckContext context) {
            return checkModel
        }
    }

    def item = new Check() {
        @Override
        List<CheckModel> check(List<CheckModel> models, CheckContext context) {
            return Lists.newArrayList(checkModel)
        }
    }

    def checkContext = Mock(CheckContext)

    def dspContextService = Mock(DspContextService)


    @Unroll
    def "test check"() {
        def chain = new GrabCheckChain(stage)
        chain.addSource(source)
        chain.addCheck(item)
        when: "执行校验方法"
        List<CheckModel> pass = chain.check(checkContext)

        then: "验证校验结果"
        pass != null
        pass.size() == 1
        pass.get(0).getCheckCode() == code

        where:
        stage          || code
        DspStage.DSP   || CheckCode.PASS
        DspStage.TAKEN || CheckCode.PASS
    }


    @Unroll
    def "test filterCachedCheckResult"() {
        def chain = new GrabCheckChain(stage)
        chain.addSource(source)
        chain.addCheck(item)
        CheckContext checkContext1 = new CheckContext();
        checkContext1.setDuid(duid)
        DspContext dspContext = new DspContext(dspContextService)
        checkContext1.setContext(dspContext)
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setDspOrderId("1")
        checkContext1.setDspOrder(dspOrderVO)
        dspContextService.getCachedCheckResultForGrab(_, _) >> Lists.newArrayList("1")
        when: "执行校验方法"
        Pair<List<CheckModel>, List<CheckModel>> res = chain.filterCachedCheckResult(checkContext1, models as List<CheckModel>)

        then: "验证校验结果"
        res.value.size() == code


        where:
        stage          | models                                                | duid                                                                                   || code
        DspStage.TAKEN | Lists.newArrayList()                                  | null                                                                                   || 0
        DspStage.TAKEN | Lists.newArrayList(new CheckModel(new DspModelVO()))  | null                                                                                   || 0
        DspStage.DSP   | Lists.newArrayList(new CheckModel(new DspModelVO()))  | null                                                                                   || 0
        DspStage.DSP   | Lists.newArrayList(new CheckModel(new DspModelVO()))  | DuidVO.of("52988019756335304-1052988019756335305-2052988019756335314-1-71-11-5-0-0-0") || 0
        DspStage.DSP   | Lists.newArrayList(new CheckModel(new DspModelVO()))  | DuidVO.of("52988019756335304-1052988019756335305-2052988019756335314-1-71-30-5-0-0-0") || 0
        DspStage.DSP   | Lists.newArrayList(getCheckModel(), getCheckModel2()) | DuidVO.of("52988019756335304-1052988019756335305-2052988019756335314-1-71-30-5-0-0-0") || 1

    }


    @Unroll
    def "test cacheCheckResultForGrab"() {
        def chain = new GrabCheckChain(stage)
        chain.addSource(source)
        chain.addCheck(item)
        CheckContext checkContext1 = new CheckContext();
        checkContext1.setDuid(duid)
        DspContext dspContext = new DspContext(dspContextService)
        checkContext1.setContext(dspContext)
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setDspOrderId("1")
        checkContext1.setDspOrder(dspOrderVO)
        dspContextService.getCheckResultCacheConfigSeconds(_) >> getCheckResultCacheConfigSeconds
        when: "执行校验方法"
        def result = chain.cacheCheckResultForGrab(models as List<CheckModel>, checkContext1)

        then: "验证校验结果"
        result == code


        where:
        stage          | models                                               | duid                                                                                   | getCheckResultCacheConfigSeconds || code
        DspStage.TAKEN | Lists.newArrayList()                                 | null                                                                                   | null                             || null
        DspStage.TAKEN | Lists.newArrayList(new CheckModel(new DspModelVO())) | null                                                                                   | null                             || null
        DspStage.DSP   | Lists.newArrayList(new CheckModel(new DspModelVO())) | null                                                                                   | null                             || null
        DspStage.DSP   | Lists.newArrayList(new CheckModel(new DspModelVO())) | DuidVO.of("52988019756335304-1052988019756335305-2052988019756335314-1-71-11-5-0-0-0") | null                             || null
        DspStage.DSP   | Lists.newArrayList(new CheckModel(new DspModelVO())) | DuidVO.of("52988019756335304-1052988019756335305-2052988019756335314-1-71-30-5-0-0-0") | 0                                || null
        DspStage.DSP   | Lists.newArrayList(new CheckModel(new DspModelVO())) | DuidVO.of("52988019756335304-1052988019756335305-2052988019756335314-1-71-30-5-0-0-0") | 100                              || null

    }

    CheckModel getCheckModel() {
        DspModelVO dspModelVO = new DspModelVO()
        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(1)
        dspModelVO.setDriver(driverVO)
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setDspOrderId("1")
        dspModelVO.setOrder(dspOrderVO)
        CheckModel checkModel1 = new CheckModel(dspModelVO, CheckCode.PASS)
        return checkModel1
    }

    CheckModel getCheckModel2() {
        DspModelVO dspModelVO = new DspModelVO()
        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(2)
        dspModelVO.setDriver(driverVO)
        DspOrderVO dspOrderVO = new DspOrderVO()
        dspOrderVO.setDspOrderId("1")
        dspModelVO.setOrder(dspOrderVO)
        CheckModel checkModel1 = new CheckModel(dspModelVO, CheckCode.PASS)
        return checkModel1
    }
}
