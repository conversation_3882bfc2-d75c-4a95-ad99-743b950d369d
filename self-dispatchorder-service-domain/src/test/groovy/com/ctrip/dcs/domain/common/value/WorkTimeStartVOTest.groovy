package com.ctrip.dcs.domain.common.value


import spock.lang.Specification
import spock.lang.Unroll

class WorkTimeStartVOTest extends Specification {

    @Unroll
    def "test after"() {
        given:
        WorkTimeStartVO workTimeStartVO = new WorkTimeStartVO(startTime)

        when:
        boolean result = workTimeStartVO.after(2000)

        then:
        result == r

        where:
        startTime || r
        "20:00"   || false
        "20:01"   || true
        "19:59"   || false
    }

    @Unroll
    def "test new"() {
        when:
        new WorkTimeStartVO(startTime)

        then:
        thrown(e)

        where:
        startTime   || e
        ""          || IllegalArgumentException
        null        || IllegalArgumentException
        "null"      || IllegalArgumentException
        "null:"     || IllegalArgumentException
        "null:null" || NumberFormatException
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme