package com.ctrip.dcs.domain.schedule.sort.feature.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.apache.commons.lang3.StringUtils
import spock.lang.Specification

class DriverEmptyDrivingDurationFeatureTest extends Specification {

    def "test"() {
        given:
        var sortContext = Mock(SortContext)
        var dispatchOrder = Mock(DspOrderVO)
        var dspContext = Mock(DspContext)
        var sortModel = new SortModel(new DspModelVO(new DspOrderVO(), new DriverVO(driverId: driver)))
        var durationMap = Mock(Map)
        and:
        var target = new DriverEmptyDrivingDurationFeature()
        when:
        sortContext.getDriverEmptyDurationMap() >> durationMap
        sortContext.getDspOrder() >> dispatchOrder
        sortContext.getCityProperties(DriverEmptyDrivingDurationFeature.NORMALIZE_CONFIG_MAX, _, _) >> 52
        sortContext.getCityProperties(DriverEmptyDrivingDurationFeature.NORMALIZE_CONFIG_MIN, _, _) >> 0
        sortContext.getDspContext() >> dspContext
        dspContext.getDriverForwardOrderMap() >> [:]
        dspContext.getDriverBackwardOrderMap() >> [:]
        dspContext.getDriverForwardEmptyMap() >> [:]
        dspContext.getDriverBackwardEmptyMap() >> [:]
        dspContext.getDriverOriginalEmptyMap() >> [:]
        durationMap.get(_) >> Optional.ofNullable(seconds).map(Double::valueOf).orElse(null)
        then:
        var out = target.value(sortModel, sortContext)
        target.normalize(sortContext, [out])
        var actual = out.getValue()
        expect:
        StringUtils.startsWith(String.valueOf(actual), String.valueOf(except))
        where:
        description      | driver | seconds | except
        "空值（边界值）"   | 1      | null    | 0.0
        "0分钟（边界值）"  | 2      | 0       | 1.0
        "1分钟"          | 3      | 60      | 0.98076
        "2分钟"          | 4      | 120     | 0.96153
        "30分钟"         | 5      | 1800    | 0.42307
        "52分钟（边界值）" | 6      | 3120    | 0.0
        "60分钟（边界值）" | 7      | 3600    | 0.0
    }

}