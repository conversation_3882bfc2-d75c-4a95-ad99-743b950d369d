package com.ctrip.dcs.domain.schedule.check.source.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.dcs.domain.schedule.value.TransportGroupOrderConfig
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.*

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class NewManualDriverCheckSourceTest extends Specification {

    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspContextService dspContextService
    @Mock
    TransportGroupVO transportGroup
    @Mock
    DriverVO driver
    @Mock
    DspOrderVO dspOrder
    @InjectMocks
    NewManualDriverCheckSource newManualDriverCheckSource

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test init"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(checkContext.getDriver()).thenReturn(driver)
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(driver.getTransportGroups()).thenReturn([transportGroup])
        when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(transportGroup.getSupplierId()).thenReturn(1L)
        when(dspOrder.getSupplierId()).thenReturn(1)
        when(transportGroup.getTransportGroupId()).thenReturn(1L)
        when(dspContextService.queryTransports(any())).thenReturn([]).thenReturn([transportGroup])

        when:
        List<CheckModel> result = newManualDriverCheckSource.init(checkContext)

        then:
        result.isEmpty() == true
    }

    def "test init 2"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(checkContext.getDriver()).thenReturn(driver)
        when(checkContext.getDrivers()).thenReturn([driver])
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getTransportGroup()).thenReturn(transportGroup)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(driver.getTransportGroups()).thenReturn([transportGroup])
        when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(transportGroup.getSupplierId()).thenReturn(1L)
        when(dspOrder.getSupplierId()).thenReturn(1)
        when(transportGroup.getTransportGroupId()).thenReturn(1L)
        when(dspContextService.queryTransports(any())).thenReturn([]).thenReturn([transportGroup])

        when:
        List<CheckModel> result = newManualDriverCheckSource.init(checkContext)

        then:
        result.isEmpty() == false
    }

    def "test taken"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)

        when:
        CheckModel result = newManualDriverCheckSource.taken(checkContext)

        then:
        result.getCheckCode() == CheckCode.NULL
    }

    def "test taken 2"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(checkContext.getDriver()).thenReturn(driver)
        when(checkContext.getDrivers()).thenReturn([driver])
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getTransportGroup()).thenReturn(transportGroup)
        when(dspContext.getService()).thenReturn(dspContextService)
        when(driver.getTransportGroups()).thenReturn([transportGroup])
        when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(transportGroup.getSupplierId()).thenReturn(1L)
        when(dspOrder.getSupplierId()).thenReturn(1)
        when(transportGroup.getTransportGroupId()).thenReturn(1L)
        when(dspContextService.queryTransports(any())).thenReturn([]).thenReturn([transportGroup])

        when:
        CheckModel result = newManualDriverCheckSource.taken(checkContext)

        then:
        result.getCheckCode() == CheckCode.PASS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme