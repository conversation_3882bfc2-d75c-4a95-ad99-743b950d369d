package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.OrderExtendAttributeCodeEnum
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverLocationVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderExtendAttributeVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.item.CheckItemId
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Maps
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DowngradeCarTypeOrderDriverCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    ConfigService configService
    @Mock
    DriverLocationVO driverLocationVO
    @Mock
    DriverLocationVO.LocationVO locationVO
    @InjectMocks
    DowngradeCarTypeOrderDriverCheck downgradeCarTypeOrderDriverCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(context.getConfigService()).thenReturn(configService)
        Mockito.when(context.getDspOrder()).thenReturn(order)
    }

    def "check"() {
        given:
        Mockito.when(order.getCarTypeId()).thenReturn(117)
        Mockito.when(order.getOrderExtendAttributeInfo()).thenReturn([new OrderExtendAttributeVO("is_downgrade_car_type_order", is_downgrade_car_type_order), new OrderExtendAttributeVO("upgrade_car_type_id", upgrade_car_type_id)])
        Mockito.when(car.getCarTypeId()).thenReturn(carTypeId)
        Map<Long, CarTypeLevelRelation> map = Maps.newHashMap()
        map.put(121L, CarTypeLevelRelation.BELOW)
        map.put(118L, CarTypeLevelRelation.PARALLEL)
        map.put(117, CarTypeLevelRelation.HIGHER)
        CarTypeLevelRelationsVO carTypeLevelRelations = new CarTypeLevelRelationsVO(map)
        Mockito.when(context.carTypeRelations(Mockito.any())).thenReturn(carTypeLevelRelations)
        Mockito.when(driverInfo.getIntendVehicleTypeId()).thenReturn(intendVehicleTypeId)

        when:
        CheckCode result = downgradeCarTypeOrderDriverCheck.check(checkModel, context)

        then:
        result == code

        where:
        is_downgrade_car_type_order | carTypeId | upgrade_car_type_id | intendVehicleTypeId || code
        "0"                         | 0L        | "118"               | ""                  || CheckCode.PASS
        "1"                         | 0L        | "118"               | ""                  || CheckCode.DOWNGRADE_CAR_TYPE_ORDER_DRIVER_NO_CAR
        "1"                         | 118L      | "0"                 | ""                  || CheckCode.DOWNGRADE_CAR_TYPE_ORDER_NO_UPGRADE_CAR_TYPE
        "1"                         | 117L      | "118"               | ""                  || CheckCode.DOWNGRADE_CAR_TYPE_ORDER_DRIVER_CAR_TYPE
        "1"                         | 118L      | "118"               | ""               || CheckCode.DOWNGRADE_CAR_TYPE_ORDER_DRIVER_INTEND_CAR_TYPE
        "1"                         | 118L      | "118"               | "117"               || CheckCode.PASS
    }

    def "downgrade"() {
        given:
        when:
        CheckCode result = downgradeCarTypeOrderDriverCheck.downgrade()
        then:
        result == CheckCode.DOWNGRADE_CAR_TYPE_ORDER_DRIVER_NO_CAR
    }

    def "load"() {
        given:
        when:
        def result = downgradeCarTypeOrderDriverCheck.load([checkModel], context)
        then:
        result == null
    }

    def "check2"(){
        given:
        Mockito.when(order.getCarTypeId()).thenReturn(117)
        Mockito.when(order.getCityId()).thenReturn(12)
        Mockito.when(order.getOrderExtendAttributeInfo()).thenReturn([new OrderExtendAttributeVO("is_downgrade_car_type_order",  "1"), new OrderExtendAttributeVO("upgrade_car_type_id", "118" )])
        Mockito.when(car.getCarTypeId()).thenReturn(118L)
        Map<Long, CarTypeLevelRelation> map = Maps.newHashMap()
        map.put(121L, CarTypeLevelRelation.BELOW)
        map.put(118L, CarTypeLevelRelation.PARALLEL)
        map.put(117, CarTypeLevelRelation.HIGHER)
        CarTypeLevelRelationsVO carTypeLevelRelations = new CarTypeLevelRelationsVO(map)
        Mockito.when(context.carTypeRelations(Mockito.any())).thenReturn(carTypeLevelRelations)
        Mockito.when(driverInfo.getIntendVehicleTypeId()).thenReturn("117")
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_BROADCAST)
        Mockito.when(context.getProperties("I146GrayCity","")).thenReturn("12")
        when:
        CheckCode result = downgradeCarTypeOrderDriverCheck.check(checkModel, context)
        then:
        result == CheckCode.PASS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme