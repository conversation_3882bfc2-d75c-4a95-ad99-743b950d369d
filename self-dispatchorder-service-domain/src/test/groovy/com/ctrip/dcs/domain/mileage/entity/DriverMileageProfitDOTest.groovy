package com.ctrip.dcs.domain.mileage.entity

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO
import com.ctrip.dcs.domain.common.value.DspOrderFeeQuantileVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.EmptyDrivingVO
import com.ctrip.dcs.domain.common.value.PairLocationVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverMileageProfitDOTest extends Specification {
    @Mock
    DriverVO driver
    @Mock
    DriverWorkTimeVO workTime
    @Mock
    DspOrderVO order1
    @Mock
    DspOrderVO order2
    @Mock
    DspOrderVO order3
    @Mock
    EmptyDrivingVO emptyDrivingVO
    @Mock
    DspOrderFeeQuantileVO dspOrderFeeQuantileVO
    @InjectMocks
    DriverMileageProfitDO driverMileageProfitDO

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test location"() {
        given:
        when(driver.getAddressLongitude()).thenReturn(0d)
        when(driver.getAddressLatitude()).thenReturn(0d)
        when(order1.getActualFromLongitude()).thenReturn(BigDecimal.TEN)
        when(order1.getActualFromLatitude()).thenReturn(BigDecimal.TEN)
        when(order1.getActualToLongitude()).thenReturn(BigDecimal.TEN)
        when(order1.getActualToLatitude()).thenReturn(BigDecimal.TEN)

        when(order2.getActualFromLongitude()).thenReturn(BigDecimal.TEN)
        when(order2.getActualFromLatitude()).thenReturn(BigDecimal.TEN)
        when(order2.getActualToLongitude()).thenReturn(BigDecimal.TEN)
        when(order2.getActualToLatitude()).thenReturn(BigDecimal.TEN)
        when(driver.getCityId()).thenReturn(1l)

        when:
        List<PairLocationVO> result1 = DriverMileageProfitDO.location(driver, [])
        List<PairLocationVO> result2 = DriverMileageProfitDO.location(driver, [order1, order2])

        then:
        result1 == []
        result2.size() == 3
    }

    def "test calculate"() {
        given:
        DriverMileageProfitDO driverMileageProfitDO = new DriverMileageProfitDO(driver, workTime)

        when(order1.getCarTypeId()).thenReturn(117)
        when(order1.getCostAmount()).thenReturn(BigDecimal.TEN)
        when(order1.isFinish()).thenReturn(true)
        when(order1.getEstimatedKm()).thenReturn(BigDecimal.ONE)
        when(order1.getActualKm()).thenReturn(BigDecimal.ONE)
        when(order1.getTollFee()).thenReturn(BigDecimal.ZERO)
        when(order1.getParkingFee()).thenReturn(BigDecimal.ZERO)

        when(order2.getCarTypeId()).thenReturn(117)
        when(order2.getCostAmount()).thenReturn(BigDecimal.valueOf(5))
        when(order2.isFinish()).thenReturn(false)
        when(order2.getEstimatedKm()).thenReturn(BigDecimal.ONE)
        when(order2.getActualKm()).thenReturn(BigDecimal.ONE)
        when(order2.getTollFee()).thenReturn(BigDecimal.ZERO)
        when(order2.getParkingFee()).thenReturn(BigDecimal.ZERO)

        when(order3.getCarTypeId()).thenReturn(118)
        when(order3.getCostAmount()).thenReturn(BigDecimal.valueOf(0))
        when(order3.isFinish()).thenReturn(true)
        when(order3.getEstimatedKm()).thenReturn(BigDecimal.ONE)
        when(order3.getActualKm()).thenReturn(null)
        when(order3.getTollFee()).thenReturn(BigDecimal.ZERO)
        when(order3.getParkingFee()).thenReturn(BigDecimal.ZERO)

        when(dspOrderFeeQuantileVO.getCarTypeId()).thenReturn(117)
        when(dspOrderFeeQuantileVO.getHigh()).thenReturn(BigDecimal.valueOf(9))
        when(dspOrderFeeQuantileVO.getMedium()).thenReturn(BigDecimal.valueOf(1))

        when(emptyDrivingVO.getDistance()).thenReturn(1000)

        when:
        driverMileageProfitDO.calculate([order1, order2, order3], [emptyDrivingVO], [117:dspOrderFeeQuantileVO])

        then:
        driverMileageProfitDO.getOrderCounts() == 3
        driverMileageProfitDO.getHighPriorityOrderCounts() == 1
        driverMileageProfitDO.getMediumPriorityOrderCounts() == 1
        driverMileageProfitDO.getEmptyMileage() == 1.0
        driverMileageProfitDO.getOrderMileage() == 3.0
        driverMileageProfitDO.getIncome() == 15.0
        driverMileageProfitDO.getProfit() == 15.0 - (3.0 + 1.0) * 0.5
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme