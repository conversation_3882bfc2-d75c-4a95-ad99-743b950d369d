package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.DriverPointsGateway
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.value.CityPointVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class CityPointsVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DriverVO driver
    @Mock
    SortContext sortContext
    @Mock
    DriverPointsGateway dcsDriverLevelServiceGateway
    @Mock
    CityPointVO cityPointVO
    @InjectMocks
    CityPointsVisitor cityPointsVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(dcsDriverLevelServiceGateway.queryCityPointsInfo(anyInt())).thenReturn(cityPointVO)
        when(sortContext.getCityPointInfoMap()).thenReturn([1:cityPointVO])
        CityPointsVisitor cityPointsVisitor = new CityPointsVisitor(1, dcsDriverLevelServiceGateway)
        when:
        cityPointsVisitor.visit(sortContext)

        then:
        true//todo - validate something
    }

    def "test visit 2"() {
        given:
        when(dcsDriverLevelServiceGateway.queryCityPointsInfo(anyInt())).thenReturn(cityPointVO)
        when(sortContext.getCityPointInfoMap()).thenReturn([0:cityPointVO])
        CityPointsVisitor cityPointsVisitor = new CityPointsVisitor(1, dcsDriverLevelServiceGateway)
        when:
        cityPointsVisitor.visit(sortContext)

        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme