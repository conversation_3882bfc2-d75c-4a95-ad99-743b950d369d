package com.ctrip.dcs.domain.schedule.process.impl

import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.*
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.factory.BatchConfirmDspOrderFactory
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.service.SortService
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SelectGrabOrderProcessTest extends Specification {
    @Mock
    Logger logger
    @Mock
    SelectGrabOrderRepository selectGrabOrderRepository
    @Mock
    QueryDspOrderService dspOrderService
    @Mock
    ScheduleTaskRepository scheduleTaskRepository
    @Mock
    RecommendService recommendService
    @Mock
    SortService sortService
    @Mock
    CheckService checkService
    @Mock
    BatchConfirmDspOrderFactory dspOrderGroupFactory
    @Mock
    DriverOrderFactory driverOrderFactory
    @Mock
    QueryDriverService queryDriverService
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    ConfirmDspOrderService confirmDspOrderService
    @Mock
    ConflictGateway conflictGateway
    @Mock
    QueryVehicleService queryVehicleService
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    MessageProviderService messageProducer
    @Mock
    ScheduleTaskDO scheduleTaskDO
    @Mock
    DspOrderVO dspOrderVO;
    @Mock
    SubSkuVO subSkuVO
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DriverVO driverVO;
    @Mock
    TransportGroupVO transportGroupVO;
    @Mock
    SupplierVO supplierVO
    @Mock
    GrabOrderDO grabOrderDO
    @Mock
    CarVO carVO
    @InjectMocks
    SelectGrabOrderProcess selectGrabOrderProcess

    def setup() {
        MockitoAnnotations.initMocks(this)
        when(transportGroupVO.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(dspModelVO.getTransportGroup()).thenReturn(transportGroupVO)
        when(driverVO.getTransportGroups()).thenReturn([transportGroupVO])
        when(driverVO.getSupplier()).thenReturn(supplierVO)
        when(subSkuVO.getSubSkuId()).thenReturn(1)
        when(subSkuVO.getTakenType()).thenReturn(TakenType.ASSISTANT)
        when(subSkuVO.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        when(scheduleTaskDO.getTaskId()).thenReturn(1L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(1L)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("1")
        when(scheduleTaskDO.getRound()).thenReturn(1)
        when(checkModel.isPass()).thenReturn(true)
        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(checkService.check(any() as TakenCheckCommand)).thenReturn(checkModel)
        when(recommendService.recommend(any(), any(), any())).thenReturn([new SortModel(dspModelVO)])
    }

    def "test execute"() {
        given:
        VehicleVO vehicleVO = new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1L,1,"desc",0)
        when(selectGrabOrderRepository.find(any(), any())).thenReturn([])
        when(dspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(sortService.sort(any(), any(), any())).thenReturn([new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [null], null, null, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", 1,0, 1l, null, YesOrNo.NO,false,0,1, "", "","","","",null)])
        when(dspOrderGroupFactory.create(any(), any(), any(), any(), any())).thenReturn(new BatchConfirmDspOrderVO("masterOrderId", "masterDriverOrderId", "slaveOrderId", "slaveDriverOrderId"))
        when(queryDriverService.queryDriver(any() as Set, any(ParentCategoryEnum.class), anyLong())).thenReturn([driverVO])
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])
        when(conflictGateway.checkOrderConflict(any(), any(), anyBoolean())).thenReturn(Boolean.TRUE)
        when(queryVehicleService.query(anyLong(),any())).thenReturn(vehicleVO)

        when:
        selectGrabOrderProcess.execute(dspOrderVO, subSkuVO)

        then:
        vehicleVO.getCarId() == 1L
    }

    def "test execute 1"() {
        given:
        VehicleVO vehicleVO = new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1L,1,"desc",0)
        when(dspOrderVO.isDispatching()).thenReturn(false)
        when(selectGrabOrderRepository.find(any(), any())).thenReturn([grabOrderDO])
        when(dspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(sortService.sort(any(), any(), any())).thenReturn([new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [null], null, null, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", 1,0, 1l, null, YesOrNo.NO,false,1,1, "", "","","","",null)])
        when(dspOrderGroupFactory.create(any(), any(), any(), any(), any())).thenReturn(new BatchConfirmDspOrderVO("masterOrderId", "masterDriverOrderId", "slaveOrderId", "slaveDriverOrderId"))
        when(queryDriverService.queryDriver(any() as Set,any(ParentCategoryEnum.class), anyLong())).thenReturn([driverVO])
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])
        when(conflictGateway.checkOrderConflict(any(), any(), anyBoolean())).thenReturn(Boolean.TRUE)
        when(queryVehicleService.query(anyLong(),any())).thenReturn(vehicleVO)

        when:
        selectGrabOrderProcess.execute(dspOrderVO, subSkuVO)

        then:
        vehicleVO.getCarId() == 1L
    }

    def "test execute 2"() {
        given:
        VehicleVO vehicleVO = new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1L,1,"desc",1)
        when(dspOrderVO.isDispatching()).thenReturn(true)
        when(selectGrabOrderRepository.find(any(), any())).thenReturn([grabOrderDO])
        when(dspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(sortService.sort(any(), any(), any())).thenReturn([new DriverVO(1l, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [null], null, null, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId",1, 0, 1l, null, YesOrNo.NO,false,0,1, "", "","","","",null)])
        when(dspOrderGroupFactory.create(any(), any(), any(), any(), any())).thenReturn(new BatchConfirmDspOrderVO("masterOrderId", "masterDriverOrderId", "slaveOrderId", "slaveDriverOrderId"))
        when(queryDriverService.queryDriver(any() as Set,any(ParentCategoryEnum.class), anyLong())).thenReturn([driverVO])
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])
        when(conflictGateway.checkOrderConflict(any(), any(), anyBoolean())).thenReturn(Boolean.TRUE)
        when(queryVehicleService.query(anyLong(),any())).thenReturn(vehicleVO)

        when:
        selectGrabOrderProcess.execute(dspOrderVO, subSkuVO)

        then:
        vehicleVO.getCarId() == 1L
    }

    def "test execute 3"() {
        given:
        VehicleVO vehicleVO = new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1L,1,"desc",0)
        when(carVO.getCarId()).thenReturn(1L)
        when(driverVO.getCar()).thenReturn(carVO)
        when(driverVO.getDriverId()).thenReturn(1L)
        when(dspOrderVO.isDispatching()).thenReturn(true)
        when(selectGrabOrderRepository.find(any(), any())).thenReturn([grabOrderDO])
        when(dspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(sortService.sort(any(), any(), any())).thenReturn([driverVO])
        when(dspOrderGroupFactory.create(any(), any(), any(), any(), any())).thenReturn(new BatchConfirmDspOrderVO("masterOrderId", "masterDriverOrderId", "slaveOrderId", "slaveDriverOrderId"))
        when(queryDriverService.queryDriver(any() as Set,any(ParentCategoryEnum.class), anyLong())).thenReturn([driverVO])
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])
        when(conflictGateway.checkOrderConflict(any(), any(), anyBoolean())).thenReturn(Boolean.TRUE)
        when(queryVehicleService.query(any(),any())).thenReturn(vehicleVO)

        when:
        selectGrabOrderProcess.execute(dspOrderVO, subSkuVO)

        then:
        vehicleVO.getCarId() == 1L
    }

    def "test queryVehicle"() {
        given:
        VehicleVO vehicleVO = new VehicleVO(1l, "carLicense", 1l, "carColor", 1l, "carBrandName", 1l, "carTypeName", 1l, "carSeriesName", 0,1L,1,"desc",0)
        when(carVO.getCarId()).thenReturn(1L)
        when(driverVO.getCar()).thenReturn(carVO)
        when(driverVO.getDriverId()).thenReturn(1L)
        when(dspOrderVO.isDispatching()).thenReturn(true)
        when(selectGrabOrderRepository.find(any(), any())).thenReturn([grabOrderDO])
        when(dspOrderService.query(anyString())).thenReturn(dspOrderVO)
        when(sortService.sort(any(), any(), any())).thenReturn([driverVO])
        when(dspOrderGroupFactory.create(any(), any(), any(), any(), any())).thenReturn(new BatchConfirmDspOrderVO("masterOrderId", "masterDriverOrderId", "slaveOrderId", "slaveDriverOrderId"))
        when(queryDriverService.queryDriver(anySet() , any(), anyLong())).thenReturn([driverVO])
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["String": "String"])
        when(conflictGateway.checkOrderConflict(any(), any(), anyBoolean())).thenReturn(Boolean.TRUE)
        when(queryVehicleService.query(any(), any())).thenReturn(vehicleVO)

        when:
        VehicleVO vehicle = selectGrabOrderProcess.queryVehicle(driverVO, ParentCategoryEnum.DAY)

        then:
        vehicle != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
