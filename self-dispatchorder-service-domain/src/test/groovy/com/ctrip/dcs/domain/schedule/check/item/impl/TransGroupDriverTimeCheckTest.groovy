package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.exception.CheckItemValidateException
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit

class TransGroupDriverTimeCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    TransportGroupVO transportGroup
    @InjectMocks
    TransGroupDriverTimeCheck transGroupDriverTimeCheck = new TransGroupDriverTimeCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    @Unroll
    def "订单预估用车与当前时间差<阈值"() {
        given:
        TakeOrderLimitTimeMinuteVO takeOrderLimitTimeMinute = new TakeOrderLimitTimeMinuteVO(10)
        Mockito.when(order.getEstimatedUseTimeBj()).thenReturn(DateUtil.addMinutes(new Date(), 5))
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getTakeOrderLimitTime()).thenReturn(takeOrderLimitTimeMinute)

        when:
        CheckCode result = transGroupDriverTimeCheck.check(checkModel, context)

        then:
        result == CheckCode.OVER_TRANS_GROUP_TIME_LIMIT
    }

    @Unroll
    def "订单预估用车与当前时间差>=阈值"() {
        given:
        TakeOrderLimitTimeMinuteVO takeOrderLimitTimeMinute = new TakeOrderLimitTimeMinuteVO(10)
        Mockito.when(order.getEstimatedUseTimeBj()).thenReturn(DateUtil.addMinutes(new Date(), 15))
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getTakeOrderLimitTime()).thenReturn(takeOrderLimitTimeMinute)

        when:
        CheckCode result = transGroupDriverTimeCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    @Unroll
    def "test check"() {
        when:
        CheckCode result = transGroupDriverTimeCheck.check(cm, Mock(CheckContext))

        then:
        result == r

        where:
        cm                                                                                                                                                                                                                                                  || r
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO()), order: new DspOrderVO()))                                                                                          || CheckCode.PASS
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(1)), order: new DspOrderVO()))                                                                                         || CheckCode.OVER_TRANS_GROUP_TIME_LIMIT
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(1)), order: new DspOrderVO(estimatedUseTimeBj: new Date(System.currentTimeMillis()))))                                 || CheckCode.OVER_TRANS_GROUP_TIME_LIMIT
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(1)), order: new DspOrderVO(estimatedUseTimeBj: new Date(System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(2L))))) || CheckCode.PASS
    }

    def "test downgrade"() {
        when:
        CheckCode result = transGroupDriverTimeCheck.downgrade()

        then:
        result == CheckCode.OVER_TRANS_GROUP_TIME_LIMIT
    }

    @Unroll
    def "test validate"() {
        when:
        transGroupDriverTimeCheck.validate(checkModel, Mock(CheckContext))

        then:
        thrown(e)

        where:
        checkModel                                                                                                                                                                                    || e
        new CheckModel()                                                                                                                                                                              || CheckItemValidateException
        new CheckModel(model: new DspModelVO())                                                                                                                                                       || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO()))                                                                                                                 || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO())))                                                             || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(), transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)))    || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(), transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST))) || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(), transportGroupMode: TransportGroupMode.REGISTER_DISPATCH)))   || CheckItemValidateException
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme