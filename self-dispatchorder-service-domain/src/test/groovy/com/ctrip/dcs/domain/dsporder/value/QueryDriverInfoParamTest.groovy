package com.ctrip.dcs.domain.dsporder.value

import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum
import org.assertj.core.util.Sets
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2025/5/6 11:04
 */
class QueryDriverInfoParamTest extends Specification {
    def testObj = new QueryDriverInfoParam(ParentCategoryEnum.JNT,12);
    def driverIds = Mock(Set)

    def setup() {
        testObj.driverIds = driverIds
    }

    @Unroll
    def "test"() {
        given:
        when:
        testObj.setParentCategoryEnum(ParentCategoryEnum.JNT)
        testObj.setSupplierId(12)
        testObj.setDriverIds(Sets.newHashSet());
        testObj.addDriverId(1L)
        then:
        Assert.assertTrue(Objects.equals(testObj.getParentCategoryEnum(), ParentCategoryEnum.JNT))
        Assert.assertTrue(Objects.equals(testObj.getSupplierId(), 12))
        Assert.assertTrue(Objects.equals(testObj.getDriverIds().size(), 1))
    }
}
