package com.ctrip.dcs.domain.schedule.check.item.impl


import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class ManualCarStatusCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    CarVO car
    @Mock
    CheckContext checkContext
    @InjectMocks
    ManualCarStatusCheck manualCarStatusCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
    }
    @Unroll
    def "test check"() {
        given:
        when(checkModel.getModel()).thenReturn(dspModel)
        when(dspModel.getCar()).thenReturn(vehicleStatus)

        when:
        CheckCode result = manualCarStatusCheck.check(checkModel, checkContext)

        then:
        result == code

        where:
        vehicleStatus                          || code
        null                                   || CheckCode.PASS
        new CarVO(carId: 0L)                   || CheckCode.PASS
        new CarVO(carId: 1L, vehicleStatus: 0) || CheckCode.VEHICLE_NOT_ACTIVE
        new CarVO(carId: 1L, vehicleStatus: 1) || CheckCode.PASS
        new CarVO(carId: 1L, vehicleStatus: 3) || CheckCode.VEHICLE_OFFLINE
    }

    def "test downgrade"() {
        when:
        CheckCode result = manualCarStatusCheck.downgrade()

        then:
        result == CheckCode.PASS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme