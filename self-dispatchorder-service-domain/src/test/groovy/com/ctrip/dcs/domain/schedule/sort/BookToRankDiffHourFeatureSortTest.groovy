package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.BookToRankDiffHourFeature
import com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverEmptyDistanceFeature
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class BookToRankDiffHourFeatureSortTest extends Specification {
    @Mock
    SortModel model
    @Mock
    SortContext context
    @Mock
    DriverVO driver
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverMileageProfitVO dmp
    @Mock
    CarVO car

    @InjectMocks
    BookToRankDiffHourFeature feature = new BookToRankDiffHourFeature()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(model.getModel()).thenReturn(dspModelVO)
        Mockito.when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getDspOrder()).thenReturn(dspOrderVO)
    }

    @Unroll
    def "test value"() {
        given:
        DriverRelateOrderVO relateOrder = Mock()

        Mockito.when(dspOrderVO.getEstimatedUseTimeBj()).thenReturn(DateUtil.addHours(new Date(),3))



        when:
        Value res = feature.value(model, context)

        then:
        res.value > 2.0


    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme