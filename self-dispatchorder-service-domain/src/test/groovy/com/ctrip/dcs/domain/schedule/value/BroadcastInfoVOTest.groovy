package com.ctrip.dcs.domain.schedule.value

import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.util.JsonUtil
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import spock.lang.*

/**
 * <AUTHOR>
 */
class BroadcastInfoVOTest extends Specification {

    @Unroll
    def "test"() {
        given:
        TransportGroupVO transportGroup = new TransportGroupVO()
        transportGroup.setTransportGroupId(1L)
        transportGroup.setTransportGroupMode(TransportGroupMode.MANUAL_DISPATCH)
        transportGroup.setTransportGroupName("name")
        SupplierVO supplier = new SupplierVO(1L)
        BroadcastInfoVO vo = new BroadcastInfoVO("123", "321", transportGroup, supplier)
        when:
        String json = JsonUtil.toJson(vo)

        then:
        json == "{\"orderMatchType\":\"SINGLE\",\"pduid\":\"321\",\"sduid\":\"\",\"transportGroupId\":1,\"transportGroupName\":\"name\",\"transportGroupMode\":1003,\"supplierId\":1,\"extendInfo\":\"{\\\"supplierId\\\":1}\",\"pOrderId\":\"123\",\"sOrderId\":\"\"}"

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme