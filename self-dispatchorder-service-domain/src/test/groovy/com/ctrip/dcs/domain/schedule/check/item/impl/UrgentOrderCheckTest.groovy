package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class UrgentOrderCheckTest extends Specification {

    UrgentOrderCheck check = new UrgentOrderCheck()

    def "Check"() {
        given:
        when:
        CheckCode code = check.check(new CheckModel(new DspModelVO(new DspOrderVO(categoryCode: CategoryCodeEnum.C_DAY_RENTAL, rushOrder: 1))), new CheckContext())
        then:
        code == CheckCode.PASS
    }
}
