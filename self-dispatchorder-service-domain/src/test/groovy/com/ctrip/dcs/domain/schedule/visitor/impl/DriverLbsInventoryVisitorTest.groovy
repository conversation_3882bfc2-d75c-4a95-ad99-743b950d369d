package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.value.DrvInventoryCheckResultVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyBoolean
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverLbsInventoryVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    SortContext sortContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO order
    @Mock
    DriverVO driverVO
    @Mock
    ConflictGateway conflictGateway
    @InjectMocks
    DriverLbsInventoryVisitor driverLbsInventoryVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(conflictGateway.checkDriverOrderConflict(any(), any(), anyBoolean())).thenReturn([(1l): new DrvInventoryCheckResultVO(1l, "checkResultCode", "checkResultDesc")])
        DriverLbsInventoryVisitor driverLbsInventoryVisitor = new DriverLbsInventoryVisitor(order, [driverVO], conflictGateway)

        when:
        driverLbsInventoryVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }

    def "test visit 2"() {
        given:
        when(driverVO.getDriverId()).thenReturn(1L)
        when(checkContext.getDrvInventoryCheckResModelMap()).thenReturn([1L:new DrvInventoryCheckResultVO(1L, "", "")])
        when(conflictGateway.checkDriverOrderConflict(any(), any(), anyBoolean())).thenReturn([(1l): new DrvInventoryCheckResultVO(1l, "checkResultCode", "checkResultDesc")])
        DriverLbsInventoryVisitor driverLbsInventoryVisitor = new DriverLbsInventoryVisitor(order, [driverVO], conflictGateway)

        when:
        driverLbsInventoryVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme