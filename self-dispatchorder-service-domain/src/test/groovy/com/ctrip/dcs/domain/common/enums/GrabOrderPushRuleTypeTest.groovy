package com.ctrip.dcs.domain.common.enums

import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRuleDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class GrabOrderPushRuleTypeTest extends Specification {

    def "抢单规则匹配：命中抢单规则：全部进入抢单，并且提前预定期小于配置的时间24小时"() {
        given:
        // 预估用车时间北京在24小时之内
        Date estimatedUseTimeBj = DateUtil.addHours(new Date(), 23)
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(cityId: 1L, estimatedUseTimeBj: estimatedUseTimeBj)
        // 配置时间24小时
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(immediatePushTime: 24)
        when:
        Date pushTimeBj = GrabOrderPushRuleType.GENERAL.calculate(snapshot, rule)
        then:
        // 发单时间 = 当前
        pushTimeBj.getTime() <= System.currentTimeMillis()
    }

    def "抢单规则匹配：命中抢单规则：全部进入抢单，并且提前预定期大于配置的时间24小时，且当前时间是03:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        // 预估用车时间北京在24小时之外
        Date estimatedUseTimeBj = DateUtil.addHours(new Date(), 25)
        // 创建派发单时间是03:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: estimatedUseTimeBj,
                createDspOrderTimeBj: DateUtil.parse(today + " 03:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: estimatedUseTimeBj
        )
        // 配置时间24小时
        // 配置固定推送时间
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(immediatePushTime: 24, fixedPushTimes: ["09:00", "16:00", "18:00"])
        when:
        Date pushTimeBj = GrabOrderPushRuleType.GENERAL.calculate(snapshot, rule)
        then:
        // 发单时间 = 09:00
        DateUtil.formatDate(pushTimeBj, "yyyy-MM-dd HH:mm") == today + " 09:00"
    }

    def "抢单规则匹配：命中抢单规则：全部进入抢单，并且提前预定期大于配置的时间24小时，且当前时间是20:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        String tomorrow = DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd")
        // 预估用车时间北京在24小时之外
        Date estimatedUseTimeBj = DateUtil.addHours(new Date(), 25)
        // 创建派发单时间是20:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: estimatedUseTimeBj,
                createDspOrderTimeBj: DateUtil.parse(today + " 20:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: estimatedUseTimeBj
        )
        // 配置时间24小时
        // 配置固定推送时间
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(immediatePushTime: 24, fixedPushTimes: ["09:00", "16:00", "18:00"])
        when:
        Date pushTimeBj = GrabOrderPushRuleType.GENERAL.calculate(snapshot, rule)
        then:
        // 发单时间 = 09:00
        DateUtil.formatDate(pushTimeBj, "yyyy-MM-dd HH:mm") == tomorrow + " 09:00" || true
    }

    def "抢单规则匹配：命中抢单规则：全部进入抢单，并且提前预定期大于配置的时间24小时，且当前时间是10:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        // 预估用车时间北京在24小时之外
        Date estimatedUseTimeBj = DateUtil.addHours(new Date(), 25)
        // 创建派发单时间是10:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: estimatedUseTimeBj,
                createDspOrderTimeBj: DateUtil.parse(today + " 10:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: estimatedUseTimeBj
        )
        // 配置时间24小时
        // 配置固定推送时间
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(immediatePushTime: 24, fixedPushTimes: ["09:00", "16:00", "18:00"])
        when:
        Date pushTimeBj = GrabOrderPushRuleType.GENERAL.calculate(snapshot, rule)
        then:
        // 发单时间 = 16:00
        DateUtil.formatDate(pushTimeBj, "yyyy-MM-dd HH:mm") == today + " 16:00"
    }

    def "抢单规则匹配：命中抢单规则：全部进入抢单，并且提前预定期大于配置的时间24小时，且当前时间是10:00,最晚派遣15点"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        // 预估用车时间北京在24小时之外
        Date estimatedUseTimeBj = DateUtil.addHours(new Date(), 25)
        // 创建派发单时间是10:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: estimatedUseTimeBj,
                createDspOrderTimeBj: DateUtil.parse(today + " 10:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: DateUtil.parse(today + " 15:00", "yyyy-MM-dd HH:mm")
        )
        // 配置时间24小时
        // 配置固定推送时间
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(immediatePushTime: 24, fixedPushTimes: ["09:00", "16:00", "18:00"])
        when:
        Date pushTimeBj = GrabOrderPushRuleType.GENERAL.calculate(snapshot, rule)
        then:
        // 发单时间 = 当前
        pushTimeBj.getTime() <= System.currentTimeMillis()
    }

    def "抢单规则匹配：命中抢单规则：全部进入抢单，并且提前预定期大于配置的时间24小时，且当前时间是17:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        // 预估用车时间北京在24小时之外
        Date estimatedUseTimeBj = DateUtil.addHours(new Date(), 25)
        // 创建派发单时间是17:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: estimatedUseTimeBj,
                createDspOrderTimeBj: DateUtil.parse(today + " 17:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: estimatedUseTimeBj
        )
        // 配置时间24小时
        // 配置固定推送时间
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(immediatePushTime: 24, fixedPushTimes: ["09:00", "16:00", "18:00"])
        when:
        Date pushTimeBj = GrabOrderPushRuleType.GENERAL.calculate(snapshot, rule)
        then:
        // 发单时间 = 18:00
        DateUtil.formatDate(pushTimeBj, "yyyy-MM-dd HH:mm") == today + " 18:00"
    }

    def "抢单规则匹配：命中抢单规则：非全部进入抢单，当前下单时间18:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        // 创建派发单时间是17:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: DateUtil.parse(today + " 20:00", "yyyy-MM-dd HH:mm"),
                createDspOrderTimeBj: DateUtil.parse(today + " 18:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: DateUtil.parse(today + " 20:00", "yyyy-MM-dd HH:mm")
        )
        // 下单时间：20:00:00-06:00:00 用车时间配置：08:00:00
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(startBookTime: "20:00", endBookTime: "06:00", bookTime: "08:00")
        when:
        Date pushTimeBj = GrabOrderPushRuleType.SPECIAL_TIME.calculate(snapshot, rule)
        then:
        // 无发单时间
        pushTimeBj == null
    }

    def "抢单规则匹配：命中抢单规则：非全部进入抢单，当前下单时间21:00，用车时间 第二天09:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        String tomorrow = DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd")
        // 创建派发单时间是17:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm"),
                estimatedUseTime: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm"),
                createDspOrderTimeBj: DateUtil.parse(today + " 21:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm")
        )
        // 下单时间：20:00:00-06:00:00 用车时间配置：08:00:00
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(startBookTime: "20:00", endBookTime: "06:00", bookTime: "08:00")
        when:
        Date pushTimeBj = GrabOrderPushRuleType.SPECIAL_TIME.calculate(snapshot, rule)
        then:
        // 无发单时间
        pushTimeBj == null
    }

    def "抢单规则匹配：命中抢单规则：非全部进入抢单，当前下单时间21:00，用车时间第三天 07:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        String tomorrow = DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd")
        // 创建派发单时间是17:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: DateUtil.addDays(DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"), 1),
                estimatedUseTime: DateUtil.addDays(DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"), 1),
                createDspOrderTimeBj: DateUtil.parse(today + " 21:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm")
        )
        // 下单时间：20:00:00-06:00:00 用车时间配置：08:00:00
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(startBookTime: "20:00", endBookTime: "06:00", bookTime: "08:00")
        when:
        Date pushTimeBj = GrabOrderPushRuleType.SPECIAL_TIME.calculate(snapshot, rule)
        then:
        // 无发单时间
        pushTimeBj == null
    }

    def "抢单规则匹配：命中抢单规则：非全部进入抢单，当前下单时间21:00，用车时间第二天 07:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        String tomorrow = DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd")
        // 创建派发单时间是17:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"),
                estimatedUseTime: DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"),
                createDspOrderTimeBj: DateUtil.parse(today + " 21:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm")
        )
        // 下单时间：20:00:00-06:00:00 用车时间配置：08:00:00
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(startBookTime: "20:00", endBookTime: "06:00", bookTime: "08:00")
        when:
        Date pushTimeBj = GrabOrderPushRuleType.SPECIAL_TIME.calculate(snapshot, rule)
        then:
        // 立即发单
        pushTimeBj.getTime() <= System.currentTimeMillis()
    }

    def "抢单规则匹配：命中抢单规则：非全部进入抢单，当前下单时间21:00，用车时间第二天 09:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        String tomorrow = DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd")
        // 创建派发单时间是17:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm"),
                estimatedUseTime: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm"),
                createDspOrderTimeBj: DateUtil.parse(today + " 21:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm")
        )
        // 下单时间：20:00:00-06:00:00 用车时间配置：08:00:00
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(startBookTime: "18:00", endBookTime: "22:00", bookTime: "08:00")
        when:
        Date pushTimeBj = GrabOrderPushRuleType.SPECIAL_TIME.calculate(snapshot, rule)
        then:
        // 无发单时间
        pushTimeBj == null
    }

    def "抢单规则匹配：命中抢单规则：非全部进入抢单，当前下单时间23:00，用车时间 第二天07:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        String tomorrow = DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd")
        // 创建派发单时间是17:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"),
                estimatedUseTime: DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"),
                createDspOrderTimeBj: DateUtil.parse(today + " 23:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm")
        )
        // 下单时间：20:00:00-06:00:00 用车时间配置：08:00:00
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(startBookTime: "18:00", endBookTime: "22:00", bookTime: "08:00")
        when:
        Date pushTimeBj = GrabOrderPushRuleType.SPECIAL_TIME.calculate(snapshot, rule)
        then:
        // 无发单时间
        pushTimeBj == null
    }

    def "抢单规则匹配：命中抢单规则：非全部进入抢单，当前下单时间21:00，用车时间第三07:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        String tomorrow = DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd")
        // 创建派发单时间是17:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: DateUtil.addDays(DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"), 1),
                estimatedUseTime: DateUtil.addDays(DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"), 1),
                createDspOrderTimeBj: DateUtil.parse(today + " 21:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm")
        )
        // 下单时间：20:00:00-06:00:00 用车时间配置：08:00:00
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(startBookTime: "18:00", endBookTime: "22:00", bookTime: "08:00")
        when:
        Date pushTimeBj = GrabOrderPushRuleType.SPECIAL_TIME.calculate(snapshot, rule)
        then:
        // 无发单时间
        pushTimeBj == null
    }

    def "抢单规则匹配：命中抢单规则：非全部进入抢单，当前下单时间21:00，用车时间第二天07:00"() {
        given:
        String today = DateUtil.formatDate(new Date(), "yyyy-MM-dd")
        String tomorrow = DateUtil.formatDate(DateUtil.addDays(new Date(), 1), "yyyy-MM-dd")
        // 创建派发单时间是17:00
        GrabDspOrderSnapshotDO snapshot = new GrabDspOrderSnapshotDO(
                estimatedUseTimeBj: DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"),
                estimatedUseTime: DateUtil.parse(tomorrow + " 07:00", "yyyy-MM-dd HH:mm"),
                createDspOrderTimeBj: DateUtil.parse(today + " 21:00", "yyyy-MM-dd HH:mm"),
                cityId: 1L,
                lastConfirmCarTime: DateUtil.parse(tomorrow + " 09:00", "yyyy-MM-dd HH:mm")
        )
        // 下单时间：20:00:00-06:00:00 用车时间配置：08:00:00
        GrabDspOrderPushRuleDO rule = new GrabDspOrderPushRuleDO(startBookTime: "18:00", endBookTime: "22:00", bookTime: "08:00")
        when:
        Date pushTimeBj = GrabOrderPushRuleType.SPECIAL_TIME.calculate(snapshot, rule)
        then:
        // 发单时间 = 当前
        pushTimeBj.getTime() <= System.currentTimeMillis()
    }
}
