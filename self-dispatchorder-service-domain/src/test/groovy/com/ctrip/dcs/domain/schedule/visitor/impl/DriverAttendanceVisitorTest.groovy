package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.repository.DriverAttendanceRepository
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyList
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverAttendanceVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    SortContext sortContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO order
    @Mock
    DriverVO driver
    @Mock
    DriverWorkTimeVO driverWorkTimeVO
    @Mock
    QueryDspOrderService queryDspOrderService
    @Mock
    ConfigService commonConfConfig
    @Mock
    DriverAttendanceRepository driverAttendanceRepository;
    @InjectMocks
    DriverAttendanceVisitor driverAttendanceVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(sortContext.getDspContext()).thenReturn(dspContext)
        when(checkContext.getContext()).thenReturn(dspContext)
        Map<String,Integer> map = Maps.newHashMap();
        map.put("1",12)
        when(driverAttendanceRepository.queryDriverAttendance(anyList(), any() as Date)).thenReturn(map)
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 57).getTime())
        when(queryDspOrderService.queryOrderList(anyList() as List<DriverVO>, any() as Date, any() as Date, anyList() as List<OrderStatusEnum>)).thenReturn([(1l): [order]])
        DriverAttendanceVisitor driverAttendanceVisitor = new DriverAttendanceVisitor([driver], queryDspOrderService, driverAttendanceRepository, "1")
        when:
        def result = driverAttendanceVisitor.visit(sortContext)

        then:
        result == null
    }

    def "test visit 2"() {
        given:
        when(sortContext.getDspContext()).thenReturn(dspContext)
        when(driver.findHitWorkTime(order)).thenReturn(driverWorkTimeVO)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 57).getTime())
        when(queryDspOrderService.queryOrderList(anyList() as List<DriverVO>, any() as Date, any() as Date, anyList() as List<OrderStatusEnum>)).thenReturn([(1l): [order]])
        DriverAttendanceVisitor driverAttendanceVisitor = new DriverAttendanceVisitor([driver], queryDspOrderService, driverAttendanceRepository, null)
        when:
        def result = driverAttendanceVisitor.visit(sortContext)

        then:
        result == null
    }

    def "test query Order List"() {
        given:
        when(sortContext.getDspContext()).thenReturn(dspContext)
        when(driverWorkTimeVO.getStart()).thenReturn(new Date())
        when(commonConfConfig.getString(anyString(),anyString())).thenReturn("1")
        when(driverWorkTimeVO.getEnd()).thenReturn(new Date())
        when(driver.findHitWorkTime(order)).thenReturn(driverWorkTimeVO)
        when(checkContext.getContext()).thenReturn(dspContext)
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 57).getTime())
        when(queryDspOrderService.queryOrderList(anyList() as List<DriverVO>, any() as Date, any() as Date, anyList() as List<OrderStatusEnum>)).thenReturn([(1l): [order]])
        DriverAttendanceVisitor driverAttendanceVisitor = new DriverAttendanceVisitor([driver], queryDspOrderService, driverAttendanceRepository, "1")
        when:
        def result = driverAttendanceVisitor.visit(sortContext)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme