package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class CarStatusCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @InjectMocks
    CarStatusCheck carStatusCheck = new CarStatusCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "司机状态信息为null"() {
        given:
        Mockito.when(driverInfo.getVehicleStatus()).thenReturn(null)

        when:
        CheckCode result = carStatusCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机状态信息为上线"() {
        given:
        Mockito.when(driverInfo.getVehicleStatus()).thenReturn(1)

        when:
        CheckCode result = carStatusCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机状态信息为下线"() {
        given:
        Mockito.when(driverInfo.getVehicleStatus()).thenReturn(3)

        when:
        CheckCode result = carStatusCheck.check(checkModel, context)

        then:
        result == CheckCode.VEHICLE_OFFLINE
    }

    def "test check"() {
        when:
        CheckCode result = carStatusCheck.check(checkModel, null)

        then:
        result == res

        where:
        checkModel                                                                                   || res
        new CheckModel(new DspModelVO(new DspOrderVO(), new DriverVO("driverId": 1234L, "vehicleStatus": 1)))    || CheckCode.PASS
        new CheckModel(new DspModelVO(new DspOrderVO(), new DriverVO("driverId": 1234L, "vehicleStatus": null))) || CheckCode.PASS
        new CheckModel(new DspModelVO(new DspOrderVO(), new DriverVO("driverId": 1234L, "vehicleStatus": 0)))    || CheckCode.VEHICLE_NOT_ACTIVE


    }

    def "test downgrade"() {
        when:
        CheckCode result = carStatusCheck.downgrade()

        then:
        result == CheckCode.PASS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme