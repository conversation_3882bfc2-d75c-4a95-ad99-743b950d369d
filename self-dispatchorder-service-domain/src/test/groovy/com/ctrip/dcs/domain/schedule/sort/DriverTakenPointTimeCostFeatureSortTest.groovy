package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverMileageProfitVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverTakenPointTimeCostFeature
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakenCostVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class DriverTakenPointTimeCostFeatureSortTest extends Specification {
    @Mock
    SortModel model
    @Mock
    SortContext context
    @Mock
    DriverVO driver
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverMileageProfitVO dmp
    @Mock
    DriverMileageProfitVO dmp1
    @Mock
    CarVO car

    @InjectMocks
    DriverTakenPointTimeCostFeature feature = new DriverTakenPointTimeCostFeature()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(model.getModel()).thenReturn(dspModelVO)
        Mockito.when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getDspOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getExpectDriverMileageProfit(Mockito.any())).thenReturn(dmp)
        Mockito.when(context.getCompleteDriverMileageProfit(Mockito.any())).thenReturn(dmp1)

        Mockito.when(dspModelVO.getDriver()).thenReturn(driver)
        Mockito.when(driver.getDriverId()).thenReturn(1L)
        Mockito.when(driver.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
    }

    @Unroll
    def "test value"() {
        given:
        TakenCostVO point = new TakenCostVO()
        point.setDrvId(1)
        point.setkCost(new BigDecimal("10"))
        point.settCost(new BigDecimal("20"))
        Optional<TakenCostVO> optional = Optional.of(point)

        Mockito.when(context.getDriverTakenCost(Mockito.any())).thenReturn(optional)


        when:
        Value res = feature.value(model, context)

        then:
        res.value == 20.0


    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme