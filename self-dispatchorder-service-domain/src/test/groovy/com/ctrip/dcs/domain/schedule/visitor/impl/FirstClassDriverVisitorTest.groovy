package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.FirstClassDriverGateway
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class FirstClassDriverVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    FirstClassDriverGateway firstClassDriverGateway
    @InjectMocks
    FirstClassDriverVisitor firstClassDriverVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(dspOrder.getCityId()).thenReturn(0)
        when(dspOrder.getCarTypeId()).thenReturn(0)
        when(dspOrder.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 16).getTime())
        when(firstClassDriverGateway.queryFirstClassDriver(anyInt(), anyInt(), any())).thenReturn([1l])

        when:
        firstClassDriverVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }

    def "test visit 2"() {
        given:
        when(checkContext.getFirstClassDriverList()).thenReturn([1])
        when(dspOrder.getCityId()).thenReturn(0)
        when(dspOrder.getCarTypeId()).thenReturn(0)
        when(dspOrder.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 15, 16).getTime())
        when(firstClassDriverGateway.queryFirstClassDriver(anyInt(), anyInt(), any())).thenReturn([1l])

        when:
        firstClassDriverVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme