package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverLocationVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverActiveCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    ConfigService configService
    @Mock
    DriverLocationVO driverLocationVO
    @Mock
    DriverLocationVO.LocationVO locationVO
    @InjectMocks
    DriverActiveCheck driverActiveCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(context.getConfigService()).thenReturn(configService)
    }

    def "司机无点位"() {
        given:


        when:
        CheckCode result = driverActiveCheck.check(checkModel, context)

        then:
        result == CheckCode.NO_ACTIVE
    }

    def "司机有点位，3分钟内"() {
        given:
        Mockito.when(context.getDriverLocation(Mockito.any())).thenReturn(Optional.of(driverLocationVO))
        Mockito.when(driverLocationVO.getLocations()).thenReturn(Lists.newArrayList(locationVO))
        Mockito.when(locationVO.getLocationTimestamp()).thenReturn(System.currentTimeMillis())
        Mockito.when(configService.getInteger(Mockito.any(), Mockito.any())).thenReturn(3)

        when:
        CheckCode result = driverActiveCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机有点位，3分钟外"() {
        given:
        Mockito.when(context.getDriverLocation(Mockito.any())).thenReturn(Optional.of(driverLocationVO))
        Mockito.when(driverLocationVO.getLocations()).thenReturn(Lists.newArrayList(locationVO))
        Mockito.when(locationVO.getLocationTimestamp()).thenReturn(System.currentTimeMillis() - 300 * 1000)
        Mockito.when(configService.getInteger(Mockito.any(), Mockito.any())).thenReturn(3)

        when:
        CheckCode result = driverActiveCheck.check(checkModel, context)

        then:
        result == CheckCode.NO_ACTIVE
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme