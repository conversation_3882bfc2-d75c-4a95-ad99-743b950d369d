package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.service.HighLevelCheckService
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.self.dispatchorder.interfaces.GoldDriverInfo
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class GoldDriverInfoVisitorTest extends Specification {
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DriverVO driverVO
    @Mock
    CarVO carVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    HighLevelCheckService highLevelCheckService
    @Mock
    CheckContext checkContext

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(highLevelCheckService.checkHighLevelDriver(any())).thenReturn([new GoldDriverInfo(driverId: 1, goldDriver: true)])
        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.FROM_AIRPORT)
        when(driverVO.getDriverId()).thenReturn(1L)
        when(driverVO.getCar()).thenReturn(carVO)
        when(carVO.getCarTypeId()).thenReturn(117L)
        when:
        GoldDriverInfoVisitor visitor = new GoldDriverInfoVisitor([checkModel], highLevelCheckService)
        def result = visitor.visit(checkContext)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme