package com.ctrip.dcs.domain.dsporder.value


import spock.lang.Specification

class CommonDistributionChannelVOTest extends Specification {
    CommonDistributionChannelVO commonDistributionChannelVO = new CommonDistributionChannelVO()

    def "test business Travel Black List"() {
        when:
        List<String> result = commonDistributionChannelVO.businessTravelBlackList(null)

        then:
        result.isEmpty()
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme