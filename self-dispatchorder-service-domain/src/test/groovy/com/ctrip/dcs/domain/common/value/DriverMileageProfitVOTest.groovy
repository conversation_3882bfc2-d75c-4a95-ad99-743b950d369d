package com.ctrip.dcs.domain.common.value

import com.ctrip.dcs.domain.common.util.JsonUtil
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverMileageProfitVOTest extends Specification {
    DriverMileageProfitVO driverMileageProfitVO = new DriverMileageProfitVO(1l, 0, 0, 0, 0d, 0d, 0d, 0d)

    def "test expect"() {
        given:
        def dspOrderVO = DspOrderVO.builder().costAmount(BigDecimal.valueOf(0L)).estimatedKm(BigDecimal.valueOf(0l)).build()
        when:
        double result = driverMileageProfitVO.expect(dspOrderVO, 0D, 0d, 0d)

        then:
        result == 0d
    }

    def "test json"() {
        given:
        def json = "{\"driverId\":1026187,\"highPriorityOrderCounts\":0,\"mediumPriorityOrderCounts\":0,\"orderCounts\":0,\"orderMileage\":0.0,\"emptyMileage\":12.940999999999999,\"income\":0.0,\"profit\":-6.4704999999999995}"
        when:
        def result = JsonUtil.fromJson(json, DriverMileageProfitVO.class)
        then:
        with(result) {
            driverId == 1026187
            highPriorityOrderCounts == 0
            mediumPriorityOrderCounts == 0
            orderCounts == 0
            orderMileage == 0.0
            emptyMileage == 12.940999999999999
            income == 0.0
            profit == -6.4704999999999995
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme