package com.ctrip.dcs.domain.common.value

import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import spock.lang.Specification
import spock.lang.Unroll

class TransportGroupsVOTest extends Specification {

    @Unroll
    def "test type"() {
        given:
        def transportGroupsVO = new TransportGroupsVO(transportGroups)

        when:
        DriverTypeVO result = transportGroupsVO.type()

        then:
        result == r

        where:
        transportGroups                                                                                                                                                    || r
        null                                                                                                                                                               || DriverTypeVO.PART_TIME
        []                                                                                                                                                                 || DriverTypeVO.PART_TIME
        [null]                                                                                                                                                             || DriverTypeVO.PART_TIME
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST)]                                                                                 || DriverTypeVO.PART_TIME
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)]  || DriverTypeVO.FULL_TIME
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.REGISTER_DISPATCH)] || DriverTypeVO.FULL_TIME
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme