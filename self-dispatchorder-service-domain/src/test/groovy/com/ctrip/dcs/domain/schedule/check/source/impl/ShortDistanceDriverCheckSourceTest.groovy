package com.ctrip.dcs.domain.schedule.check.source.impl

import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckConfig
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.google.common.collect.Lists
import spock.lang.Specification

class ShortDistanceDriverCheckSourceTest extends Specification {


    def dspOrder = Mock(DspOrderVO)
    def dspContextService = Mock(DspContextService)
    def checkContext = Mock(CheckContext)
    def dspContext = Mock(DspContext)
    def t1 = Mock(TransportGroupVO)

    def subSkuVO = Mock(SubSkuVO)
    def  checkConfig = Mock(CheckConfig)
    def checkSourceConfig = Mock(com.ctrip.dcs.domain.schedule.check.source.CheckSourceConfig)
    def shortDistanceTransportCheckSource = new ShortDistanceTransportGroupCheckSource()


    def "init source 0"() {
        given: "Mock数据"
        checkContext.getContext() >> dspContext
        checkContext.getDspOrder() >> dspOrder
        checkContext.getSubSku()>> subSkuVO
        subSkuVO.getCheck() >> checkConfig
        checkConfig.getCheckSourceConfig()>> checkSourceConfig
        checkSourceConfig.getTransportGroupMode() >> 1003
        dspOrder.getShortDisOrder() >> 0
        dspContext.getService() >> dspContextService
        t1.getTransportGroupId() >> 1L
        t1.getSupplierId()>> 1L
        dspContextService.queryTransports(_) >> Lists.newArrayList(t1)
        DriverVO driver = new DriverVO(driverId: 1L, transportGroups: [t1])
        dspContextService.queryValidateTransportSupplier(_,_)>> Lists.newArrayList(1L)
        dspContextService.queryDriversByTransportGroupIds(_,_,_,_) >> Lists.newArrayList(driver)
        when: "执行校验方法"
        def driverSource = shortDistanceTransportCheckSource.init(checkContext)

        then: "验证校验结果"
        driverSource.size() == 0
    }

    def "init source 1"() {
        given: "Mock数据"
        checkContext.getContext() >> dspContext
        checkContext.getDspOrder() >> dspOrder
        dspOrder.getShortDisOrder() >> 1
        dspContext.getService() >> dspContextService
        t1.getTransportGroupId() >> 1L
        t1.getSupplierId()>> 1L
        dspContextService.queryTransports(_) >> Lists.newArrayList(t1)
        DriverVO driver = new DriverVO(driverId: 1L, transportGroups: [t1])
        dspContextService.queryValidateTransportSupplier(_,_)>> Lists.newArrayList(1L)
        dspContextService.queryDriversByTransportGroupIds(_,_,_,_) >> Lists.newArrayList(driver)
        when: "执行校验方法"
        def driverSource = shortDistanceTransportCheckSource.init(checkContext)

        then: "验证校验结果"
        driverSource.size() == 0
    }

    def "init source 2"() {
        given: "Mock数据"
        checkContext.getContext() >> dspContext
        checkContext.getDspOrder() >> dspOrder
        checkContext.getSubSku()>> subSkuVO
        subSkuVO.getCheck() >> checkConfig
        checkConfig.getCheckSourceConfig()>> checkSourceConfig
        checkSourceConfig.getTransportGroupMode() >> 1003
        dspOrder.getShortDisOrder() >> 1
        dspContext.getService() >> dspContextService
        t1.getTransportGroupId() >> 1L
        t1.getShortDisSwitch()>> 1
        t1.getSupplierId()>> 1L
        t1.getTransportGroupMode()>> TransportGroupMode.MANUAL_DISPATCH
        dspContextService.queryTransports(_) >> Lists.newArrayList(t1)
        dspContextService.queryValidateTransportSupplier(_,_)>> Lists.newArrayList(1L)
        DriverVO driver = new DriverVO(driverId: 1L, transportGroups: [t1])

        dspContextService.queryDriversByTransportGroupIds(_,_,_,_) >> Lists.newArrayList(driver)
        when: "执行校验方法"
        def driverSource = shortDistanceTransportCheckSource.init(checkContext)

        then: "验证校验结果"
        driverSource.size() == 1
    }

    def "init source 3"() {
        given: "Mock数据"
        checkContext.getContext() >> dspContext
        checkContext.getDspOrder() >> dspOrder
        checkContext.getSubSku()>> subSkuVO
        subSkuVO.getCheck() >> checkConfig
        checkConfig.getCheckSourceConfig()>> checkSourceConfig
        checkSourceConfig.getTransportGroupMode() >> 1002
        dspOrder.getShortDisOrder() >> 1
        dspContext.getService() >> dspContextService
        t1.getTransportGroupId() >> 1L
        t1.getShortDisSwitch()>> 1
        t1.getSupplierId()>> 1L
        t1.getTransportGroupMode()>> TransportGroupMode.MANUAL_DISPATCH
        dspContextService.queryTransports(_) >> Lists.newArrayList(t1)
        dspContextService.queryValidateTransportSupplier(_,_)>> Lists.newArrayList(1L)
        DriverVO driver = new DriverVO(driverId: 1L, transportGroups: [t1])

        dspContextService.queryDriversByTransportGroupIds(_,_,_,_) >> Lists.newArrayList(driver)
        when: "执行校验方法"
        def driverSource = shortDistanceTransportCheckSource.init(checkContext)

        then: "验证校验结果"
        driverSource.size() == 0
    }


}
