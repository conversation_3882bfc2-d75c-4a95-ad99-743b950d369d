package com.ctrip.dcs.domain.schedule.check.item.impl


import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.carconfig.HeadTailLimitValueVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.ImmutableMap
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class HeadTailOrderCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    HeadTailLimitValueVO limitValue
    @Mock
    DriverHeadTailOrderVO driverHeadTailOrder
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @InjectMocks
    HeadTailOrderCheck headTailOrderCheck = new HeadTailOrderCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "首单，符合首单时间策略"() {
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(context.getDriverHeadTailOrderVOMap()).thenReturn(ImmutableMap.builder().put(1L, driverHeadTailOrder).build())
        Mockito.when(context.queryHeadTailLimitValue(Mockito.any(), Mockito.any())).thenReturn(limitValue)
        Mockito.when(driverHeadTailOrder.isHead()).thenReturn(true)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)


        Mockito.when(order.getEstimatedUseTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 18:00:00"))
        Mockito.when(driverHeadTailOrder.getBeginWorkTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 17:00:00"))
        Mockito.when(driverHeadTailOrder.getHeadLbsDuration()).thenReturn(60D*60)
        Mockito.when(limitValue.getHeadLimit()).thenReturn(30D)

        when:
        CheckCode result = headTailOrderCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "首单，不符合首单时间策略"() {
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(context.getDriverHeadTailOrderVOMap()).thenReturn(ImmutableMap.builder().put(1L, driverHeadTailOrder).build())
        Mockito.when(context.queryHeadTailLimitValue(Mockito.any(), Mockito.any())).thenReturn(limitValue)
        Mockito.when(driverHeadTailOrder.isHead()).thenReturn(true)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)


        Mockito.when(order.getEstimatedUseTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 18:00:00"))
        Mockito.when(driverHeadTailOrder.getBeginWorkTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 17:00:00"))
        Mockito.when(driverHeadTailOrder.getHeadLbsDuration()).thenReturn(100D*60)
        Mockito.when(limitValue.getHeadLimit()).thenReturn(30D)

        when:
        CheckCode result = headTailOrderCheck.check(checkModel, context)

        then:
        result == CheckCode.HEAD_ORDER_LIMIT
    }

    def "尾单，符合尾单时间策略"() {
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(context.getDriverHeadTailOrderVOMap()).thenReturn(ImmutableMap.builder().put(1L, driverHeadTailOrder).build())
        Mockito.when(context.queryHeadTailLimitValue(Mockito.any(), Mockito.any())).thenReturn(limitValue)
        Mockito.when(driverHeadTailOrder.isHead()).thenReturn(false)
        Mockito.when(driverHeadTailOrder.isTail()).thenReturn(true)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)


        Mockito.when(order.getPredicServiceStopTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 18:00:00"))
        Mockito.when(driverHeadTailOrder.getEndWorkTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 19:00:00"))
        Mockito.when(driverHeadTailOrder.getTailLbsDuration()).thenReturn(600D)
        Mockito.when(limitValue.getTailLimit()).thenReturn(30D)

        when:
        CheckCode result = headTailOrderCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "尾单，不符合尾单时间策略"() {
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(context.getDriverHeadTailOrderVOMap()).thenReturn(ImmutableMap.builder().put(1L, driverHeadTailOrder).build())
        Mockito.when(context.queryHeadTailLimitValue(Mockito.any(), Mockito.any())).thenReturn(limitValue)
        Mockito.when(driverHeadTailOrder.isHead()).thenReturn(false)
        Mockito.when(driverHeadTailOrder.isTail()).thenReturn(true)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)


        Mockito.when(order.getPredicServiceStopTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 18:00:00"))
        Mockito.when(driverHeadTailOrder.getEndWorkTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-13 19:00:00"))
        Mockito.when(driverHeadTailOrder.getTailLbsDuration()).thenReturn(100D*60)
        Mockito.when(limitValue.getTailLimit()).thenReturn(30D)

        when:
        CheckCode result = headTailOrderCheck.check(checkModel, context)

        then:
        result == CheckCode.TAIL_ORDER_LIMIT
    }

    def "test check"() {
        given:
        def context = Mock(CheckContext)
        context.getDriverHeadTailOrderVOMap() >> resMap
        context.getSubSku() >> subSku
        context.queryHeadTailLimitValue(_, _) >> headTailLimitValue
        def mockOrder = new DspOrderVO(estimatedUseTime: DateUtil.parseDateStr2Date("2023-04-19 10:00:00"),
                predicServiceStopTime: DateUtil.parseDateStr2Date("2023-04-19 16:00:00"))
        def mockDriver = new DriverVO(driverId: driverId, coopMode: coopMode, cityId: 1, car: new CarVO(carTypeId: 117),
                transportGroups: Arrays.asList(new TransportGroupVO(transportGroupMode: TransportGroupMode.REGISTER_DISPATCH)))
        def checkModel = new CheckModel(model: new DspModelVO(mockOrder, mockDriver))

        when:
        CheckCode result = headTailOrderCheck.check(checkModel, context)
        then:
        result == checkRes

        where:
        resMap   | subSku                        | coopMode | driverId | headTailLimitValue   || checkRes
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 1        | 1L       | null                 || CheckCode.PASS
        getMap() | getSku(DspType.DELAY_ASSIGN)  | 4        | 2l       | null                 || CheckCode.PASS
        null     | getSku(DspType.SYSTEM_ASSIGN) | 4        | 2L       | null                 || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 3L       | null                 || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 1L       | getLimit(1d, 2d, 1d)     || CheckCode.HEAD_ORDER_LIMIT
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 1L       | getLimit(100d, 2d, 1d)   || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 1L       | getLimit(null, 2d, 1d)   || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 1L       | getLimit(-1d, 2d, 1d)    || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 1L       | null                 || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 2L       | getLimit(100d, 200d, 1d) || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 2L       | getLimit(1d, 2d, 1d)     || CheckCode.TAIL_ORDER_LIMIT
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 55L      | getLimit(1d, 2d, 1d)     || CheckCode.TAIL_ORDER_LIMIT
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 55L      | null                 || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 55L      | null                 || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 55L      | getLimit(1d, -1d, 1d)    || CheckCode.PASS
        getMap() | getSku(DspType.SYSTEM_ASSIGN) | 4        | 55L      | getLimit(1d, null, 1d)   || CheckCode.PASS
    }

    def getSku(DspType dspType) {
        return new SubSkuVO(dspType: dspType)
    }

    def getLimit(Double headLimit, Double tailLimit, Double tailMileageValue) {
        return new HeadTailLimitValueVO(headLimit, tailLimit, tailMileageValue,1D)
    }

    def getMap() {
        Map<Long, DriverHeadTailOrderVO> map = Maps.newHashMap();
        map.put(1L, new DriverHeadTailOrderVO(driverId: 1L, head: true, headRange: true,
                beginWorkTime: DateUtil.parseDateStr2Date("2023-04-19 10:00:00"),
                endWorkTime: DateUtil.parseDateStr2Date("2023-04-19 16:00:00"),
                headLbsDuration: 10d*60,
                tailLbsDuration: 11d*60,
                addressLatitude: BigDecimal.TEN,
                addressLongitude: BigDecimal.TEN));
        map.put(2L, new DriverHeadTailOrderVO(driverId: 2L, tail: true, tailRange: true,
                beginWorkTime: DateUtil.parseDateStr2Date("2023-04-19 10:00:00"),
                endWorkTime: DateUtil.parseDateStr2Date("2023-04-19 16:00:00"),
                headLbsDuration: 10d*60,
                tailLbsDuration: 11d*60,
                addressLatitude: BigDecimal.TEN,
                addressLongitude: BigDecimal.TEN
        ));
        map.put(55L, new DriverHeadTailOrderVO(driverId: 55L, tail: true, tailRange: true,
                beginWorkTime: DateUtil.parseDateStr2Date("2023-04-19 10:00:00"),
                endWorkTime: DateUtil.parseDateStr2Date("2023-04-19 15:00:00"),
                headLbsDuration: 10d*60,
                tailLbsDuration: 10000d,
                addressLatitude: BigDecimal.TEN,
                addressLongitude: BigDecimal.TEN
        ));
        return map

    }

    def "test downgrade"() {
        when:
        CheckCode result = headTailOrderCheck.downgrade()

        then:
        result == CheckCode.PASS
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme