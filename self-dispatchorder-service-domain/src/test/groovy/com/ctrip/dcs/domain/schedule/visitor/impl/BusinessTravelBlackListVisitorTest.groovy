package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.dsporder.gateway.ChannelGateway
import com.ctrip.dcs.domain.dsporder.value.BusinessTravelDistributionChannelVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.BusinessTravelServiceGateway
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class BusinessTravelBlackListVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO order
    @Mock
    DriverVO driverVO
    @Mock
    ChannelGateway channelGateway
    @Mock
    BusinessTravelServiceGateway businessTravelServiceGateway
    @InjectMocks
    BusinessTravelBlackListVisitor businessTravelBlackListVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(order.getUserOrderId()).thenReturn("getUserOrderIdResponse")
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 17, 9).getTime())
        when(order.getDistributionChannelId()).thenReturn(0)
        when(channelGateway.findDistributionChannel(anyInt())).thenReturn(true)
        when(businessTravelServiceGateway.queryBusinessTravelBlackList(anyString(), any())).thenReturn(["String"])
        BusinessTravelBlackListVisitor businessTravelBlackListVisitor = new BusinessTravelBlackListVisitor(order, channelGateway, businessTravelServiceGateway)

        when:
        businessTravelBlackListVisitor.visit(checkContext)

        then:
        checkContext !=null ==true
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme