package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.FixedLocationType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.exception.CheckItemValidateException
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.dcs.domain.schedule.value.TransportGroupOrderConfig
import com.google.common.collect.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

/**
 * UnitTest for the TransGroupOrderNumLimitWithBuffCheck
 *
 * <AUTHOR>
 */
class TransGroupOrderNumLimiWithBuffCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    TransportGroupVO transportGroup
    @InjectMocks
    TransGroupOrderNumLimiWithBuffCheck transGroupOrderNumLimiWithBuffCheck = new TransGroupOrderNumLimiWithBuffCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "司机运力组时段内接单数>=阈值"() {
        given:
        Mockito.when(context.transGroupRemainingInventory(Mockito.any())).thenReturn(0)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getTransportGroupId()).thenReturn(1L)

        when:
        CheckCode result = transGroupOrderNumLimiWithBuffCheck.check(checkModel, context)

        then:
        result == CheckCode.OVER_TRANS_GROUP_ORDERS_NUM_LIMIT
    }

    def "司机运力组时段内接单数<阈值"() {
        given:
        Mockito.when(context.transGroupRemainingInventory(Mockito.any())).thenReturn(1)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getTransportGroupId()).thenReturn(1L)

        when:
        CheckCode result = transGroupOrderNumLimiWithBuffCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check"() {
        given:
        def context = Mock(CheckContext)
        context.transGroupRemainingInventory(_) >> inventory
        context.getDspOrder() >> orderVO
        context.getProperties(_ as String,_) >> ratio
        def model = new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(transportGroupId: 1L, orderLimitConfigs: limitConfigs)))

        when:
        CheckCode result = transGroupOrderNumLimiWithBuffCheck.check(model, context)

        then:
        result == r

        where:
        inventory || ratio  || orderVO  || limitConfigs                            || r
        0         ||  0.75  || null     || getTransportGroupOrderConfig_null()     || CheckCode.OVER_TRANS_GROUP_ORDERS_NUM_LIMIT
        -1        ||  0.75  || null     || getTransportGroupOrderConfig_null()     || CheckCode.OVER_TRANS_GROUP_ORDERS_NUM_LIMIT
        1         ||  0.75  || null     || getTransportGroupOrderConfig_null()     || CheckCode.PASS
        1         ||  0.75  || null     || getTransportGroupOrderConfig_empty()    || CheckCode.PASS
        1         ||  0.75  || null     || getTransportGroupOrderConfig_NotEmpty() || CheckCode.OVER_TRANS_GROUP_ORDERS_NUM_LIMIT
        1         ||  0.75  || new DspOrderVO(categoryCode: CategoryCodeEnum.FROM_AIRPORT, targetId: "PEK", estimatedUseTime: DateUtil.parseDateStr2Date("2024-12-31 10:30")) || getTransportGroupOrderConfig_NotEmpty_NoLimit() || CheckCode.PASS
        1         ||  0.75  || new DspOrderVO(categoryCode: CategoryCodeEnum.FROM_AIRPORT, targetId: "PEK", estimatedUseTime: DateUtil.parseDateStr2Date("2024-12-31 10:30")) || getTransportGroupOrderConfig_NotEmpty() || CheckCode.OVER_TRANS_GROUP_ORDERS_NUM_LIMIT
        3         ||  0.75  || new DspOrderVO(categoryCode: CategoryCodeEnum.FROM_AIRPORT, targetId: "PEK", estimatedUseTime: DateUtil.parseDateStr2Date("2024-12-31 10:30")) || getTransportGroupOrderConfig_NotEmpty() || CheckCode.OVER_TRANS_GROUP_ORDERS_NUM_LIMIT
        4         ||  0.75  || new DspOrderVO(categoryCode: CategoryCodeEnum.FROM_AIRPORT, targetId: "PEK", estimatedUseTime: DateUtil.parseDateStr2Date("2024-12-31 10:30")) || getTransportGroupOrderConfig_NotEmpty() || CheckCode.PASS
    }

    def getTransportGroupOrderConfig_null() {
        return null;
    }
    def getTransportGroupOrderConfig_empty() {
        return new ArrayList<TransportGroupOrderConfig>();
    }
    def getTransportGroupOrderConfig_NotEmpty_NoLimit() {
        def limitConfigs = new ArrayList<TransportGroupOrderConfig>();
        TransportGroupOrderConfig config1 = new TransportGroupOrderConfig();
        config1.setActive(true)
        config1.setFixedLocationType(FixedLocationType.AIRPORT)
        config1.setLocationCode("PEK")
        config1.setConfigItems(Lists.newArrayList(new TransportGroupOrderConfig.OrderLimitByTime("22:00-23:59", 2)))
        limitConfigs.add(config1);

        return limitConfigs;
    }
    def getTransportGroupOrderConfig_NotEmpty() {
        def limitConfigs = new ArrayList<TransportGroupOrderConfig>();
        TransportGroupOrderConfig config1 = new TransportGroupOrderConfig();
        config1.setActive(true)
        config1.setFixedLocationType(FixedLocationType.AIRPORT)
        config1.setLocationCode("PEK")
        config1.setConfigItems(Lists.newArrayList(new TransportGroupOrderConfig.OrderLimitByTime("10:00-11:59", 10)))
        limitConfigs.add(config1);

        TransportGroupOrderConfig config2 = new TransportGroupOrderConfig();
        config2.setActive(false)
        config2.setFixedLocationType(FixedLocationType.AIRPORT)
        config2.setLocationCode("PEK")
        config2.setConfigItems(Lists.newArrayList(new TransportGroupOrderConfig.OrderLimitByTime("10:00-11:59", 10)))
        limitConfigs.add(config2);

        TransportGroupOrderConfig config3 = new TransportGroupOrderConfig();
        config3.setActive(true)
        config3.setFixedLocationType(FixedLocationType.AIRPORT)
        config3.setLocationCode("PKX")
        config3.setConfigItems(Lists.newArrayList(new TransportGroupOrderConfig.OrderLimitByTime("10:00-11:59", 10)))
        limitConfigs.add(config3);

        return limitConfigs;
    }

    def "test downgrade"() {
        when:
        CheckCode result = transGroupOrderNumLimiWithBuffCheck.downgrade()

        then:
        result == CheckCode.OVER_TRANS_GROUP_ORDERS_NUM_LIMIT
    }

    @Unroll
    def "test validate"() {
        when:
        transGroupOrderNumLimiWithBuffCheck.validate(checkModel, Mock(CheckContext))

        then:
        thrown(e)

        where:
        checkModel                                                                                                                                                                                    || e
        new CheckModel()                                                                                                                                                                           || CheckItemValidateException
        new CheckModel(model: new DspModelVO())                                                                                                                                                       || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO()))                                                                                                                 || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO())))                                                          || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(), transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN))) || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(), transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST))) || CheckItemValidateException
        new CheckModel(model: new DspModelVO(transportGroup: new TransportGroupVO(takeOrderLimitTime: new TakeOrderLimitTimeMinuteVO(), transportGroupMode: TransportGroupMode.REGISTER_DISPATCH)))   || CheckItemValidateException
    }
}
