package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.value.DriverRelateOrderVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.value.DriverPointsVO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class SortContextTest extends Specification {

    DspOrderVO dspOrder = Mock(DspOrderVO);
    DspContextService dspContextService = Mock(DspContextService);
    DspContext dspContext = new DspContext(dspContextService)
    ConfigService configService = Mock(ConfigService);

    SortContext context = new SortContext(dspOrder, dspContext, configService);

    def "test getTopDriverPoints lese"() {
        given:
        dspContext.getDriverPointsMap().put(1l, new DriverPointsVO(points: 1))
        when:
        List<DriverPointsVO> points = context.getTopDriverPoints(10)
        then:
        points.size() == 1
    }

    def "test getTopDriverPoints more"() {
        given:
        for (int i = 0; i < 15; i++) {
            dspContext.getDriverPointsMap().put(i, new DriverPointsVO(points: 1))
        }

        when:
        List<DriverPointsVO> points = context.getTopDriverPoints(10)
        then:
        points.size() == 10
    }

    def "test getDriverRelateOrder lese"() {
        given:
        dspContext.getRelateOrderMap().put(1l, new DriverRelateOrderVO(driverId: 1))
        when:
        DriverRelateOrderVO points = context.getDriverRelateOrder(1L)
        then:
        points !=null == true
    }

}
