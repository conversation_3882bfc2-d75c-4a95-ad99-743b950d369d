package com.ctrip.dcs.domain.schedule.sort.feature.impl

import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification

class DriverOrderConnectionFeatureTest extends Specification {

    def sortContext = Mock(SortContext)
    def dispatchContext = Mock(DspContext)
    var dispatchService = Mock(DspContextService)

    def "test(#description)"() {
        given:
        var target = new DriverOrderConnectionFeature()
        and:
        var driver = new DriverVO(driverId: driverId)
        var dispatchOrder = Mock(DspOrderVO)
        var forwardOrder = Mock(DspOrderVO)

        var sortModel = new SortModel(new DspModelVO(dispatchOrder, driver))
        var forwardOrderMap = hasForwardOrder ? Map.of(driverId, forwardOrder) : Collections.emptyMap()
        when:
        sortContext.getDspOrder() >> dispatchOrder
        sortContext.getDspContext() >> dispatchContext
        dispatchContext.getService() >> dispatchService
        dispatchContext.getDriverForwardOrderMap() >> forwardOrderMap
        dispatchContext.getDriverBackwardOrderMap() >> Collections.emptyMap()
        dispatchService.getOrderConnectionDurationScore(_, _) >> expectedScore
        dispatchOrder.getCityId() >> 99
        dispatchOrder.getEstimatedUseTimeBj() >> DateUtil.parseDateStr2Date(dispatchOrderEstimateUseTime)
        forwardOrder.getPredicServiceStopTimeBj() >> DateUtil.parseDateStr2Date(forwardOrderEstimateEndTime)
        then:
        var actualScore = target.value(sortModel, sortContext).getValue()
        expect:
        actualScore == expectedScore
        where:
        description | driverId | dispatchOrderEstimateUseTime | hasForwardOrder | forwardOrderEstimateEndTime | expectedScore
        "无前向单+内" | 1L | "2025-01-15 10:20:00" | false | null | 0
        "无前向单+外" | 2L | "2025-01-15 07:20:00" | false | null | 0
        "距离前向单2小时" | 3L | "2025-01-15 10:20:00" | true | "2025-01-15 08:20:00" | 0
        "距离前向单1小时" | 4L | "2025-01-15 10:20:00" | true | "2025-01-15 09:20:00" | 0
        "距离前向单40分钟" | 4L | "2025-01-15 10:20:00" | true | "2025-01-15 09:20:00" | 10
        "距离前向单8分钟" | 4L | "2025-01-15 10:20:00" | true | "2025-01-15 10:12:00" | 100
    }


}