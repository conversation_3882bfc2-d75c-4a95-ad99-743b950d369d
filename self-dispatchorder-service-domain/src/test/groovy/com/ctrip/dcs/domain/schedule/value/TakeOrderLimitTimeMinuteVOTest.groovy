package com.ctrip.dcs.domain.schedule.value


import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit

class TakeOrderLimitTimeMinuteVOTest extends Specification {

    @Unroll
    def "test greater Than Milliseconds"() {
        given:
        TakeOrderLimitTimeMinuteVO takeOrderLimitTimeMinuteVO = new TakeOrderLimitTimeMinuteVO(minute)

        when:
        boolean result = takeOrderLimitTimeMinuteVO.greaterThanMilliseconds(duration)

        then:
        result == r

        where:
        minute | duration                      || r
        null   | 0                             || false
        0      | 0                             || false
        1      | 0                             || true
        1      | 1000                          || true
        1      | TimeUnit.MINUTES.toMillis(2L) || false
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme