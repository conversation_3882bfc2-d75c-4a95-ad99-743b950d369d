package com.ctrip.dcs.domain.schedule.sort.feature.impl

import com.ctrip.dcs.domain.common.enums.DiversionMatchEnum
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.sort.Sorter
import com.ctrip.dcs.domain.schedule.sort.score.impl.WeightScorer
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SupplierDiversionVO
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.apache.commons.collections.MapUtils
import spock.lang.Specification

class SupplierOrderDiversionSimulationTest extends Specification {

    def "模拟分流"() {
        given:
        def supplierDiversionMap = [
                83951L: new BigDecimal("58"),
                94721L: new BigDecimal("13"),
                1372266L: new BigDecimal("18.1"),
                81946L: new BigDecimal("10.9")
        ]
        def ratePlanId = 3956
        def availableSuppliers = [94721L, 81946L]
        def supplierOrderCountMap = Maps.newHashMap()
//        supplierOrderCountMap.put(4L, 100)
        Set<Long> allSupplierIds = Sets.newHashSet();
        allSupplierIds.addAll(supplierDiversionMap.keySet());
        allSupplierIds.addAll(availableSuppliers);
        var total = 0
        when:
        100.times { round ->
            println "第${round+1}轮: 当前订单数=${supplierOrderCountMap}"
            // mock上下文
            var sortContext = Mock(SortContext)
            var dispatchOrder = Mock(DspOrderVO)
            sortContext.getDspOrder() >> dispatchOrder
            dispatchOrder.getRatePlanId() >> ratePlanId
            // mock可派供应商
            var sortModels = []
            availableSuppliers.each {
                var dispatchModel = Mock(DspModelVO)
                var transportGroup = Mock(TransportGroupVO)
                dispatchModel.getTransportGroup() >> transportGroup
                dispatchModel.getOrder() >> dispatchOrder
                transportGroup.getSupplierId() >> it
                sortModels.add(new SortModel(dispatchModel))
            }
            // mock配置的供应商列表
            var supplierOrderDiversionMap = [:]
            allSupplierIds.each {
                Integer orderCount = supplierOrderCountMap.getOrDefault(it, 0);
                BigDecimal diversion = (BigDecimal) MapUtils.getObject(supplierDiversionMap, it, BigDecimal.ZERO);
                DiversionMatchEnum type = DiversionMatchEnum.valuesOf(supplierDiversionMap.keySet(), Sets.newHashSet(availableSuppliers));
                var key = new AbstractMap.SimpleEntry<>(it.intValue(), ratePlanId)
                var value = new SupplierDiversionVO(it, ratePlanId, diversion, orderCount, type, supplierDiversionMap.containsKey(it), availableSuppliers.contains(it));
                supplierOrderDiversionMap.put(key, value)
            }
            sortContext.getSupplierOrderDiversionMap() >> supplierOrderDiversionMap

            // 创建排序器
            Sorter sorter = new Sorter(new WeightScorer(["F17": Double.valueOf("1")]))
            // 加载特征项
            sorter.addFeature([new SupplierOrderDiversionFeature()])
            // 执行排序项
            List<SortModel> res = sorter.sort(sortModels, sortContext)

            res.each {
                var supplierId = it.getModel().getTransportGroup().getSupplierId()
                var score = it.getScore()
                println "\t\t派给A供应商[${supplierId}]得分${score}"
            }
            var winner = res.first.getModel().getTransportGroup().getSupplierId()

            println "\t供应商[${winner}]订单+1\n"
            supplierOrderCountMap[winner] = supplierOrderCountMap.getOrDefault(winner, 0) + 1
            total = total + 1
        }
        then:
        println "\n\n========================================================================\n\n"
        for (Long it : allSupplierIds) {
            println "供应商[${it}] 配置比例=${supplierDiversionMap.getOrDefault(it, 0)}% 实际比例=${supplierOrderCountMap.getOrDefault(it, 0) / total*100}% 接单数量=${supplierOrderCountMap.getOrDefault(it, 0)}"
        }
    }

}