package com.ctrip.dcs.domain.common.value

import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class GrabOrderDOTest extends Specification {

    def "test"() {
        given:
        GrabOrderDO grabOrderDO = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId","1", 1, 1L, 1l, 1l, 1)


        when:
        def d = grabOrderDO.getPremium()

        then:
        d == 1




    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme