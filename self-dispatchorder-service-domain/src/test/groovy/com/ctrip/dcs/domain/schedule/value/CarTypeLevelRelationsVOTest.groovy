package com.ctrip.dcs.domain.schedule.value

import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation
import spock.lang.Specification
import spock.lang.Unroll

class CarTypeLevelRelationsVOTest extends Specification {

    @Unroll
    def "test other Car Type Relation"() {
        given:
        CarTypeLevelRelationsVO carTypeLevelRelationsVO = new CarTypeLevelRelationsVO(otherCarTypeRelationMap)

        when:
        CarTypeLevelRelation result = carTypeLevelRelationsVO.otherCarTypeRelation(1l)

        then:
        result == r

        where:
        otherCarTypeRelationMap          || r
        null                             || CarTypeLevelRelation.UNKOWN
        new HashMap<>()                  || CarTypeLevelRelation.UNKOWN
        [1L: CarTypeLevelRelation.BELOW] || CarTypeLevelRelation.BELOW
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme