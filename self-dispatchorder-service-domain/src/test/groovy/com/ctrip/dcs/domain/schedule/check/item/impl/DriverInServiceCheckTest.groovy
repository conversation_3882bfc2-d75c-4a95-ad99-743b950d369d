package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class DriverInServiceCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @InjectMocks
    DriverInServiceCheck driverInServiceCheck = new DriverInServiceCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "司机没有正在服务的订单"() {
        given:
        Map<Long, Boolean> map = Maps.newHashMap()
        map.put(1L, false)
        Mockito.when(context.getDriverInServiceMap()).thenReturn(map)

        when:
        CheckCode result = driverInServiceCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机有正在服务的订单"() {
        given:
        Map<Long, Boolean> map = Maps.newHashMap()
        map.put(1L, true)
        Mockito.when(context.getDriverInServiceMap()).thenReturn(map)

        when:
        CheckCode result = driverInServiceCheck.check(checkModel, context)

        then:
        result == CheckCode.DRIV_IN_SERVICE
    }

    def "test check"() {
        given:
        CheckContext context = Mock()
        context.getDriverInServiceMap() >> getMap()
        def checkModel = new CheckModel(model: new DspModelVO(new DspOrderVO(), new DriverVO(driverId: driverId)))
        when:
        CheckCode result = driverInServiceCheck.check(checkModel, context)

        then:
        result == res

        where:
        driverId || res
        1L       || CheckCode.DRIV_IN_SERVICE
        2L       || CheckCode.PASS
        null     || CheckCode.DRIV_IN_SERVICE

    }

    def getMap() {
        Map<Long, Boolean> map = Maps.newHashMap();
        map.put(1L, Boolean.TRUE);
        map.put(2L, Boolean.FALSE)
        return map
    }

    def "test downgrade"() {
        when:
        CheckCode result = driverInServiceCheck.downgrade()

        then:
        result == CheckCode.DRIV_IN_SERVICE
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme