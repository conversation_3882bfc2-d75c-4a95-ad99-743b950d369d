package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class BusinessTravelBlackListCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @InjectMocks
    BusinessTravelBlackListCheck businessTravelBlackListCheck = new BusinessTravelBlackListCheck()

    def "检查商旅黑名单，司机不在黑名单"() {
        given:
        Mockito.when(context.getBusinessTravelBlackList()).thenReturn(Collections.emptyList())

        when:
        CheckCode result = businessTravelBlackListCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "检查商旅黑名单，司机在黑名单"() {
        given:
        Mockito.when(context.getBusinessTravelBlackList()).thenReturn(Lists.newArrayList("京A88888"))
        Mockito.when(car.getCarLicense()).thenReturn("京A88888")

        when:
        CheckCode result = businessTravelBlackListCheck.check(checkModel, context)

        then:
        result == CheckCode.BUSINESS_TRAVEL_BLACKLIST_LIMIT
    }

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    @Unroll
    def "test check"() {
        given:
        CheckContext context = Mock()
        context.getBusinessTravelBlackList() >> businessTravelBlackList

        and:
        DriverVO driverVO = Mock()
        driverVO.getCar() >> car

        and:
        def dspModelVO = new CheckModel(new DspModelVO(driver: driverVO))

        when:
        CheckCode result = businessTravelBlackListCheck.check(dspModelVO, context)

        then:
        result == r

        where:
        car                          | businessTravelBlackList || r
        null                         | []                      || CheckCode.PASS
        new CarVO()                  | []                      || CheckCode.PASS
        new CarVO(carLicense: "abc") | ["bbc"]                 || CheckCode.PASS
        new CarVO(carLicense: "abc") | ["abc"]                 || CheckCode.BUSINESS_TRAVEL_BLACKLIST_LIMIT
    }

    def "test downgrade"() {
        when:
        CheckCode result = businessTravelBlackListCheck.downgrade()

        then:
        result == CheckCode.BUSINESS_TRAVEL_BLACKLIST_LIMIT
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme