package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class FirstClassDriverCheckCheckTest extends Specification {
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    Logger logger
    @InjectMocks
    FirstClassDriverCheckCheck firstClassDriverCheckCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "没有1类司机数据"() {
        given:
        Mockito.when(context.getFirstClassDriverList()).thenReturn(Collections.emptyList())

        when:
        CheckCode result = firstClassDriverCheckCheck.check(checkModel, context)

        then:
        result == CheckCode.DRIVER_NOT_FIRST_CLASS
    }

    def "有1类司机数据，司机不在集合"() {
        given:
        Mockito.when(context.getFirstClassDriverList()).thenReturn(Lists.newArrayList(2L))

        when:
        CheckCode result = firstClassDriverCheckCheck.check(checkModel, context)

        then:
        result == CheckCode.DRIVER_NOT_FIRST_CLASS
    }

    def "有1类司机数据，司机在集合"() {
        given:
        Mockito.when(context.getFirstClassDriverList()).thenReturn(Lists.newArrayList(1L, 2L))

        when:
        CheckCode result = firstClassDriverCheckCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme