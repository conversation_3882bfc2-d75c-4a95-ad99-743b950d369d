package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.CarTypeRelationGateway
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class CarTypeLevelRelationVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DriverVO driver
    @Mock
    CarTypeRelationGateway carTypeRelationGateway
    @Mock
    CarTypeLevelRelationsVO carTypeLevelRelationsVO

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(checkContext.getCarTypeLevelRelationsMap()).thenReturn([1:carTypeLevelRelationsVO])
        when(carTypeRelationGateway.queryCarTypeLevelRelations(anyInt())).thenReturn(new CarTypeLevelRelationsVO([(1l): CarTypeLevelRelation.BELOW]))
        CarTypeLevelRelationVisitor carTypeLevelRelationVisitor = new CarTypeLevelRelationVisitor(1, carTypeRelationGateway)
        when:
        carTypeLevelRelationVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }

    def "test visit 2"() {
        given:
        when(checkContext.getCarTypeLevelRelationsMap()).thenReturn([2:carTypeLevelRelationsVO])
        when(carTypeRelationGateway.queryCarTypeLevelRelations(anyInt())).thenReturn(new CarTypeLevelRelationsVO([(1l): CarTypeLevelRelation.BELOW]))
        CarTypeLevelRelationVisitor carTypeLevelRelationVisitor = new CarTypeLevelRelationVisitor(1, carTypeRelationGateway)
        when:
        carTypeLevelRelationVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme