package com.ctrip.dcs.domain.common.enums

import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.context.DspOrderRewardStrategyContext
import com.ctrip.dcs.domain.schedule.entity.DspOrderRewardStrategyDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRewardRuleDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderPushRuleDO
import spock.lang.Specification

import java.sql.Timestamp

/**
 * <AUTHOR>
 */
class DspOrderRewardStrategyTypeTest extends Specification {


    def "test SYSTEM strategy"() {
        given:
        DspOrderRewardStrategyContext context = Mock(DspOrderRewardStrategyContext)
        DspOrderVO order = Mock(DspOrderVO)
        context.getOrder() >> order
        order.getDspAddPrice() >> new BigDecimal("100.00")
        order.getCityId() >> 1
        order.getDspOrderId() >> "orderId"
        order.getUserOrderId() >> "userOrderId"
        order.getDriverCurrency() >> "CNY"
        context.getOfflineCityIds() >> ""

        when:
        List<DspOrderRewardStrategyDO> result = DspOrderRewardStrategyType.SYSTEM.create(context)

        then:
        result.size() == 1
        result[0].getDspRewardAmount() == new BigDecimal("100.00")
        result[0].getDspRewardCurrency() == "CNY"
    }

    def "test VBK strategy"() {
        given:
        DspOrderRewardStrategyContext context = Mock(DspOrderRewardStrategyContext)
        DspOrderVO order = Mock(DspOrderVO)
        VBKDriverGrabOrderDO vbkDriverGrabOrder = Mock(VBKDriverGrabOrderDO)
        context.getOrder() >> order
        context.getVbkDriverGrabOrder() >> vbkDriverGrabOrder
        order.getOrderStatus() >> OrderStatusEnum.DISPATCH_CONFIRMED.getCode()
        order.getDspOrderId() >> "orderId"
        order.getUserOrderId() >> "userOrderId"
        vbkDriverGrabOrder.getInitialAmount() >> new BigDecimal("50.00")
        vbkDriverGrabOrder.getInitialCurrency() >> "USD"
        vbkDriverGrabOrder.getRewardsAmount() >> new BigDecimal("50.00")
        vbkDriverGrabOrder.getRewardsCurrency() >> "USD"
        vbkDriverGrabOrder.getGrabRewardTimeLocal() >> new Timestamp(System.currentTimeMillis())

        when:
        List<DspOrderRewardStrategyDO> result = DspOrderRewardStrategyType.VBK.create(context)

        then:
        result.size() == 1
        result[0].getDspRewardAmount() == new BigDecimal("50.00")
        result[0].getDspRewardCurrency() == "USD"
    }

    def "test VBK_SYSTEM strategy"() {
        given:
        DspOrderRewardStrategyContext context = Mock(DspOrderRewardStrategyContext)
        DspOrderVO order = Mock(DspOrderVO)
        GrabDspOrderPushRuleDO rule = Mock(GrabDspOrderPushRuleDO)
        context.getOrder() >> order
        context.getRule() >> rule
        order.getOrderStatus() >> OrderStatusEnum.DISPATCH_CONFIRMED.getCode()
        order.getDspOrderId() >> "orderId"
        order.getUserOrderId() >> "userOrderId"
        order.getCityId() >> 1
        order.getSkuId() >> 1
        order.getEstimatedUseTimeBj() >> DateUtil.addHours(new Date(), 1)
        order.getUseDays() >> new UseDays(BigDecimal.ONE)
        order.getLastConfirmCarTimeBj() >> new Date()
        rule.getRewardRule() >> Mock(GrabDspOrderPushRewardRuleDO)
        rule.getRewardRule().getRound() >> 2
        rule.getRuleType() >> 1
        rule.getImmediatePushTime() >> Integer.MAX_VALUE
        rule.calculateRewardDuration(_, _) >> 1

        when:
        List<DspOrderRewardStrategyDO> result = DspOrderRewardStrategyType.VBK_SYSTEM.create(context)

        then:
        result.size() == 2
    }

    def "test VBK_SYSTEM strategy 2"() {
        given:
        DspOrderRewardStrategyContext context = Mock(DspOrderRewardStrategyContext)
        DspOrderVO order = Mock(DspOrderVO)
        GrabDspOrderPushRuleDO rule = Mock(GrabDspOrderPushRuleDO)
        context.getOrder() >> order
        context.getRule() >> rule
        order.getOrderStatus() >> OrderStatusEnum.DISPATCH_CONFIRMED.getCode()
        order.getDspOrderId() >> "orderId"
        order.getUserOrderId() >> "userOrderId"
        order.getCityId() >> 1
        order.getSkuId() >> 1
        order.getSettleToDriver() >> 1
        order.getEstimatedUseTimeBj() >> DateUtil.addHours(new Date(), 1)
        order.getUseDays() >> new UseDays(BigDecimal.ONE)
        order.getLastConfirmCarTimeBj() >> new Date()
        rule.getRewardRule() >> Mock(GrabDspOrderPushRewardRuleDO)
        rule.getRewardRule().getRound() >> 2
        rule.getRuleType() >> 1
        rule.getImmediatePushTime() >> Integer.MAX_VALUE
        rule.calculateRewardDuration(_, _) >> 1

        when:
        List<DspOrderRewardStrategyDO> result = DspOrderRewardStrategyType.VBK_SYSTEM.create(context)

        then:
        result.size() == 2
    }
}
