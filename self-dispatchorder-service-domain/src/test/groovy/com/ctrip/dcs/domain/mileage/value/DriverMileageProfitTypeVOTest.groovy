package com.ctrip.dcs.domain.mileage.value

import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.value.DspOrderVO
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import java.util.function.Function
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverMileageProfitTypeVOTest extends Specification {
    @Mock
    DspOrderVO dspOrderVO

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test complete"() {
        when:
        String type = DriverMileageProfitTypeVO.COMPLETE.getType()

        Boolean filter1 = DriverMileageProfitTypeVO.COMPLETE.getFilter().apply(null)

        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.ORDER_CANCEL.getCode())
        Boolean filter2 = DriverMileageProfitTypeVO.COMPLETE.getFilter().apply(dspOrderVO)

        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.ORDER_FINISH.getCode())
        Boolean filter3 = DriverMileageProfitTypeVO.COMPLETE.getFilter().apply(dspOrderVO)

        then:
        type == "complete"
        !filter1
        !filter2
        filter3
    }

    def "test expect"() {
        when:
        String type = DriverMileageProfitTypeVO.EXPECT.getType()

        Boolean filter1 = DriverMileageProfitTypeVO.EXPECT.getFilter().apply(null)

        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.ORDER_CANCEL.getCode())
        Boolean filter2 = DriverMileageProfitTypeVO.EXPECT.getFilter().apply(dspOrderVO)

        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.DRIVER_CONFIRMED.getCode())
        Boolean filter3 = DriverMileageProfitTypeVO.EXPECT.getFilter().apply(dspOrderVO)

        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.DRIVER_CAR_CONFIRMED.getCode())
        Boolean filter4 = DriverMileageProfitTypeVO.EXPECT.getFilter().apply(dspOrderVO)

        then:
        type == "expect"
        !filter1
        !filter2
        filter3
        filter4
    }

    def "test today"() {
        when:
        String type = DriverMileageProfitTypeVO.TODAY.getType()

        Boolean filter1 = DriverMileageProfitTypeVO.TODAY.getFilter().apply(null)

        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.ORDER_CANCEL.getCode())
        Boolean filter2 = DriverMileageProfitTypeVO.TODAY.getFilter().apply(dspOrderVO)

        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.DRIVER_CONFIRMED.getCode())
        Boolean filter3 = DriverMileageProfitTypeVO.TODAY.getFilter().apply(dspOrderVO)

        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.DRIVER_CAR_CONFIRMED.getCode())
        Boolean filter4 = DriverMileageProfitTypeVO.TODAY.getFilter().apply(dspOrderVO)

        when(dspOrderVO.getOrderStatus()).thenReturn(OrderStatusEnum.ORDER_FINISH.getCode())
        Boolean filter5 = DriverMileageProfitTypeVO.TODAY.getFilter().apply(dspOrderVO)

        then:
        type == "today"
        !filter1
        !filter2
        filter3
        filter4
        filter5
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme