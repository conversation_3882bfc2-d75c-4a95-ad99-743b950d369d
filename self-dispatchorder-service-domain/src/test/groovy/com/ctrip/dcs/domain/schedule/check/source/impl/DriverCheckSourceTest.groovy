package com.ctrip.dcs.domain.schedule.check.source.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.google.common.collect.Lists
import spock.lang.Specification

class DriverCheckSourceTest extends Specification {


    def dspOrder = Mock(DspOrderVO)
    def dspContextService = Mock(DspContextService)
    def checkContext = Mock(CheckContext)
    def dspContext = Mock(DspContext)
    def t1 = Mock(TransportGroupVO)
    def t2 = Mock(TransportGroupVO)
    def driverCheckSource = new DriverCheckSource()
    def transportGroupCheckSource = new TransportGroupCheckSource()

    def "init source 0"() {
        given: "Mock数据"
        checkContext.getContext() >> dspContext
        checkContext.getDspOrder() >> dspOrder
        dspContext.getService() >> dspContextService
        t1.getTransportGroupId() >> 1L
        t1.getSupplierId() >> 1L
        dspOrder.getShortDisOrder() >> 1L
        dspContextService.queryTransports(_) >> Lists.newArrayList(t1)
        DriverVO driver = new DriverVO(driverId: 1L, transportGroups: [t1])

        dspContextService.queryDriversByTransportGroupIds(_,_,_,_) >> Lists.newArrayList(driver)

        dspContextService.queryValidateTransportSupplier(_,_) >> Lists.newArrayList(1L)
        when: "执行校验方法"
        def groupSource = transportGroupCheckSource.init(checkContext)

        then: "验证校验结果"
        groupSource.size() == 0
    }

    def "init source 0_1"() {
        given: "Mock数据"
        checkContext.getContext() >> dspContext
        checkContext.getDspOrder() >> dspOrder
        dspContext.getService() >> dspContextService
        t1.getTransportGroupId() >> 1L
        t1.getSupplierId() >> 1L
        dspOrder.getShortDisOrder() >> 1L
        dspContextService.queryTransports(_) >> Lists.newArrayList(t1)

        t1.getTransportGroupId() >> 2L
        t1.getSupplierId() >> 2L
        DriverVO driver = new DriverVO(driverId: 1L, transportGroups: [t2])

        dspContextService.queryDriversByTransportGroupIds(_,_,_,_) >> Lists.newArrayList(driver)

        dspContextService.queryValidateTransportSupplier(_,_) >> Lists.newArrayList(1L)
        when: "执行校验方法"
        def driverSource = driverCheckSource.init(checkContext)

        then: "验证校验结果"
        driverSource.size() == 0
    }

    def "init source 0_2"() {
        given: "Mock数据"
        checkContext.getContext() >> dspContext
        checkContext.getDspOrder() >> dspOrder
        dspContext.getService() >> dspContextService
        t1.getTransportGroupId() >> 1L
        t1.getSupplierId() >> 1L
        dspOrder.getShortDisOrder() >> 0L
        dspContextService.queryTransports(_) >> Lists.newArrayList(t1)

        t2.getTransportGroupId() >> 1L
        t2.getSupplierId() >> 1L
        DriverVO driver = new DriverVO(driverId: 1L, transportGroups: [t2])

        dspContextService.queryDriversByTransportGroupIds(_,_,_,_) >> Lists.newArrayList(driver)

        dspContextService.queryValidateTransportSupplier(_,_) >> Lists.newArrayList(1L)
        when: "执行校验方法"
        def driverSource = driverCheckSource.init(checkContext)

        then: "验证校验结果"
        driverSource.size() == 1
    }

    def "init source"() {
        given: "Mock数据"
        checkContext.getContext() >> dspContext
        checkContext.getDspOrder() >> dspOrder
        dspContext.getService() >> dspContextService
        t1.getTransportGroupId() >> 1L
        t1.getSupplierId() >> 1L
        dspContextService.queryTransports(_) >> Lists.newArrayList(t1)
        DriverVO driver = new DriverVO(driverId: 1L, transportGroups: [t1])

        dspContextService.queryDriversByTransportGroupIds(_,_,_,_) >> Lists.newArrayList(driver)

        dspContextService.queryValidateTransportSupplier(_,_) >> Lists.newArrayList(1L)
        when: "执行校验方法"
        def driverSource = driverCheckSource.init(checkContext)
        def groupSource = transportGroupCheckSource.init(checkContext)

        then: "验证校验结果"
        driverSource.size() == 1
        groupSource.size() == 1
    }

    def "taken source"() {
        given: "Mock数据"
        checkContext.getContext() >> dspContext
        checkContext.getDspOrder() >> dspOrder
        dspContext.getService() >> dspContextService
        t1.getTransportGroupId() >> 1L
        t1.getSupplierId() >> 1L
        dspContextService.queryTransports(_) >> Lists.newArrayList(t1)
        DriverVO driver = new DriverVO(driverId: 1L, transportGroups: [t1])
        checkContext.getDriver() >> driver
        dspContextService.queryDriversByTransportGroupIds(_,_,_,_) >> Lists.newArrayList(driver)

        dspContextService.queryValidateTransportSupplier(_,_) >> Lists.newArrayList(1L)
        when: "执行校验方法"
        def checkModel = driverCheckSource.taken(checkContext)

        then: "验证校验结果"
        checkModel.getCheckCode() == CheckCode.PASS
    }

    def "test filterDistanceType"() {
        given:
        dspOrder.getShortDisOrder() >>> [0, 1]
        when:
        var r1 = driverCheckSource.filterDistanceType(false, dspOrder, Collections.emptyList())
        var r2 = driverCheckSource.filterDistanceType(true, dspOrder, Collections.emptyList())
        var r3 = driverCheckSource.filterDistanceType(true, dspOrder, Collections.emptyList())
        then:
        org.apache.commons.collections4.CollectionUtils.size(r1) == 0
        org.apache.commons.collections4.CollectionUtils.size(r2) == 0
        org.apache.commons.collections4.CollectionUtils.size(r3) == 0
    }

}
