package com.ctrip.dcs.domain.common.value

import org.apache.commons.collections.CollectionUtils
import org.assertj.core.util.Lists
import org.junit.Assert
import spock.lang.Specification

/**
 * <AUTHOR>
 * @since 2025/4/30 16:56
 */
class DriverInfoRequestVOTest extends Specification {
    def testObj = new DriverInfoRequestVO()
    def driverIdList = Mock(List)
    def driverPhoneList = Mock(List)

    def setup() {
        testObj.driverPhoneList = driverPhoneList
        testObj.driverIdList = driverIdList
    }

    def "test"() {
        given:
        when:
        DriverInfoRequestVO driverInfoRequestVO = new DriverInfoRequestVO()
        driverInfoRequestVO.setDriverIdList(Lists.newArrayList(1L))
        driverInfoRequestVO.setDriverName("driverName")
        driverInfoRequestVO.setDriverPhoneList(Lists.newArrayList("18333333333"))
        driverInfoRequestVO.setStatus(2)
        driverInfoRequestVO.setSupplierId(22L)

        then:
        Assert.assertTrue(CollectionUtils.isNotEmpty(driverInfoRequestVO.getDriverIdList()))
        Assert.assertTrue(Objects.equals(driverInfoRequestVO.getDriverName(),"driverName"))
        Assert.assertTrue(CollectionUtils.isNotEmpty(driverInfoRequestVO.getDriverPhoneList()))
        Assert.assertTrue(Objects.equals(driverInfoRequestVO.getStatus(), 2))
        Assert.assertTrue(Objects.equals(driverInfoRequestVO.getSupplierId(), 22L))
    }
}
