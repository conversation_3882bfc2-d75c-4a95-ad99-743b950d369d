package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.google.common.collect.Maps
import spock.lang.Specification

import java.time.LocalDate

class DriverDistanceOrderCountVisitorTest extends Specification {

    CheckContext checkContext = Mock()
    DspContext dispatchContext = Mock()
    SelfOrderQueryGateway orderGateway = Mock()

    def "test check"() {
        given:
        var instance = new DriverDistanceOrderCountVisitor(LocalDate.now(), List.of(new DriverVO()), orderGateway)
        when:
        checkContext.getContext() >> dispatchContext
        dispatchContext.getDriverDistanceOrderCountMap() >> Maps.newHashMap()
        orderGateway.queryDriverDistanceOrderCount(_, _, _, _, _, _) >> Maps.newConcurrentMap()
        then:
        instance.visit(checkContext)
        expect:
        checkContext != null
    }

}