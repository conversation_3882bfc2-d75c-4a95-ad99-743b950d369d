package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.EmptyDrivingVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.carconfig.NearPickUpTimeOrderConfigValueVO
import com.ctrip.dcs.domain.schedule.value.carconfig.OrderMileageConfigValueVO
import spock.lang.Specification

class OrderMileageValueV2CheckTest extends Specification {
    def check = new OrderMileageValueV2Check()

    def context = Mock(CheckContext)
    def checkModel = Mock(CheckModel)
    def dspOrder = Mock(DspOrderVO)
    def driver = Mock(DriverVO)
    def model = Mock(DspModelVO)

    def setup() {
        context.getDspOrder() >> dspOrder
        checkModel.getModel() >> model
        model.getDriver() >> driver
        model.getOrder() >> dspOrder
    }

    def "test check method with orderMileageConfigValue is null"() {
        given:
        context.getDriverEmptyDrivingInfo4OrderMileageV2Map() >> [(driver.getDriverId()): new DeadHeadDisModelVO()]
        context.queryOrderMileageValueConfigValue(_ as DspOrderVO) >> null

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check method with forwardOrderInfo no backwardOrderInfo"() {
        given:
        dspOrder.getCostAmount() >> dspOrderCostAmount
        dspOrder.getEstimatedKm() >> estimatedKm
        dspOrder.getEstimatedUseTimeBj() >> estimatedUseTimeBj
        context.getDriverEmptyDrivingInfo4OrderMileageV2Map() >> [(driver.getDriverId()): new DeadHeadDisModelVO(frowardOrderInfo: frowardOrderInfo, frowardEmptyDrivingInfo: new EmptyDrivingVO(distance: 30000))]
        context.queryOrderMileageValueConfigValue(_ as DspOrderVO) >> new OrderMileageConfigValueVO(0, 0, "00:00:00", "23:59:59", 0.7, 1.2, 1.1)
        context.queryNearPickUpTimeOrderConfigValue(_) >> new NearPickUpTimeOrderConfigValueVO(60)

        expect:
        result == check.check(checkModel, context)

        where:
        result                                    | dspOrderCostAmount        | estimatedKm              | frowardOrderInfo                              | estimatedUseTimeBj
        CheckCode.FORWARD_ORDER_LOW_MILEAGE_VALUE | BigDecimal.valueOf(40.0)  | BigDecimal.valueOf(30.0) | null                                          | DateUtil.addMinutes(new Date(), 10)  // 无前向单，待派单是临近用车订单，使用临近用车系数*里程价值判断,前向里程价值>=阈值，不通过
        CheckCode.FORWARD_ORDER_LOW_MILEAGE_VALUE | BigDecimal.valueOf(40.0)  | BigDecimal.valueOf(30.0) | null                                          | DateUtil.addMinutes(new Date(), 70) // 无前向单，待派单非临近用车订单，使用原里程价值判断,前向里程价值>=阈值，不通过
        CheckCode.PASS                            | BigDecimal.valueOf(100.0) | BigDecimal.valueOf(30.0) | new DspOrderVO(fromCityId: 2L, toCityId: 10L) | new Date() // 前向单为跨城订单，使用跨城单后向里程价值判断,前向里程价值>=阈值，通过
        CheckCode.FORWARD_ORDER_LOW_MILEAGE_VALUE | BigDecimal.valueOf(60.0)  | BigDecimal.valueOf(30.0) | new DspOrderVO(fromCityId: 2L, toCityId: 10L) | new Date() // 前向单为跨城订单，使用跨城单后向里程价值判断,前向里程价值<阈值，不通过
        CheckCode.PASS                            | BigDecimal.valueOf(100.0) | BigDecimal.valueOf(30.0) | new DspOrderVO(fromCityId: 2L, toCityId: 2L)  | DateUtil.addMinutes(new Date(), 10) // 前向单非跨城订单，待派单是临近用车订单，使用临近用车系数*里程价值判断,前向里程价值>=阈值，通过
        CheckCode.FORWARD_ORDER_LOW_MILEAGE_VALUE | BigDecimal.valueOf(40.0)  | BigDecimal.valueOf(30.0) | new DspOrderVO(fromCityId: 2L, toCityId: 2L)  | DateUtil.addMinutes(new Date(), 10) // 前向单非跨城订单，待派单是临近用车订单，使用临近用车系数*里程价值判断,前向里程价值<阈值，不通过
        CheckCode.PASS                            | BigDecimal.valueOf(100.0) | BigDecimal.valueOf(30.0) | new DspOrderVO(fromCityId: 2L, toCityId: 2L)  | DateUtil.addMinutes(new Date(), 70) // 前向单非跨城订单，待派单非临近用车订单，使用原里程价值判断,前向里程价值>=阈值，通过
        CheckCode.FORWARD_ORDER_LOW_MILEAGE_VALUE | BigDecimal.valueOf(40.0)  | BigDecimal.valueOf(30.0) | new DspOrderVO(fromCityId: 2L, toCityId: 2L)  | DateUtil.addMinutes(new Date(), 70) // 前向单非跨城订单，待派单非临近用车订单，使用原里程价值判断,前向里程价值<>阈值，不通过
    }

    def "test check method with backwardOrderInfo no forwardOrderInfo"() {
        given:
        dspOrder.getCostAmount() >> BigDecimal.valueOf(100.0)
        dspOrder.getEstimatedKm() >> BigDecimal.valueOf(30.0)
        dspOrder.isCrossCityOrder() >> isCrossCityOrder
        dspOrder.getEstimatedUseTimeBj() >> estimatedUseTimeBj
        context.getDriverEmptyDrivingInfo4OrderMileageV2Map() >> [(driver.getDriverId()): new DeadHeadDisModelVO(backwardOrderInfo: backwardOrderInfo, backwardEmptyDrivingInfo: new EmptyDrivingVO(distance: 30000))]
        context.queryOrderMileageValueConfigValue(_ as DspOrderVO) >> new OrderMileageConfigValueVO(0, 0, "00:00:00", "23:59:59", 0.7, 1.2, 1.1)
        context.queryNearPickUpTimeOrderConfigValue(_) >> new NearPickUpTimeOrderConfigValueVO(60)

        expect:
        result == check.check(checkModel, context)

        where:
        result                                     | isCrossCityOrder | backwardOrderInfo                                    | estimatedUseTimeBj
        CheckCode.PASS                             | true             | new DspOrderVO(costAmount: 100.0, estimatedKm: 30.0) | new Date() // 无前向单（前向里程价值达标），有后向单，待派单为跨城订单，使用跨城单后向里程价值判断,后向里程价值>=阈值，通过
        CheckCode.BACKWARD_ORDER_LOW_MILEAGE_VALUE | true             | new DspOrderVO(costAmount: 60.0, estimatedKm: 30.0)  | new Date() // 无前向单（前向里程价值达标），有后向单，待派单为跨城订单，使用跨城单后向里程价值判断,后向里程价值<阈值，不通过
        CheckCode.PASS                             | false            | new DspOrderVO(costAmount: 100.0, estimatedKm: 30.0) | DateUtil.addMinutes(new Date(), 10) // 无前向单（前向里程价值达标），有后向单，待派单是临近用车订单，使用临近用车系数*里程价值判断,后向里程价值>=阈值，通过
        CheckCode.BACKWARD_ORDER_LOW_MILEAGE_VALUE | false            | new DspOrderVO(costAmount: 40.0, estimatedKm: 30.0)  | DateUtil.addMinutes(new Date(), 10) // 无前向单（前向里程价值达标），有后向单，待派单是临近用车订单，使用临近用车系数*里程价值判断,后向里程价值<阈值，不通过
        CheckCode.PASS                             | false            | new DspOrderVO(costAmount: 100.0, estimatedKm: 30.0) | DateUtil.addMinutes(new Date(), 70) // 无前向单（前向里程价值达标），有后向单，待派单非跨城单，且非临近用车订单，使用原里程价值判断,后向里程价值>=阈值，通过
        CheckCode.BACKWARD_ORDER_LOW_MILEAGE_VALUE | false            | new DspOrderVO(costAmount: 40.0, estimatedKm: 30.0)  | DateUtil.addMinutes(new Date(), 70) // 无前向单（前向里程价值达标），有后向单，待派单非跨城单，且非临近用车订单，使用原里程价值判断,后向里程价值<阈值，不通过
    }
}
