package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.service.OrderFeePriorityService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.value.OrderPriorityType
import com.ctrip.dcs.domain.schedule.context.DspContext
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class OrderPriorityTypeVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driverVO
    @Mock
    OrderFeePriorityService orderFeePriorityService
    @InjectMocks
    OrderPriorityTypeVisitor orderPriorityTypeVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(dspOrder.getDspOrderId()).thenReturn("getDspOrderIdResponse")
        when(orderFeePriorityService.queryOrderPriorityType(any())).thenReturn(OrderPriorityType.HIGH)
        OrderPriorityTypeVisitor orderPriorityTypeVisitor = new OrderPriorityTypeVisitor(dspOrder, orderFeePriorityService)
        when:
        orderPriorityTypeVisitor.visit(checkContext)

        then:
        true//todo - validate something
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme