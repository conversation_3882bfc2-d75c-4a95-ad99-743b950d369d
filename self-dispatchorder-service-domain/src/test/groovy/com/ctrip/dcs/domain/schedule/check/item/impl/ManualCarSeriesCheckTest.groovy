package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.SeriesVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.dcs.domain.schedule.visitor.Visitor
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Sets
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ManualCarSeriesCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO dspOrder
    @Mock
    CarVO car
    @Mock
    CheckContext checkContext
    @InjectMocks
    ManualCarSeriesCheck manualCarSeriesCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test load"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(dspOrder.isDesignatedVehicleOrder()).thenReturn(false)

        when:
        manualCarSeriesCheck.load([checkModel], checkContext)

        then:
        verify(checkContext, never()).accept(any() as Visitor)
    }

    def "test check"() {
        given:
        when(checkContext.getDspOrder()).thenReturn(dspOrder)
        when(checkContext.getCarSeriesMap()).thenReturn(["1":new SeriesVO(Sets.newHashSet(1L))])
        when(checkModel.getModel()).thenReturn(dspModel)
        when(dspModel.getOrder()).thenReturn(dspOrder)
        when(dspModel.getCar()).thenReturn(car)
        when(car.getCarSeriesId()).thenReturn(carSeriesId)
        when(dspOrder.isDesignatedVehicleOrder()).thenReturn(isDesignatedVehicleOrder)
        when(dspOrder.getDspOrderId()).thenReturn("1")

        when:
        CheckCode result = manualCarSeriesCheck.check(checkModel, checkContext)

        then:
        result == code

        where:
        isDesignatedVehicleOrder | carSeriesId || code
        false                    | 0L           | CheckCode.PASS
        true                     | 0L           | CheckCode.PASS
        true                     | 1L           | CheckCode.PASS
        true                     | 2L           | CheckCode.APPOINT_VEHICLE_SERIES_DRIVER

    }

    def "test downgrade"() {
        when:
        CheckCode result = manualCarSeriesCheck.downgrade()

        then:
        result == CheckCode.APPOINT_VEHICLE_SERIES_DRIVER
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme