package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.gateway.SelfTransportInventoryGateway
import org.junit.Assert
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class TransGroupOrderNumLimitVisitorTest extends Specification {
    @Mock
    DspOrderVO dspOrder
    @Mock
    TransportGroupVO transportGroup
    @Mock
    CheckContext checkContext
    @Mock
    SelfTransportInventoryGateway selfTransportInventoryGateway
    @InjectMocks
    TransGroupOrderNumLimitVisitor transGroupOrderNumLimitVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(dspOrder.getCategoryCode()).thenReturn(CategoryCodeEnum.ALL)
        when(dspOrder.getCityId()).thenReturn(0)
        when(dspOrder.getEstimatedUseTimeBj()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 16, 35).getTime())
        when(dspOrder.getUseDays()).thenReturn(new UseDays(0 as BigDecimal))
        when(dspOrder.getTargetId()).thenReturn("getTargetIdResponse")
        when(selfTransportInventoryGateway.queryTransportGroupUsableInventory(any(), any(), any(), anyInt(), anyString(), any(),any(),any(),any())).thenReturn([(1l): 0])
        TransGroupOrderNumLimitVisitor transGroupOrderNumLimitVisitor = new TransGroupOrderNumLimitVisitor(dspOrder, [transportGroup], selfTransportInventoryGateway)
        CheckContext checkContext1 = new CheckContext()
        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(1L)
        checkContext1.setDriver(driverVO)
        when:
        transGroupOrderNumLimitVisitor.visit(new CheckContext())

        then:
        Assert.assertTrue(driverVO.getDriverId() == 1L)
    }

    def "test visit 2"() {
        given:
        when(checkContext.getTransportGroupInventoryMap()).thenReturn([1L : 1])
        DriverVO driverVO = new DriverVO()
        driverVO.setDriverId(1L)
        when(checkContext.getDriver()).thenReturn(driverVO)
        when(transportGroup.getTransportGroupId()).thenReturn(1L)
        when(dspOrder.getCategoryCode()).thenReturn(CategoryCodeEnum.ALL)
        when(dspOrder.getCityId()).thenReturn(0)
        when(dspOrder.getEstimatedUseTimeBj()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 16, 35).getTime())
        when(dspOrder.getUseDays()).thenReturn(new UseDays(0 as BigDecimal))
        when(dspOrder.getTargetId()).thenReturn("getTargetIdResponse")
        when(selfTransportInventoryGateway.queryTransportGroupUsableInventory(any(), any(), any(), anyInt(), anyString(), any(),any(),any(),any())).thenReturn([(1l): 0])
        TransGroupOrderNumLimitVisitor transGroupOrderNumLimitVisitor = new TransGroupOrderNumLimitVisitor(dspOrder, [transportGroup], selfTransportInventoryGateway)

        when:
        transGroupOrderNumLimitVisitor.visit(checkContext)

        then:
        Assert.assertTrue(driverVO.getDriverId() == 1L)
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme