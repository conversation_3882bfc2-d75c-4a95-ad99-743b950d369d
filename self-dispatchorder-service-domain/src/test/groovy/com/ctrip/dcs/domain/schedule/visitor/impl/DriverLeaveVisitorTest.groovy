package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.DriverLeaveGateway
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.value.DriverLeaveVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyBoolean
import static org.mockito.ArgumentMatchers.anySet
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverLeaveVisitorTest extends Specification {
    @Mock
    DriverLeaveGateway driverLeaveGateway
    @Mock
    CheckContext checkContext
    @Mock
    SortContext sortContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO order
    @Mock
    DriverVO driverVO
    @InjectMocks
    DriverLeaveVisitor driverLeaveVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        def a = 1
        when(driverLeaveGateway.queryDrvLeaveDetail(anySet(), anyBoolean())).thenReturn([new DriverLeaveVO(1l, "leaveBeginTime", "leaveEndTime")])
        DriverLeaveVisitor driverLeaveVisitor = new DriverLeaveVisitor(driverLeaveGateway, [driverVO])
        when:
        driverLeaveVisitor.visit(checkContext)
        a = a +1
        then:
        a ==2
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme