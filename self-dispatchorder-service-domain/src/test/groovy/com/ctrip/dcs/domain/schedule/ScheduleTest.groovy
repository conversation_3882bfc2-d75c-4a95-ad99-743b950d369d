package com.ctrip.dcs.domain.schedule

import cn.hutool.core.math.Money
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ScheduleStatus
import com.ctrip.dcs.domain.common.enums.ScheduleTaskStatus
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.value.RewardVO
import com.ctrip.dcs.domain.schedule.value.ScheduleStrategyVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class ScheduleTest extends Specification {



    @Unroll
    def "test schedule execute"() {
        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(30).build()
        def t1 = ScheduleTaskDO.builder().taskId(1).status( ScheduleTaskStatus.INIT).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).build()
        def t2 = ScheduleTaskDO.builder().taskId(2).status( ScheduleTaskStatus.WAIT_EXECUTE).priority(3).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).build()
        def t3 = ScheduleTaskDO.builder().taskId(3).status( ScheduleTaskStatus.EXECUTE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).build()
        def t4 = ScheduleTaskDO.builder().taskId(4).status( ScheduleTaskStatus.COMPLETE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).build()
        def t5 = ScheduleTaskDO.builder().taskId(5).status( ScheduleTaskStatus.CANCEL).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).build()
        def t6 = ScheduleTaskDO.builder().taskId(6).status( ScheduleTaskStatus.WAIT_EXECUTE).priority(2).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).build()
        def schedule = ScheduleDO.builder().scheduleId(1).dspOrderId("1").strategy(new ScheduleStrategyVO()).round(0).status(ScheduleStatus.EXECUTING).build()
        def order = DspOrderVO.builder().estimatedUseTimeBj(new Timestamp(System.currentTimeMillis())).lastConfirmTimeBj(new Timestamp(System.currentTimeMillis())).build()
        when: "执行校验方法"
        schedule.execute(order, Lists.newArrayList(t1, t2, t3, t4, t5, t6), new RewardVO(BigDecimal.ZERO,"", []))

        then: "验证校验结果"
        schedule.getNext().size() == 1
        schedule.getNext().get(0).getTaskId() == 2

    }

    @Unroll
    def "test schedule rebuild"() {
        def t1 = ScheduleTaskDO.builder().taskId(1).status( ScheduleTaskStatus.INIT).priority(1).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def t2 = ScheduleTaskDO.builder().taskId(2).status( ScheduleTaskStatus.INIT).priority(2).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def t3 = ScheduleTaskDO.builder().taskId(3).status( ScheduleTaskStatus.COMPLETE).priority(1).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def t4 = ScheduleTaskDO.builder().taskId(4).status( ScheduleTaskStatus.COMPLETE).priority(1).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(DateUtil.addSeconds(new Date(), -20)).round(0).build()
        def t5 = ScheduleTaskDO.builder().taskId(5).status( ScheduleTaskStatus.CANCEL).priority(1).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def t6 = ScheduleTaskDO.builder().taskId(6).status( ScheduleTaskStatus.COMPLETE).priority(2).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def schedule = ScheduleDO.builder().scheduleId(1).dspOrderId("1").strategy(new ScheduleStrategyVO()).round(0).status(ScheduleStatus.EXECUTING).build()
        def order = DspOrderVO.builder().estimatedUseTimeBj(new Timestamp(System.currentTimeMillis())).lastConfirmTimeBj(new Timestamp(System.currentTimeMillis())).categoryCode(CategoryCodeEnum.FROM_AIRPORT).build()

        when: "执行校验方法"
        schedule.execute(order, Lists.newArrayList(t1, t2, t3, t4, t5, t6), new RewardVO(BigDecimal.ZERO,"", []))

        then: "验证校验结果"
        schedule.getRound() == 1
        schedule.getNext().size() == 1
    }

    @Unroll
    def "test schedule is executing"() {

        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(20).roundRetryFactor([]).build()
//        def t1 = ScheduleTaskDO.builder().taskId(1).status( ScheduleTaskStatus.INIT).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
//        def t2 = ScheduleTaskDO.builder().taskId(2).status( ScheduleTaskStatus.INIT).priority(2).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
//        def t3 = ScheduleTaskDO.builder().taskId(3).status( ScheduleTaskStatus.COMPLETE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
//        def t4 = ScheduleTaskDO.builder().taskId(4).status( ScheduleTaskStatus.COMPLETE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
//        def t5 = ScheduleTaskDO.builder().taskId(5).status( ScheduleTaskStatus.EXECUTE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).executeTime(DateUtil.addSeconds(new Date(), -45)).build()
//        def t6 = ScheduleTaskDO.builder().taskId(6).status( ScheduleTaskStatus.EXECUTE).priority(2).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).executeTime(DateUtil.addSeconds(new Date(), -15)).build()
        def t1 = ScheduleTaskDO.builder().taskId(1).status( ScheduleTaskStatus.EXECUTE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).executeTime(DateUtil.addSeconds(new Date(), -9)).build()
        def schedule = ScheduleDO.builder().scheduleId(1).dspOrderId("1").strategy(new ScheduleStrategyVO()).round(0).status(ScheduleStatus.EXECUTING).build()

        when: "执行校验方法"
        boolean result = schedule.isExecuting(Lists.newArrayList(t1))

        then: "验证校验结果"
        result == true
    }

    @Unroll
    def "test schedule shutdown"() {
        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(30).build()
        def t1 = ScheduleTaskDO.builder().taskId(1).status( ScheduleTaskStatus.INIT).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
        def t2 = ScheduleTaskDO.builder().taskId(2).status( ScheduleTaskStatus.INIT).priority(2).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
        def t3 = ScheduleTaskDO.builder().taskId(3).status( ScheduleTaskStatus.COMPLETE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
        def t4 = ScheduleTaskDO.builder().taskId(4).status( ScheduleTaskStatus.COMPLETE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
        def t5 = ScheduleTaskDO.builder().taskId(5).status( ScheduleTaskStatus.EXECUTE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).executeTime(DateUtil.addSeconds(new Date(), -45)).build()
        def t6 = ScheduleTaskDO.builder().taskId(6).status( ScheduleTaskStatus.EXECUTE).priority(2).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).executeTime(DateUtil.addSeconds(new Date(), -15)).build()
        def schedule = ScheduleDO.builder().scheduleId(1).dspOrderId("1").strategy(new ScheduleStrategyVO()).round(0).status(ScheduleStatus.EXECUTING).build()

        when: "执行校验方法"
        schedule.shutdown(Lists.newArrayList(t1, t2, t3, t4, t5, t6))
        boolean shutdown = schedule.isShutdown()

        then: "验证校验结果"
        schedule.getStatus() == ScheduleStatus.SHUTDOWN
        schedule.getShutdownTime() != null
        schedule.getCancel().size() == 6
        shutdown
    }

    def "test get Highest Priority Task"() {
        given:
        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(30).build()
        def t1 = ScheduleTaskDO.builder().taskId(1).status( ScheduleTaskStatus.INIT).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
        def t2 = ScheduleTaskDO.builder().taskId(2).status( ScheduleTaskStatus.INIT).priority(2).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()

        when:
        List<ScheduleTaskDO> result = ScheduleDO.getHighestPriorityTask([t1, t2])

        then:
        result.size() == 1
        result[0].getTaskId() == 2L
    }

    def "test getWaitExecuteTask"() {
        given:
        def subSku = SubSkuVO.builder().subSkuId(1).subSkuName("1").dspType(DspType.SYSTEM_ASSIGN).takenType(TakenType.ASSISTANT).retrySecond(30).build()
        def t1 = ScheduleTaskDO.builder().taskId(1).status( ScheduleTaskStatus.WAIT_EXECUTE).priority(1).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()
        def t2 = ScheduleTaskDO.builder().taskId(2).status( ScheduleTaskStatus.WAIT_EXECUTE).priority(2).scheduleId(1L).dspOrderId("1").subSku(subSku).reward(new Money()).createTime(new Date()).build()

        when:
        List<ScheduleTaskDO> result1 = ScheduleDO.getWaitExecuteTask([])
        List<ScheduleTaskDO> result2 = ScheduleDO.getWaitExecuteTask([t1,t2])

        then:
        result1.size() == 0
        result2.size() == 2
    }

    @Unroll
    def "test schedule retry"() {
        def t1 = ScheduleTaskDO.builder().taskId(1).status( ScheduleTaskStatus.INIT).priority(1).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def t2 = ScheduleTaskDO.builder().taskId(2).status( ScheduleTaskStatus.INIT).priority(2).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def t3 = ScheduleTaskDO.builder().taskId(3).status( ScheduleTaskStatus.COMPLETE).priority(1).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 40, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def t4 = ScheduleTaskDO.builder().taskId(4).status( ScheduleTaskStatus.COMPLETE).priority(1).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(DateUtil.addSeconds(new Date(), -20)).round(0).build()
        def t5 = ScheduleTaskDO.builder().taskId(5).status( ScheduleTaskStatus.CANCEL).priority(1).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def t6 = ScheduleTaskDO.builder().taskId(6).status( ScheduleTaskStatus.COMPLETE).priority(2).scheduleId(1L).dspOrderId("1").subSku(new SubSkuVO(retrySecond: 10, roundRetryFactor: [])).reward(new Money()).createTime(new Date()).executeTime(new Date()).round(0).build()
        def schedule = ScheduleDO.builder().scheduleId(1).dspOrderId("1").strategy(new ScheduleStrategyVO()).round(0).status(ScheduleStatus.EXECUTING).build()
        def order = DspOrderVO.builder().estimatedUseTimeBj(new Timestamp(System.currentTimeMillis())).lastConfirmTimeBj(new Timestamp(System.currentTimeMillis())).categoryCode(CategoryCodeEnum.FROM_AIRPORT).build()

        when: "执行校验方法"
        Long result1 = schedule.retry([])
        Long result2 = schedule.retry([t1, t2, t3, t4, t5, t6])

        then: "验证校验结果"
        result1 == null
        result2 == 30L
    }

}
