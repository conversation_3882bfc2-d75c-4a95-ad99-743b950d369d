package com.ctrip.dcs.domain.schedule.sort.feature.impl

import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification

class DriverOrderGapDurationFeatureTest extends Specification {

    def sortContext = Mock(SortContext)
    def dispatchContext = Mock(DspContext)

    def "test(#description)"() {
        given:
        var target = new DriverOrderGapDurationFeature()
        and:
        var driver = new DriverVO(driverId: driverId, workTimes: new PeriodsVO(workTimes))
        var dispatchOrder = Mock(DspOrderVO)
        var forwardOrder = Mock(DspOrderVO)
        var sortModel = new SortModel(new DspModelVO(dispatchOrder, driver))
        var forwardOrderMap = hasForwardOrder ? Map.of(driverId, forwardOrder) : Collections.emptyMap()
        when:
        sortContext.getDspOrder() >> dispatchOrder
        sortContext.getDspContext() >> dispatchContext
        dispatchContext.getDriverForwardOrderMap() >> forwardOrderMap
        dispatchOrder.getEstimatedUseTime() >> DateUtil.parseDateStr2Date(dispatchOrderEstimateUseTime)
        forwardOrder.getEstimatedUseTime() >> DateUtil.parseDateStr2Date(forwardOrderEstimateUseTime)
        forwardOrder.getPredicServiceStopTime() >> DateUtil.parseDateStr2Date(forwardOrderEstimateEndTime)
        sortContext.getCityProperties(DriverOrderGapDurationFeature.NORMALIZE_CONFIG_MAX, _, _) >> 720
        sortContext.getCityProperties(DriverOrderGapDurationFeature.NORMALIZE_CONFIG_MIN, _, _) >> 0
        then:
        var result = target.value(sortModel, sortContext)
        var duration = result.value
        target.normalize(sortContext, [result])
        var score = result.value
        expect:
        duration == expectedDuration
        Math.abs(expectedScore - score) < 1e-5
        where:
        description             | driverId | workTimes       | dispatchOrderEstimateUseTime | hasForwardOrder | forwardOrderEstimateUseTime | forwardOrderEstimateEndTime | expectedDuration | expectedScore
        "无前向单+在工作时间内" | 1L | ["08:30~14:30"] | "2025-01-15 10:20:00" | false | null | null | 110D | 0.84722222
        "无前向单+在工作时间外" | 2L | ["08:30~14:30"] | "2025-01-15 07:20:00" | false | null | null | Double.MAX_VALUE | 0.0
        "距离前向单2小时" | 3L | ["08:30~14:30"] | "2025-01-15 10:20:00" | true | null | "2025-01-15 08:20:00" | 120D | 0.83333333
    }

}