package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DowngradeCarTypeOrderTransportGroupCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    TransportGroupVO transportGroup
    @Mock
    ConfigService configService
    @Mock
    DriverLocationVO driverLocationVO
    @Mock
    DriverLocationVO.LocationVO locationVO
    @InjectMocks
    DowngradeCarTypeOrderTransportGroupCheck downgradeCarTypeOrderTransportGroupCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(context.getConfigService()).thenReturn(configService)
    }

    def "check"() {
        given:
        Mockito.when(order.getCarTypeId()).thenReturn(117)
        Mockito.when(order.getOrderExtendAttributeInfo()).thenReturn([new OrderExtendAttributeVO("is_downgrade_car_type_order", is_downgrade_car_type_order)])
        Map<Long, CarTypeLevelRelation> map = Maps.newHashMap()
        map.put(121L, CarTypeLevelRelation.BELOW)
        map.put(118L, CarTypeLevelRelation.PARALLEL)
        map.put(117, CarTypeLevelRelation.HIGHER)
        CarTypeLevelRelationsVO carTypeLevelRelations = new CarTypeLevelRelationsVO(map)
        Mockito.when(context.carTypeRelations(Mockito.any())).thenReturn(carTypeLevelRelations)
        Mockito.when(transportGroup.getTransportGroupMode()).thenReturn(transportGroupMode)

        when:
        CheckCode result = downgradeCarTypeOrderTransportGroupCheck.check(checkModel, context)

        then:
        result == code

        where:
        is_downgrade_car_type_order | transportGroupMode                  || code
        "0"                         | TransportGroupMode.FULL_TIME_ASSIGN || CheckCode.PASS
        "1"                         | TransportGroupMode.FULL_TIME_ASSIGN || CheckCode.PASS
        "1"                         | TransportGroupMode.MANUAL_DISPATCH  || CheckCode.DOWNGRADE_CAR_TYPE_ORDER_NOT_IN_TRANSPORT_GROUP
    }

    def "downgrade"() {
        given:
        when:
        CheckCode result = downgradeCarTypeOrderTransportGroupCheck.downgrade()
        then:
        result == CheckCode.DOWNGRADE_CAR_TYPE_ORDER_NOT_IN_TRANSPORT_GROUP
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme