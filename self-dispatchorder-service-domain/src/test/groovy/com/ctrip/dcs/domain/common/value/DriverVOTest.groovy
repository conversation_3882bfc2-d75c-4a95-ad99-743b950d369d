package com.ctrip.dcs.domain.common.value

import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.dsporder.value.XSkuCategoryCode
import spock.lang.Specification
import spock.lang.Unroll

class DriverVOTest extends Specification {

    @Unroll
    def "test grab Work Time"() {
        given:
        def driverVO = new DriverVO(
                transportGroups: transportGroups,
                workTimes: workTimes,
                bookTimeHour: bookTimeHour,
        )
        def useTimeVO = new EstimatedUseTimeVO(new GregorianCalendar(2023, Calendar.APRIL, 1, 13, 53).getTime())

        when:
        boolean result = driverVO.grabWorkTime(useTimeVO)

        then:
        result == grabWorkTime

        where:
        transportGroups                                                                                                                                                   | workTimes                      | bookTimeHour                   || grabWorkTime
        null                                                                                                                                                              | null                           | null                           || true
        []                                                                                                                                                                | null                           | null                           || true
        [new TransportGroupVO()]                                                                                                                                          | null                           | null                           || true
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST)]                                                                                | null                           | null                           || true
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["12:00~14:00"]) | new PeriodsVO(["9:00~10:00"])  || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["13:00~14:00"]) | new PeriodsVO(["9:00~10:00"])  || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["12:00~13:00"]) | new PeriodsVO(["9:00~10:00"])  || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["09:00~08:00"]) | new PeriodsVO(["12:00~14:00"]) || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["09:00~08:00"]) | new PeriodsVO(["13:00~14:00"]) || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["09:00~08:00"]) | new PeriodsVO(["13:00~13:00"]) || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["09:00~08:00"]) | new PeriodsVO(["13:00~12:00"]) || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["09:00~08:00"]) | new PeriodsVO(["12:00~13:00"]) || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["09:00~08:00"]) | new PeriodsVO(["9:00~10:00"])  || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["09:00~08:00"]) | new PeriodsVO(["7:00~6:00"])   || false
        [new TransportGroupVO(transportGroupMode: TransportGroupMode.PART_TIME_BROADCAST), new TransportGroupVO(transportGroupMode: TransportGroupMode.FULL_TIME_ASSIGN)] | new PeriodsVO(["09:00~08:00"]) | new PeriodsVO(["7:00~10:00"])  || false
    }

    @Unroll
    def "test find Hit Work Time"() {
        given:
        def driverVO = new DriverVO(workTimes: new PeriodsVO([workTimes]))
        def dspOrderVo = DspOrderVO.builder().estimatedUseTime(DateUtil.parseDateStr2Date(estimatedUseTime)).build()

        when:
        boolean result = driverVO.findHitWorkTime(dspOrderVo)

        then:
        result == hit

        where:
        workTimes     | estimatedUseTime      || hit
        "08:00~12:59" | "2023-06-15 10:00:00" || true
        "08:00~12:59" | "2023-06-15 13:00:00" || false
        "08:00~12:59" | "2023-06-15 07:59:59" || false
        "20:00~02:59" | "2023-06-15 21:59:59" || true
        "20:00~02:59" | "2023-06-15 01:59:59" || true
        "20:00~02:59" | "2023-06-15 19:59:59" || false
        "20:00~02:59" | "2023-06-15 03:59:59" || false
    }

    @Unroll
    def "test find Hit Work Time 1"() {
        given:
        def driverVO = new DriverVO(workTimes: new PeriodsVO([workTimes]))
        def dspOrderVo = DspOrderVO.builder().estimatedUseTime(DateUtil.parseDateStr2Date(estimatedUseTime)).build()

        when:
        DriverWorkTimeVO result = driverVO.findHitWorkTime(dspOrderVo)

        then:
        DateUtil.formatDate(result.getStart(), DateUtil.DATETIME_FORMAT) == start
        DateUtil.formatDate(result.getEnd(), DateUtil.DATETIME_FORMAT) == end

        where:
        workTimes     | estimatedUseTime      || start                 | end
        "08:00~12:59" | "2023-06-15 10:00:00" || "2023-06-15 08:00:00" | "2023-06-15 12:59:00"
        "20:00~02:59" | "2023-06-15 21:59:59" || "2023-06-15 20:00:00" | "2023-06-16 02:59:00"
        "20:00~02:59" | "2023-06-15 01:59:59" || "2023-06-14 20:00:00" | "2023-06-15 02:59:00"
    }

    @Unroll
    def "test is Register Driver"() {
        given:
        def driverVO = new DriverVO(coopMode: coopMode, transportGroups: transportGroup)

        when:
        boolean result = driverVO.isRegisterDriver()

        then:
        result == hit

        where:
        coopMode | transportGroup                                                                         || hit
        1        | null                                                                                   || false
        4        | null                                                                                   || false
        4        | [new TransportGroupVO(transportGroupMode: TransportGroupMode.DEFAULT_MANUAL_DISPATCH)] || false
        4        | [new TransportGroupVO(transportGroupMode: TransportGroupMode.REGISTER_DISPATCH)]       || true
    }

    @Unroll
    def "test supported Product"() {
        given:
        def driverVO = new DriverVO(raisingPickUp: true, childSeat: true)
        XproductVO xproductVO = new XproductVO(
                XSkuCategoryCode.PICK_UP_CARD.getCode(),
                1,
                1,
                BigDecimal.ONE,
                "",
                1,
                BigDecimal.TEN,
                BigDecimal.TEN,
                "",
                "xProductName",
                "supplierCategoryCode"
        )
        when:
        def result = driverVO.supportedProduct([xproductVO])

        then:
        result.size() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme