package com.ctrip.dcs.domain.schedule.process.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.GrabOrderType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.IdempotentCheckService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.util.JsonUtil
import com.ctrip.dcs.domain.common.value.BaseDetailVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.graborder.GrabOrderDTO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.factory.GrabOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway
import com.ctrip.dcs.domain.schedule.repository.GrabCentreRepository
import com.ctrip.dcs.domain.schedule.repository.GrabOrderDetailRepository
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.clogging.LoggerContext
import com.fasterxml.jackson.core.type.TypeReference
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import javax.annotation.Resource

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class GrabCentreProcessTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    RecommendService recommendService
    @Mock
    CheckService checkService
    @Mock
    MessageProviderService messageProducer
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    GrabOrderFactory grabOrderFactory
    @Mock
    GrabCentreRepository grabCentreRepository
    @Mock
    DspContextService dspContextService
    @Mock
    IdempotentCheckService idempotentCheckService
    @Mock
    DriverOrderFactory driverOrderFactory
    @Mock
    ScheduleTaskDO scheduleTaskDO
    @Mock
    DspOrderVO dspOrderVO;
    @Mock
    SubSkuVO subSkuVO
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DriverVO driverVO;
    @Mock
    TransportGroupVO transportGroupVO;
    @Mock
    SupplierVO supplierVO
    @Mock
    GrabOrderDO grabOrderDO
    @Mock
    SelfOrderQueryGateway selfOrderQueryGateway
    @Mock
    GrabOrderDetailRepository grabOrderDetailRepository
    @Mock
    SysSwitchConfigGateway sysSwitchConfigGateway;
    @InjectMocks
    GrabCentreProcess grabCentreProcess


    def setup() {
        MockitoAnnotations.initMocks(this)
        when(transportGroupVO.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(dspModelVO.getTransportGroup()).thenReturn(transportGroupVO)
        when(driverVO.getTransportGroups()).thenReturn([transportGroupVO])
        when(driverVO.getSupplier()).thenReturn(supplierVO)
        when(subSkuVO.getSubSkuId()).thenReturn(1)
        when(subSkuVO.getTakenType()).thenReturn(TakenType.ASSISTANT)
        when(subSkuVO.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        when(scheduleTaskDO.getTaskId()).thenReturn(1L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(1L)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("1")
        when(scheduleTaskDO.getRound()).thenReturn(1)
        when(checkModel.isPass()).thenReturn(true)
        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(checkService.check(any() as TakenCheckCommand)).thenReturn(checkModel)
        when(recommendService.recommend(any(), any(), any())).thenReturn([new SortModel(dspModelVO)])
    }

    def "test execute"() {
        given:
        when(broadcastGrabConfig.getInteger(anyString(), anyInt())).thenReturn(0)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(grabOrderFactory.create(any(), any(), any(), any())).thenReturn([grabOrderDO])
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)

        when:
        def result = grabCentreProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }

    def "test execute 1"() {
        given:
        when(broadcastGrabConfig.getInteger(anyString(), anyInt())).thenReturn(0)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(grabOrderFactory.create(any(), any(), any(), any())).thenReturn([grabOrderDO])
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)

        when:
        def result = grabCentreProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }

    def "test execute 2"() {
        given:
        when(broadcastGrabConfig.getInteger(anyString(), anyInt())).thenReturn(0)
        when(broadcastGrabConfig.getLong(anyString(), anyLong())).thenReturn(1l)
        when(grabOrderFactory.create(any(), any(), any(), any())).thenReturn([grabOrderDO])
        CheckModel checkModel1 = new CheckModel()
        checkModel1.setCheckCode(CheckCode.PASS)
        when(checkService.grabCheck(any())).thenReturn([checkModel1])
        when(idempotentCheckService.isNotProcessed(anyString(), anyLong())).thenReturn(true)
        when:
        def result = grabCentreProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        result == null
    }

    def "test sendGrabOrderExpireMessage"() {
        given:
        List<GrabOrderDO> list = buildGrabOrderDOList();

        when:
        def result = grabCentreProcess.sendGrabOrderExpireMessage(list)

        then:
        result == res

        where:
        req                    || res
        Lists.newArrayList()   || null
        buildGrabOrderDOList() || null
    }


    def "test saveGrabOrderDetail"() {
        given:
        when(selfOrderQueryGateway.queryGrabDriverList(anyString(), anyString(), any())).thenReturn(buildGrabOrderDTO)
        SubSkuVO subSku = new SubSkuVO(0, "subSkuName", DspType.SYSTEM_ASSIGN, TakenType.DEFAULT, null, null, 1l)
        DuidVO duid = DuidVO.of("1-1-1-1-1-1-1-1-1-1-1");
        when:
        def res = grabCentreProcess.saveGrabOrderDetail("dspOrderId", drivers, subSku, duid, grabOrders)

        then:

        res == expectRes

        where:
        drivers             | grabOrders             | buildGrabOrderDTO    || expectRes
//        buildDriverVOList() | buildGrabOrderDOList() | buildGrabOrderDTO()  || null
        buildDriverVOList() | buildGrabOrderDOList() | Lists.newArrayList() || null

    }


    def "test saveGrabOrderDetail1"() {
        given:
        when(selfOrderQueryGateway.queryGrabDriverList(anyString(), anyString(), any())).thenReturn(Lists.newArrayList())
        SubSkuVO subSku = new SubSkuVO(0, "subSkuName", DspType.SYSTEM_ASSIGN, TakenType.DEFAULT, null, null, 1l)
        DuidVO duid = DuidVO.of("1-1-1-1-1-1-1-1-1-1-1");
        when:
        def res = grabCentreProcess.saveGrabOrderDetail("dspOrderId", drivers, subSku, duid, grabOrders)

        then:

        res == expectRes

        where:
        drivers             | grabOrders             | buildGrabOrderDTO    || expectRes
        buildDriverVOList() | buildGrabOrderDOList() | Lists.newArrayList() || null

    }


    private List<GrabOrderDO> buildGrabOrderDOList() {
        GrabOrderDO grabOrderDO1 = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId", "1", 1, 1L, 1l, 1l, 1)
        GrabOrderDO grabOrderDO2 = new GrabOrderDO("1-1-1-1-1-1-1-1-1-1", "dspOrderId", "1", 1, 2L, 1l, 1l, 1)
        return Lists.newArrayList(grabOrderDO1, grabOrderDO2)
    }

    private List<DriverVO> buildDriverVOList() {
        DriverVO driverVO1 = new DriverVO(1L, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [null], null, null, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", null, 0, 1l, null, YesOrNo.NO, false,0,1, "", "", "", "","",null)
        DriverVO driverVO2 = new DriverVO(2L, "driverName", 0, "driverPhone", "driverPhoneAreaCode", "driverLanguage", 0d, 0d, "email", "wechat", "picUrl", [null], null, null, 0, null, Boolean.TRUE, Boolean.TRUE, "intendVehicleTypeId", null, 0, 1l, null, YesOrNo.NO, false,0,1, "", "","","","",null)
        return Lists.newArrayList(driverVO1, driverVO2)
    }


    private List<GrabOrderDTO> buildGrabOrderDTO() {
        String str = "[{\"dspOrderId\":\"881836781523483541\",\"driverId\":1,\"duid\":\"11111111\",\"basicInfo\":{\"platformSupplyOrderId\":\"162644194319204388\",\"supplyOrderId\":\"881836781523483541\",\"userOrderId\":\"31157645594\",\"cityName\":\"上海\",\"cityCode\":\"shanghai_city\",\"cityId\":2,\"serviceType\":2,\"orderCarType\":117,\"orderCarTypeName\":\"经济5座\",\"orderStatus\":3,\"bookTime\":\"2024-03-28 17:56:00\",\"sysExpectBookTime\":\"2024-03-28 17:56:00\",\"orderTime\":\"2024-03-28 15:00:00\",\"ota\":0,\"orderVersion\":4,\"pickupPointId\":\"\",\"adultCount\":4,\"childCount\":0,\"bagCount\":0,\"maxBagCount\":0,\"bizAreaType\":0,\"extraFeeEffectiveTime\":\"\",\"kiloLength\":36.0,\"takenType\":1,\"takeUseTime\":\"2024-03-28 17:56:00\",\"specialTimePoints\":0,\"waitMinute\":0,\"uid\":\"2179341790\",\"highUser\":0},\"orderFeeInfo\":{},\"pointInfo\":{\"fromInfo\":{\"cityId\":2,\"cityCode\":\"shanghai_city\",\"name\":\"虹桥国际机场T2航站楼\",\"adress\":\"虹桥国际机场 T2\",\"longitude\":121.327447,\"latitude\":31.192568,\"type\":\"GCJ02\"},\"toInfo\":{\"cityId\":83,\"cityCode\":\"kunshan\",\"name\":\"朗绿花园-西北门\",\"adress\":\"昆山市 光明路1388号\",\"longitude\":121.083633,\"latitude\":31.29804,\"type\":\"GCJ02\"}},\"driverGrabOrderInfo\":{\"tipsDelaySecond\":10,\"distanceFormDriver\":43468.0,\"driverId\":1}},{\"dspOrderId\":\"881836781523483541\",\"driverId\":2,\"duid\":\"11111111\",\"basicInfo\":{\"platformSupplyOrderId\":\"162644194319204388\",\"supplyOrderId\":\"881836781523483541\",\"userOrderId\":\"31157645594\",\"cityName\":\"上海\",\"cityCode\":\"shanghai_city\",\"cityId\":2,\"serviceType\":2,\"orderCarType\":117,\"orderCarTypeName\":\"经济5座\",\"orderStatus\":3,\"bookTime\":\"2024-03-28 17:56:00\",\"sysExpectBookTime\":\"2024-03-28 17:56:00\",\"orderTime\":\"2024-03-28 15:00:00\",\"ota\":0,\"orderVersion\":4,\"pickupPointId\":\"\",\"adultCount\":4,\"childCount\":0,\"bagCount\":0,\"maxBagCount\":0,\"bizAreaType\":0,\"extraFeeEffectiveTime\":\"\",\"kiloLength\":36.0,\"takenType\":1,\"takeUseTime\":\"2024-03-28 17:56:00\",\"specialTimePoints\":0,\"waitMinute\":0,\"uid\":\"2179341790\",\"highUser\":0},\"orderFeeInfo\":{},\"pointInfo\":{\"fromInfo\":{\"cityId\":2,\"cityCode\":\"shanghai_city\",\"name\":\"虹桥国际机场T2航站楼\",\"adress\":\"虹桥国际机场 T2\",\"longitude\":121.327447,\"latitude\":31.192568,\"type\":\"GCJ02\"},\"toInfo\":{\"cityId\":83,\"cityCode\":\"kunshan\",\"name\":\"朗绿花园-西北门\",\"adress\":\"昆山市 光明路1388号\",\"longitude\":121.083633,\"latitude\":31.29804,\"type\":\"GCJ02\"}},\"driverGrabOrderInfo\":{\"tipsDelaySecond\":10,\"distanceFormDriver\":6701.0,\"driverId\":2}}]";
        List<Long> list = JsonUtil.fromJson(str, new TypeReference<List<GrabOrderDTO>>() {
        });
        return list;
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
