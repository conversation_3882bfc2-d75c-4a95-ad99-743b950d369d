package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class GrabWorkTimeCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    TransportGroupVO transportGroup
    @InjectMocks
    GrabWorkTimeCheck grabWorkTimeCheck = new GrabWorkTimeCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "兼职/调度司机：订单的预估用车时间在司机的抢单时段范围内"() {
        given:
        DriverVO driver = DriverVO.builder()
                .transportGroups(Lists.newArrayList(transportGroup))
                .bookTimeHour(new PeriodsVO(["09:00~18:00"]))
                .build()
        Mockito.when(dspModel.getDriver()).thenReturn(driver)
        Mockito.when(order.estimatedUseTime()).thenReturn(new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2023-05-15 12:00:00")))
        Mockito.when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.DEFAULT_MANUAL_DISPATCH)

        when:
        CheckCode result = grabWorkTimeCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "兼职/调度司机：订单的预估用车时间不在司机的抢单时段范围内"() {
        given:
        DriverVO driver = DriverVO.builder()
                .transportGroups(Lists.newArrayList(transportGroup))
                .bookTimeHour(new PeriodsVO(["09:00~18:00"]))
                .build()
        Mockito.when(dspModel.getDriver()).thenReturn(driver)
        Mockito.when(order.estimatedUseTime()).thenReturn(new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2023-05-15 19:00:00")))
        Mockito.when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.DEFAULT_MANUAL_DISPATCH)

        when:
        CheckCode result = grabWorkTimeCheck.check(checkModel, context)

        then:
        result == CheckCode.RPC_DOWN_GRADE
    }

    def "全职:订单的预估用车时间不在司机的工作时段内，并且在抢单时段范围内"() {
        given:
        DriverVO driver = DriverVO.builder()
                .transportGroups(Lists.newArrayList(transportGroup))
                .bookTimeHour(new PeriodsVO(["09:00~18:00"]))
                .workTimes(new PeriodsVO(["06:00~08:00"]))
                .build()
        Mockito.when(dspModel.getDriver()).thenReturn(driver)
        Mockito.when(order.estimatedUseTime()).thenReturn(new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2023-05-15 12:00:00")))
        Mockito.when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.REGISTER_DISPATCH)

        when:
        CheckCode result = grabWorkTimeCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "全职:订单的预估用车时间在司机的工作时段内"() {
        given:
        DriverVO driver = DriverVO.builder()
                .transportGroups(Lists.newArrayList(transportGroup))
                .bookTimeHour(new PeriodsVO(["09:00~18:00"]))
                .workTimes(new PeriodsVO(["06:00~13:00"]))
                .build()
        Mockito.when(dspModel.getDriver()).thenReturn(driver)
        Mockito.when(order.estimatedUseTime()).thenReturn(new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2023-05-15 12:00:00")))
        Mockito.when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.REGISTER_DISPATCH)

        when:
        CheckCode result = grabWorkTimeCheck.check(checkModel, context)

        then:
        result == CheckCode.RPC_DOWN_GRADE
    }

    def "全职:订单的预估用车时间不在司机的工作时段内，但不在抢单时段范围内"() {
        given:
        DriverVO driver = DriverVO.builder()
                .transportGroups(Lists.newArrayList(transportGroup))
                .bookTimeHour(new PeriodsVO(["09:00~18:00"]))
                .workTimes(new PeriodsVO(["06:00~13:00"]))
                .build()
        Mockito.when(dspModel.getDriver()).thenReturn(driver)
        Mockito.when(order.estimatedUseTime()).thenReturn(new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2023-05-15 19:00:00")))
        Mockito.when(transportGroup.getTransportGroupMode()).thenReturn(TransportGroupMode.REGISTER_DISPATCH)

        when:
        CheckCode result = grabWorkTimeCheck.check(checkModel, context)

        then:
        result == CheckCode.RPC_DOWN_GRADE
    }

    @Unroll
    def "test check"() {
        given:
        DriverVO driver = Mock()
        driver.grabWorkTime(_) >> grabWorkTime
        CheckModel checkModel = new CheckModel(
                model: new DspModelVO(
                        order: new DspOrderVO(),
                        driver: driver
                ),
        )
        CheckContext context = Mock()

        when:
        CheckCode result = grabWorkTimeCheck.check(checkModel, context)

        then:
        result == r

        where:
        grabWorkTime || r
        true         || CheckCode.PASS
        false        || CheckCode.RPC_DOWN_GRADE
    }

    def "test downgrade"() {
        when:
        CheckCode result = grabWorkTimeCheck.downgrade()

        then:
        result == CheckCode.RPC_DOWN_GRADE
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme