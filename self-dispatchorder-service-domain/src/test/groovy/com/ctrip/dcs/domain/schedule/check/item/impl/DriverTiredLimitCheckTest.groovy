package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.dcs.domain.schedule.visitor.Visitor
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Sets
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverTiredLimitCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CheckModel checkModel
    @Mock
    CheckContext checkContext
    @Mock
    DspModelVO dspModel
    @Mock
    DriverVO driver
    @InjectMocks
    DriverTiredLimitCheck driverTiredLimitCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test check"() {
        given:
        when(checkContext.getTiredLimitDriverIds()).thenReturn(Sets.newHashSet(1L))
        when(checkModel.getModel()).thenReturn(dspModel)
        when(dspModel.getDriver()).thenReturn(driver)
        when(driver.getDriverId()).thenReturn(1L).thenReturn(2L)

        when:
        CheckCode result1 = driverTiredLimitCheck.check(checkModel, checkContext)
        CheckCode result2 = driverTiredLimitCheck.check(checkModel, checkContext)

        then:
        result1 == CheckCode.DRIVER_TIRED_LIMIT
        result2 == CheckCode.PASS
    }

    def "test downgrade"() {
        when:
        CheckCode result = driverTiredLimitCheck.downgrade()

        then:
        result == CheckCode.PASS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme