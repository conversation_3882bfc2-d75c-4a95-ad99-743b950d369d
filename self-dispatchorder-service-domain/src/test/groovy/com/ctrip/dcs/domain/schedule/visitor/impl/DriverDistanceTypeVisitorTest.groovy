package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.google.common.collect.Maps
import spock.lang.Specification

class DriverDistanceTypeVisitorTest extends Specification {

    CheckModel checkModel = Mock()
    DspModelVO dispatchModel = Mock()
    DriverVO driverModel = Mock()
    CheckContext checkContext = Mock()
    DspContext dispatchContext = Mock()
    DspContextService dispatchService = Mock()
    TransportGroupVO transportGroup = Mock()

    def "test load"() {
        given:
        checkModel.getModel() >> dispatchModel
        dispatchModel.getDriver() >> driverModel
        driverModel.getDriverId() >> driverId
        driverModel.getTransportGroups() >> [transportGroup]
        checkContext.getContext() >> dispatchContext
        dispatchContext.getService() >> dispatchService
        dispatchContext.getDriverDistanceTypeMap() >> Maps.newHashMap()
        dispatchService.queryTransportList(_) >> [transportGroup]
        transportGroup.getShortDisSwitch() >> transportGroupDistanceType
        when:
        new DriverDistanceTypeVisitor([driverModel]).visit(checkContext)
        then:
        dispatchContext.getDriverDistanceTypeMap().get(driverId) == expected
        where:
        driverId | transportGroupDistanceType | expected
        99L      | -1                         | null
    }

}