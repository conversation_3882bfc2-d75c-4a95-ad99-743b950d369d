package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.gateway.ConflictGateway
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverStaticInventoryIgnoreDelayPoolVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driverVO
    @Mock
    ConflictGateway conflictGateway
    @InjectMocks
    DriverStaticInventoryIgnoreDelayPoolVisitor driverStaticInventoryIgnoreDelayPoolVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(dspOrder.getDspOrderId()).thenReturn("getDspOrderIdResponse")
        when(dspOrder.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 16, 41).getTime())
        when(dspOrder.getPredicServiceStopTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 16, 41).getTime())
        when(conflictGateway.checkDriverInventory(any(), anyString(), anyString(), anyString(), anyBoolean(), anyString(), anyString(), anyString())).thenReturn([1l])
        when(dspOrder.getCategoryCode()).thenReturn(CategoryCodeEnum.DAY_RENTAL)
        DriverStaticInventoryIgnoreDelayPoolVisitor driverStaticInventoryIgnoreDelayPoolVisitor = new DriverStaticInventoryIgnoreDelayPoolVisitor(dspOrder, [driverVO], conflictGateway)
        when:
        driverStaticInventoryIgnoreDelayPoolVisitor.visit(checkContext)
        then:
        checkContext != null
    }
}
