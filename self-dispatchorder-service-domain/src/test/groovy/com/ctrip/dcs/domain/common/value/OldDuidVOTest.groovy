package com.ctrip.dcs.domain.common.value


import spock.lang.Specification

/**
 * <AUTHOR>
 */
class OldDuidVOTest extends Specification {
    OldDuidVO oldDuidVO = null

    def "test of"() {
        when:
        OldDuidVO result = OldDuidVO.of("881833786708809291-v2.0-12-1101-0:1:13-************-1-1-0-0")
        String s = result.toString()
        then:
        result.orderId == "881833786708809291"
        result.subSku == 1101
        s == "881833786708809291-v2.0-12-1101-0:1:13-************-1-1-0-0"
    }

    def "test isOldDuid"() {
        when:
        Boolean result = OldDuidVO.isOldDuid("881833786708809291-v2.0-12-1101-0:1:13-************-1-1-0-0")
        then:
        result == true
    }
}

//Generated with love by <PERSON><PERSON><PERSON> :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme