package com.ctrip.dcs.domain.common.value


import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverTiredLimitVOTest extends Specification {

    def "test "() {
        when:

        def driverTiredLimitVO = new DriverTiredLimitVO(1L,1)
        def r = driverTiredLimitVO.isTired();
        then:
        r==true
        driverTiredLimitVO.getDriverId()==1L
        driverTiredLimitVO.getTired()==1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme