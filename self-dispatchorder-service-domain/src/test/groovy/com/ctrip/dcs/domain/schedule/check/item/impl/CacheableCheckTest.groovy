package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.service.DspContextService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class CacheableCheckTest extends Specification {

    AbstractCheck check = Mock(AbstractCheck)

    CheckModel checkModel = Mock(CheckModel)

    DspModelVO dspModel = Mock(DspModelVO)

    DspOrderVO dspOrder = Mock(DspOrderVO)

    DriverVO driver = Mock(DriverVO)

    CheckContext checkContext = Mock(CheckContext)

    DspContext dspContext = Mock(DspContext)

    DspContextService dspContextService = Mock(DspContextService)
    SubSkuVO subSku = Mock(SubSkuVO)

    TransportGroupVO transportGroup = Mock(TransportGroupVO)

    def "Check"() {
        given:
        CacheableCheck cacheableCheck = new CacheableCheck(check, ["I1": 1])
        check.getCheckId() >> "I1"
        check.downgrade() >> CheckCode.NULL
        checkModel.getModel() >> dspModel
        dspModel.getOrder() >> dspOrder
        dspModel.getDriver() >> driver
        dspModel.getTransportGroup() >> transportGroup
        dspOrder.getDspOrderId() >> "1"
        driver.getDriverId() >> 2L
        transportGroup.getTransportGroupId() >> 3L
        checkContext.getContext() >> dspContext
        dspContext.getService() >> dspContextService
        dspContextService.queryCheckCode(_) >> ["100041593_CacheableCheck_I1_1_2_3":cacheCode]
        CheckModel checkModel = new CheckModel(dspModel)
        checkContext.getDspStage() >> stage
        check.check([checkModel], checkContext) >> [checkModel]
        checkContext.getSubSku() >> subSku
        subSku.getDspType() >> DspType.SYSTEM_ASSIGN

        when:
        List<CheckModel> list = cacheableCheck.check([checkModel], checkContext)

        then:
        list.size() == size
        checkModel.getCheckCode() == code

        where:
        stage          | cacheCode           || size | code
        DspStage.TAKEN | CheckCode.NO_ACTIVE || 1    | CheckCode.PASS
        DspStage.DSP   | CheckCode.NO_ACTIVE || 1    | CheckCode.NO_ACTIVE
        DspStage.DSP   | CheckCode.NULL      || 1    | CheckCode.PASS
    }

    def "CheckCache"() {
        given:
        CacheableCheck cacheableCheck = new CacheableCheck(check, ["I1": 1])
        check.getCheckId() >> "I1"
        check.downgrade() >> CheckCode.NULL
        checkModel.getModel() >> dspModel
        dspModel.getOrder() >> dspOrder
        dspModel.getDriver() >> driver
        dspModel.getTransportGroup() >> transportGroup
        dspOrder.getDspOrderId() >> "1"
        driver.getDriverId() >> 2L
        transportGroup.getTransportGroupId() >> 3L
        checkContext.getContext() >> dspContext
        dspContext.getService() >> dspContextService
        dspContextService.queryCheckCode(_) >> ["100041593_CacheableCheck_I1_1_2_3":cacheCode]
        CheckModel checkModel = new CheckModel(dspModel)
        checkContext.getSubSku() >> subSku
        subSku.getDspType() >> DspType.SYSTEM_ASSIGN

        when:
        List<CheckModel> list = cacheableCheck.checkCache([checkModel], checkContext)

        then:
        list.size() == size
        checkModel.getCheckCode() == code

        where:
        cacheCode           || size | code
        CheckCode.NO_ACTIVE || 0    | CheckCode.NO_ACTIVE
        CheckCode.NULL      || 1    | CheckCode.PASS
    }


    def "Downgrade"() {
        given:
        CacheableCheck cacheableCheck = new CacheableCheck(check, ["I1": 1])
        check.getCheckId() >> "I1"
        check.downgrade() >> CheckCode.NULL
        checkModel.getModel() >> dspModel
        dspModel.getOrder() >> dspOrder
        dspModel.getDriver() >> driver
        dspModel.getTransportGroup() >> transportGroup
        dspOrder.getDspOrderId() >> "1"
        driver.getDriverId() >> 2L
        transportGroup.getTransportGroupId() >> 3L

        when:
        CheckCode checkCode = cacheableCheck.downgrade()

        then:
        checkCode == CheckCode.NULL
    }

    def "GetCheckId"() {
        given:
        CacheableCheck cacheableCheck = new CacheableCheck(check, ["I1": 1])
        check.getCheckId() >> "I1"
        checkModel.getModel() >> dspModel
        dspModel.getOrder() >> dspOrder
        dspModel.getDriver() >> driver
        dspModel.getTransportGroup() >> transportGroup
        dspOrder.getDspOrderId() >> "1"
        driver.getDriverId() >> 2L
        transportGroup.getTransportGroupId() >> 3L

        when:
        String key = cacheableCheck.getCheckId()

        then:
        key == "I1"
    }

    def "GetCacheKey"() {
        given:
        CacheableCheck cacheableCheck = new CacheableCheck(check, ["I1": 1])
        check.getCheckId() >> "I1"
        checkModel.getModel() >> dspModel
        dspModel.getOrder() >> dspOrder
        dspModel.getDriver() >> driver
        dspModel.getTransportGroup() >> transportGroup
        dspOrder.getDspOrderId() >> "1"
        driver.getDriverId() >> 2L
        transportGroup.getTransportGroupId() >> 3L

        when:
        String key = cacheableCheck.getCacheKey(checkModel)

        then:
        key == "100041593_CacheableCheck_I1_1_2_3"
    }
}
