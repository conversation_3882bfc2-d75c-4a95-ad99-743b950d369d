package com.ctrip.dcs.domain.schedule.process.impl

import cn.hutool.core.math.Money
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotTypeEnum
import com.ctrip.dcs.domain.common.enums.GrabOrderCode
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDriverService
import com.ctrip.dcs.domain.common.service.QueryTransportGroupService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.value.graborder.abroad.VBKDriverGrabOrderDO
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.SelfOrderQueryGateway
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway
import com.ctrip.dcs.domain.schedule.repository.*
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.service.SortService
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.GrabOrderResultVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import java.util.concurrent.ExecutorService

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SelectGrabBroadcastGrabProcessTest extends Specification {
    @Mock
    ScheduleTaskRepository scheduleTaskRepository
    @Mock
    SortService sortService
    @Mock
    CheckService checkService
    @Mock
    DriverOrderFactory driverOrderFactory
    @Mock
    QueryDriverService queryDriverService
    @Mock
    ConfigService broadcastGrabConfig
    @Mock
    ConfigService broadcastGrabFailConfig
    @Mock
    QueryTransportGroupService queryTransportGroupService
    @Mock
    GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository
    @Mock
    GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository
    @Mock
    SelectVBKGrabOrderProcess selectVBKGrabOrderProcess
    @Mock
    RecommendService recommendService
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    MessageProviderService messageProducer
    @Mock
    SelfOrderQueryGateway selfOrderQueryGateway
    @Mock
    ExecutorService queryGrabDriverListThreadPool
    @Mock
    GrabOrderDetailRepository grabOrderDetailRepository
    @Mock
    DspOrderVO dspOrder
    @Mock
    CheckModel checkModel
    @Mock
    DriverVO driver
    @Mock
    VehicleVO vehicle
    @Mock
    TransportGroupVO transportGroup
    @Mock
    DriverOrderVO driverOrder
    @Mock
    ScheduleTaskDO task
    @Mock
    SubSkuVO subSku
    @Mock
    GrabDspOrderSnapshotDO snapshot
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderRewardStrategyRepository dspOrderRewardStrategyRepository;
    @Mock
    SysSwitchConfigGateway sysSwitchConfigGateway;
    @InjectMocks
    SelectGrabBroadcastGrabProcess selectGrabBroadcastGrabProcess

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    def "test validate"() {
        when:
        boolean result = selectGrabBroadcastGrabProcess.validate(order, grabSnapshot, indexes)

        then:
        result == flag

        where:
        order                                                                     | grabSnapshot                                                                    | indexes                           || flag
        null                                                                      | null                                                                            | null                              || false
        new DspOrderVO()                                                          | null                                                                            | null                              || false
        new DspOrderVO()                                                          | new GrabDspOrderSnapshotDO()                                                    | null                              || false
        new DspOrderVO()                                                          | new GrabDspOrderSnapshotDO(grabType: GrabDspOrderSnapshotTypeEnum.SYSTEM)       | []                                || false
        new DspOrderVO(orderStatus: OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) | new GrabDspOrderSnapshotDO(grabType: GrabDspOrderSnapshotTypeEnum.SYSTEM)       | [new GrabDspOrderDriverIndexDO()] || false
        new DspOrderVO(orderStatus: OrderStatusEnum.DISPATCH_CONFIRMED.getCode()) | new GrabDspOrderSnapshotDO(grabType: GrabDspOrderSnapshotTypeEnum.VBK_SUPPLIER) | [new GrabDspOrderDriverIndexDO()] || true
    }

    def "test query Submit Index"() {
        given:
        when(grabDspOrderDriverIndexRepository.querySubmitIndex(anyString(), any(Date.class))).thenReturn([new GrabDspOrderDriverIndexDO()])

        when:
        List<GrabDspOrderDriverIndexDO> result = selectGrabBroadcastGrabProcess.querySubmitIndex(new GrabDspOrderSnapshotDO(dspOrderId: "dspOrderId", tipsDelaySecond: 0))

        then:
        result.size() == 1
    }

    def "test query Transport Group"() {
        given:
        when(queryTransportGroupService.queryTransportGroup(anyLong())).thenReturn(transportGroup)
        when(snapshot.getGrabType()).thenReturn(GrabDspOrderSnapshotTypeEnum.SYSTEM).thenReturn(GrabDspOrderSnapshotTypeEnum.VBK_SUPPLIER)
        when(checkModel.getModel()).thenReturn(dspModel)
        when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        when(dspOrder.getTransportGroupId()).thenReturn(1L)
        when(transportGroup.getTransportGroupId()).thenReturn(1L)

        when:
        TransportGroupVO result1 = selectGrabBroadcastGrabProcess.queryTransportGroup(dspOrder, checkModel, snapshot)
        TransportGroupVO result2 = selectGrabBroadcastGrabProcess.queryTransportGroup(dspOrder, checkModel, snapshot)

        then:
        result1.getTransportGroupId() == 1L
        result2.getTransportGroupId() == 1L
    }

    def "test create Driver Order"() {
        given:
        when(driverOrderFactory.create(any(DspModelVO.class), any(ScheduleTaskDO.class))).thenReturn(new DriverOrderVO("1", "dspOrderId", 1l))
        when(driverOrderFactory.create(any(DspModelVO.class), any(TransportGroupVO.class), any(ScheduleTaskDO.class), any(VBKDriverGrabOrderDO.class), any())).thenReturn(new DriverOrderVO("2", "dspOrderId", 1l))
        when(selectVBKGrabOrderProcess.queryVBKDriverGrabOrder(any(DspOrderVO.class))).thenReturn(new VBKDriverGrabOrderDO())
        when(dspModel.getOrder()).thenReturn(dspOrder)
        when(dspModel.getDriver()).thenReturn(driver)
        when(dspOrder.getDspOrderId()).thenReturn("3")
        when(snapshot.getGrabType()).thenReturn(grabType)

        when:
        DriverOrderVO result = selectGrabBroadcastGrabProcess.createDriverOrder(dspModel, transportGroup, task, snapshot, null)

        then:
        result.getDriverOrderId() == driverOrderId

        where:
        grabType                                 || driverOrderId
        GrabDspOrderSnapshotTypeEnum.VBK_SUPPLIER | "2"
        GrabDspOrderSnapshotTypeEnum.SYSTEM       | "1"

    }

    def "test confirm Order"() {
        given:
        when(snapshot.getGrabType()).thenReturn(GrabDspOrderSnapshotTypeEnum.VBK_DISPATCHER)
        when(dspOrder.getSupplierId()).thenReturn(1)
        when(task.getSubSku()).thenReturn(subSku)
        when(task.getReward()).thenReturn(new Money(BigDecimal.ONE))
        when(task.getVbkReward()).thenReturn(new Money(BigDecimal.ONE))
        when(subSku.getDspType()).thenReturn(DspType.GRAB_BROADCAST)
        when(subSku.getTakenType()).thenReturn(TakenType.BROADCAST)
        when(driverOrder.getDriverOrderId()).thenReturn("1")
        when:
        String result = selectGrabBroadcastGrabProcess.confirmOrder(dspOrder, driver, vehicle, transportGroup, driverOrder, task, snapshot)

        then:
        result == GrabOrderCode.SUCCESS.getCode()
    }

    def "test notice"() {
        given:
        when(broadcastGrabConfig.getMap(anyString())).thenReturn(["getMapResponse": "getMapResponse"])
        when(broadcastGrabFailConfig.getMap(anyString())).thenReturn(["getMapResponse": "getMapResponse"])
        GrabOrderResultVO grabOrderResult = new GrabOrderResultVO(new GrabDspOrderDriverIndexDO())
        when:
        def result = selectGrabBroadcastGrabProcess.notice(dspOrder, [grabOrderResult])

        then:
        result == null
    }

    def "test get Grab Order Code"() {
        given:
        when(broadcastGrabConfig.getString(anyString(), anyString())).thenReturn("getStringResponse")
        when(broadcastGrabFailConfig.getString(anyString(), anyString())).thenReturn("getStringResponse")

        when:
        String result = selectGrabBroadcastGrabProcess.getGrabOrderCode(CheckCode.PASS)

        then:
        result == "getStringResponse"
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme