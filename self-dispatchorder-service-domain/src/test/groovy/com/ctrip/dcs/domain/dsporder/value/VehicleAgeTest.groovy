package com.ctrip.dcs.domain.dsporder.value

import org.junit.Assert
import spock.lang.Specification

/**
 * <AUTHOR>
 * @since 2025/5/6 11:09
 */
class VehicleAgeTest extends Specification {
    def testObj = new VehicleAge()


    def "test"() {
        given:
        when:
        testObj.setVehicleId(1L)
        testObj.setCityId(2L)
        testObj.setCarTypeId(3)
        testObj.setOverAgeTime("2024-05-12 10:00:00")
        testObj.setVehCreateTime("2024-05-13 10:00:00")
        testObj.setVehRegstDate("2024-05-13")

        then:
        Assert.assertTrue(Objects.equals(testObj.getVehicleId(),1L))
        Assert.assertTrue(Objects.equals(testObj.getCityId(),2L))
        Assert.assertTrue(Objects.equals(testObj.getCarTypeId(),3))
        Assert.assertTrue(Objects.equals(testObj.getOverAgeTime(),"2024-05-12 10:00:00"))
        Assert.assertTrue(Objects.equals(testObj.getVehCreateTime(),"2024-05-13 10:00:00"))
        Assert.assertTrue(Objects.equals(testObj.getVehRegstDate(),"2024-05-13"))
    }
}
