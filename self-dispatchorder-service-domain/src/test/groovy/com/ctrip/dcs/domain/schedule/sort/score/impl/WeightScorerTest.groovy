package com.ctrip.dcs.domain.schedule.sort.score.impl

import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class WeightScorerTest extends Specification {
//    F22:0_F21:0_F1:0.31048469_F2:0.08450868_F4:-0.1145399_F5:0.45742457_F6:0.19858314_F7:-0.0234921_F9:0.24901123_F11:0.13202058_F12:-0.0907708_F13:-0.0132077_F14:0.0883767_F23:0.06614971_F24:-0.0036687


    def "Sum"() {
        given:
        Map<String, Double> map = new HashMap<>()
        map.put("F1",0.31048469D)
        map.put("F2",0.08450868D)
        map.put("F4",-0.1145399D)
        map.put("F5",0.45742457D)
        map.put("F6",0.19858314D)
        map.put("F7",-0.0234921D)
        map.put("F9",0.24901123D)
        map.put("F11",0.13202058D)
        map.put("F12",-0.0907708D)
        map.put("F13",-0.0132077D)
        map.put("F14",0.0883767D)
        map.put("F23",0.06614971D)
        map.put("F24",-0.0036687D)

        WeightScorer weightScorer = new WeightScorer(map)
        when:
        double score = weightScorer.sum([new Value("F9", 0.5)])
        then:
        score != 0.5D
    }
}
