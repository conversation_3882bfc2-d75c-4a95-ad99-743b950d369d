package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.service.DriverDeadHeadDisService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.value.DeadHeadDisModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverDeadHeadDistanceVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driverVO
    @Mock
    DriverDeadHeadDisService driverDeadHeadDisService
    @Mock
    DriverLocationVisitor driverLocationVisitor
    @Mock
    DriverRelateOrderVisitor driverRelateOrderVisitor
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverWorkTimeVO driverWorkTimeVO
    @Mock
    DeadHeadDisModelVO deadHeadDisModelVO
    @InjectMocks
    DriverDeadHeadDistanceVisitor driverDeadHeadDistanceVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(checkContext.getContext()).thenReturn(dspContext)
        when(driverDeadHeadDisService.calculateDeadHeadDis(any(), any(), any(), any())).thenReturn([(1l): deadHeadDisModelVO])
        when(driverLocationVisitor.filter(any(), any())).thenReturn([driverVO])
        when(driverRelateOrderVisitor.filter(any(), any())).thenReturn([driverVO])
        DriverDeadHeadDistanceVisitor driverDeadHeadDistanceVisitor = new DriverDeadHeadDistanceVisitor(dspOrder, [driverVO], driverDeadHeadDisService, driverLocationVisitor, driverRelateOrderVisitor)

        when:
        def result = driverDeadHeadDistanceVisitor.visit(checkContext)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme