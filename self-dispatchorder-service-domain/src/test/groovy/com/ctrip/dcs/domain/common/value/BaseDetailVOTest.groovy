package com.ctrip.dcs.domain.common.value


import spock.lang.Specification

/**
 * <AUTHOR>
 */
class BaseDetailVOTest extends Specification {

    def "test"() {
        given:

        when:
        BaseDetailVO baseDetailVO = new BaseDetailVO();
        baseDetailVO.setUserOrderId("0");
        baseDetailVO.setSupplyOrderId("0");
        baseDetailVO.setDspOrderId("0");
        baseDetailVO.setDriverOrderId("0");
        baseDetailVO.setOrderStatus(0);
        baseDetailVO.setOldOrderStatus(0);
        baseDetailVO.setOldOrderStatusDetail(0);
        baseDetailVO.setProductType(0);
        baseDetailVO.setProductCode("0");
        baseDetailVO.setProductName("0");
        baseDetailVO.setCategoryCode("0");
        baseDetailVO.setServiceType(0);
        baseDetailVO.setCityId(0);
        baseDetailVO.setFromCityId(0);
        baseDetailVO.setToCityId(0);
        baseDetailVO.setVehicleGroupId(0);
        baseDetailVO.setEstimatedUseTime("0");
        baseDetailVO.setEstimatedUseTimeBj("2023-10-10 10:00:00");
        baseDetailVO.setPredicServiceStopTimeBj("0");
        baseDetailVO.setLastConfirmTime("0");
        baseDetailVO.setLastConfirmTimeBj("0");
        baseDetailVO.setLastConfirmCarTime("0");
        baseDetailVO.setLastConfirmCarTimeBj("0");
        baseDetailVO.setConnectMode(0);
        baseDetailVO.setSalesMode(0);
        baseDetailVO.setUseDays(new BigDecimal("0"));
        baseDetailVO.setEstimatedKm(new BigDecimal("0"));
        baseDetailVO.setEstimatedMin(new BigDecimal("0"));
        baseDetailVO.setActualKm(new BigDecimal("0"));
        baseDetailVO.setActualMin(new BigDecimal("0"));
        baseDetailVO.setPriceMark("0");
        baseDetailVO.setServiceProviderId(0);
        baseDetailVO.setSupplierId(0);
        baseDetailVO.setCountryId(0L);
        baseDetailVO.setLocale("0");
        baseDetailVO.setLanguageInfo("0");
        baseDetailVO.setSkuId(0);
        baseDetailVO.setCancelRule("0");
        baseDetailVO.setWaitingRule("0");
        baseDetailVO.setPriceResultCode("0");
        baseDetailVO.setDetailSnapShotid("0");
        baseDetailVO.setXproductInfo("0");
        baseDetailVO.setSpContractInfo("0");
        baseDetailVO.setDspStrategyStr("0");
        baseDetailVO.setOrderCarTypeId(0L);
        baseDetailVO.setDrvId(0L);
        baseDetailVO.setDrvName("0");
        baseDetailVO.setDrvPhone("0");
        baseDetailVO.setDrvPhoneAreaCode("0");
        baseDetailVO.setDrvCoopMode(0);
        baseDetailVO.setDrvLanguageCodeList("0");
        baseDetailVO.setCarId(0L);
        baseDetailVO.setCarTypeId(0L);
        baseDetailVO.setCarLicense("0");
        baseDetailVO.setCarColorId(0L);
        baseDetailVO.setCarBrandId(0L);
        baseDetailVO.setCarSeriesId(0L);
        baseDetailVO.setTransportGroupId(0L);
        baseDetailVO.setTransportGroupMode(0);
        baseDetailVO.setTransportGroupName("0");
        baseDetailVO.setConfirmMoneyTimeLocal("0");
        baseDetailVO.setConfirmMoneyTimeBj("0");
        baseDetailVO.setFinishTimeBj("0");
        baseDetailVO.setCancelTimeLocal("0");
        baseDetailVO.setCancelTimeBj("0");
        baseDetailVO.setBizAreaType(0);
        baseDetailVO.setAdultCount(0);
        baseDetailVO.setChildCount(0);
        baseDetailVO.setBagCount(0);
        baseDetailVO.setMaxBagCount(0);
        baseDetailVO.setCoordType("0");
        baseDetailVO.setFromLongitude(0.0D);
        baseDetailVO.setFromLatitude(0.0D);
        baseDetailVO.setToLongitude(0.0D);
        baseDetailVO.setToLatitude(0.0D);
        baseDetailVO.setTakenTimeBj("0");
        baseDetailVO.setTargetId("0");
        baseDetailVO.setTerminalId("0");
        baseDetailVO.setFixedLocationType(0);
        baseDetailVO.setFixedPosition("0");
        baseDetailVO.setPremiumOrderFlag(0);
        def day = baseDetailVO.isDay()
        def jnt = baseDetailVO.isJnt()
        def isInServiced = baseDetailVO.isInServiced()
        def isAirportPickup = baseDetailVO.isAirportPickup()
        def isSelf = baseDetailVO.isSelf()
        def isAllowUseRightSysBookTime = baseDetailVO.isAllowUseRightSysBookTime()
        def isDriverAppDispatcherTakenType = baseDetailVO.isDriverAppDispatcherTakenType()
        then:
        "0" == baseDetailVO.getUserOrderId();
        "0" == baseDetailVO.getSupplyOrderId();
        "0" == baseDetailVO.getDspOrderId();
        "0" == baseDetailVO.getDriverOrderId();
        0 == baseDetailVO.getOrderStatus();
        0 == baseDetailVO.getOldOrderStatus();
        0 == baseDetailVO.getOldOrderStatusDetail();
        0 == baseDetailVO.getProductType();
        "0" == baseDetailVO.getProductCode();
        "0" == baseDetailVO.getProductName();
        "0" == baseDetailVO.getCategoryCode();
        0 == baseDetailVO.getServiceType();
        0 == baseDetailVO.getCityId();
        0 == baseDetailVO.getFromCityId();
        0 == baseDetailVO.getToCityId();
        0 == baseDetailVO.getVehicleGroupId();
        "0" == baseDetailVO.getEstimatedUseTime();
        "2023-10-10 10:00:00" == baseDetailVO.getEstimatedUseTimeBj();
        "0" == baseDetailVO.getPredicServiceStopTimeBj();
        "0" == baseDetailVO.getLastConfirmTime();
        "0" == baseDetailVO.getLastConfirmTimeBj();
        "0" == baseDetailVO.getLastConfirmCarTime();
        "0" == baseDetailVO.getLastConfirmCarTimeBj();
        0 == baseDetailVO.getConnectMode();
        0 == baseDetailVO.getSalesMode();
        new BigDecimal("0") == baseDetailVO.getUseDays();
        new BigDecimal("0") == baseDetailVO.getEstimatedKm();
        new BigDecimal("0") == baseDetailVO.getEstimatedMin();
        new BigDecimal("0") == baseDetailVO.getActualKm();
        new BigDecimal("0") == baseDetailVO.getActualMin();
        "0" == baseDetailVO.getPriceMark();
        0 == baseDetailVO.getServiceProviderId();
        0 == baseDetailVO.getSupplierId();
        0L == baseDetailVO.getCountryId();
        "0" == baseDetailVO.getLocale();
        "0" == baseDetailVO.getLanguageInfo();
        0 == baseDetailVO.getSkuId();
        "0" == baseDetailVO.getCancelRule();
        "0" == baseDetailVO.getWaitingRule();
        "0" == baseDetailVO.getPriceResultCode();
        "0" == baseDetailVO.getDetailSnapShotid();
        "0" == baseDetailVO.getXproductInfo();
        "0" == baseDetailVO.getSpContractInfo();
        "0" == baseDetailVO.getDspStrategyStr();
        0L == baseDetailVO.getOrderCarTypeId();
        0L == baseDetailVO.getDrvId();
        "0" == baseDetailVO.getDrvName();
        "0" == baseDetailVO.getDrvPhone();
        "0" == baseDetailVO.getDrvPhoneAreaCode();
        0 == baseDetailVO.getDrvCoopMode();
        "0" == baseDetailVO.getDrvLanguageCodeList();
        0L == baseDetailVO.getCarId();
        0L == baseDetailVO.getCarTypeId();
        "0" == baseDetailVO.getCarLicense();
        0L == baseDetailVO.getCarColorId();
        0L == baseDetailVO.getCarBrandId();
        0L == baseDetailVO.getCarSeriesId();
        0L == baseDetailVO.getTransportGroupId();
        0 == baseDetailVO.getTransportGroupMode();
        "0" == baseDetailVO.getTransportGroupName();
        "0" == baseDetailVO.getConfirmMoneyTimeLocal();
        "0" == baseDetailVO.getConfirmMoneyTimeBj();
        "0" == baseDetailVO.getFinishTimeBj();
        "0" == baseDetailVO.getCancelTimeLocal();
        "0" == baseDetailVO.getCancelTimeBj();
        0 == baseDetailVO.getBizAreaType();
        0 == baseDetailVO.getAdultCount();
        0 == baseDetailVO.getChildCount();
        0 == baseDetailVO.getBagCount();
        0 == baseDetailVO.getMaxBagCount();
        "0" == baseDetailVO.getCoordType();
        0.0D == baseDetailVO.getFromLongitude();
        0.0D == baseDetailVO.getFromLatitude();
        0.0D == baseDetailVO.getToLongitude();
        0.0D == baseDetailVO.getToLatitude();
        "0" == baseDetailVO.getTakenTimeBj();
        "0" == baseDetailVO.getTargetId();
        "0" == baseDetailVO.getTerminalId();
        0 == baseDetailVO.getFixedLocationType();
        "0" == baseDetailVO.getFixedPosition();
        0 == baseDetailVO.getPremiumOrderFlag();
        day == false
        jnt == false
        isInServiced==false
        isAirportPickup==false
        isSelf == false
        isAllowUseRightSysBookTime==false
        isDriverAppDispatcherTakenType==false

    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme