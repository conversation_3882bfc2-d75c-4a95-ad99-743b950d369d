package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.DriverLocalTimeIntervalFeature
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TimeIntervalVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class DriverLocalTimeIntervalFeatureSortTest extends Specification {
    @Mock
    SortModel model
    @Mock
    SortContext context
    @Mock
    DriverVO driver
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrderVO
    @Mock
    DriverMileageProfitVO dmp
    @Mock
    CarVO car
    @Mock
    TimeIntervalVO timeIntervalVO
    @Mock
    DriverRelateOrderVO relateOrder
    @Mock
    EstimatedUseTimeVO estimatedUseTimeVO
    @Mock
    PredictServiceStopTimeVO predictServiceStopTimeVO
    @Mock
    PeriodsVO periodsVO

    @InjectMocks
    DriverLocalTimeIntervalFeature feature = new DriverLocalTimeIntervalFeature()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(model.getModel()).thenReturn(dspModelVO)
        Mockito.when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getDspOrder()).thenReturn(dspOrderVO)
        Mockito.when(context.getExpectDriverMileageProfit(Mockito.any())).thenReturn(dmp)
        Mockito.when(dspModelVO.getDriver()).thenReturn(driver)
        Mockito.when(driver.getDriverId()).thenReturn(1L)
        Mockito.when(driver.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDriverRelateOrder(Mockito.any())).thenReturn(relateOrder)

    }

    @Unroll
    def "test value"() {
        given:
        Mockito.when(dspOrderVO.estimatedUseTime()).thenReturn(estimatedUseTimeVO)
        Mockito.when(dspOrderVO.predictServiceStopTime()).thenReturn(predictServiceStopTimeVO)
        Mockito.when(driver.getWorkTimes()).thenReturn(periodsVO)


        when:
        Value res = feature.value(model, context)

        then:
        res.value == 0.0


    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme