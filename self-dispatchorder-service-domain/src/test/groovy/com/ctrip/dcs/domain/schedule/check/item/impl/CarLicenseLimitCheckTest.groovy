package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class CarLicenseLimitCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    Logger logger
    @InjectMocks
    CarLicenseLimitCheck carLicenseLimitCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "司机没有绑定车辆"() {
        given:
        Mockito.when(driverInfo.getCar()).thenReturn(null)

        when:
        CheckCode result = carLicenseLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机城市无限号"() {
        given:
        Mockito.when(car.getCarLicense()).thenReturn("京A88888")
        Mockito.when(context.carLicenseLimit(Mockito.any())).thenReturn(false)

        when:
        CheckCode result = carLicenseLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机城市限号"() {
        given:
        Mockito.when(car.getCarLicense()).thenReturn("京A88888")
        Mockito.when(context.carLicenseLimit(Mockito.any())).thenReturn(true)

        when:
        CheckCode result = carLicenseLimitCheck.check(checkModel, context)

        then:
        result == CheckCode.TRANSPORT_LIMIT_STATUS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme