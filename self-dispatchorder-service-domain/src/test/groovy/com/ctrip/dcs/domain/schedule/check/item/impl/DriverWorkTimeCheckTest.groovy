package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.util.DateUtil
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DriverWorkTimeCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    Logger logger
    @InjectMocks
    DriverWorkTimeCheck driverWorkTimeCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(context.getDspOrder()).thenReturn(order)
    }

    def "预估用车时间在司机工作时段"() {
        given:
        Mockito.when(order.getEstimatedUseTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-15 12:00:00"))
        Mockito.when(order.estimatedUseTime()).thenReturn(new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2023-05-15 12:00:00")))
        Mockito.when(driverInfo.getWorkTimes()).thenReturn(new PeriodsVO(["09:00~18:00"]))

        when:
        CheckCode result = driverWorkTimeCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "预估用车时间不在司机工作时段"() {
        given:
        Mockito.when(order.getEstimatedUseTime()).thenReturn(DateUtil.parseDateStr2Date("2023-05-15 12:00:00"))
        Mockito.when(order.estimatedUseTime()).thenReturn(new EstimatedUseTimeVO(DateUtil.parseDateStr2Date("2023-05-15 12:00:00")))
        Mockito.when(driverInfo.getWorkTimes()).thenReturn(new PeriodsVO(["09:00~10:00"]))

        when:
        CheckCode result = driverWorkTimeCheck.check(checkModel, context)

        then:
        result == CheckCode.WORK_TIME_LIMIT
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme