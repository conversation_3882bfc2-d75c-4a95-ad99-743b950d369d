package com.ctrip.dcs.domain.schedule.process.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.enums.ErrorCode
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.exception.OrderStatusException
import com.ctrip.dcs.domain.common.service.ConfirmDspOrderService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.service.QueryDspOrderService
import com.ctrip.dcs.domain.common.value.*
import com.ctrip.dcs.domain.dsporder.value.TakenType
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand
import com.ctrip.dcs.domain.schedule.check.command.TakenCheckCommand
import com.ctrip.dcs.domain.schedule.entity.ScheduleTaskDO
import com.ctrip.dcs.domain.schedule.factory.DriverOrderFactory
import com.ctrip.dcs.domain.schedule.gateway.SelfTransportInventoryGateway
import com.ctrip.dcs.domain.schedule.repository.ScheduleTaskRepository
import com.ctrip.dcs.domain.schedule.service.CheckService
import com.ctrip.dcs.domain.schedule.service.RecommendService
import com.ctrip.dcs.domain.schedule.sort.SortModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyBoolean
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class SystemAssignTransportProcessTest extends Specification {
    @Mock
    Logger logger
    @Mock
    ScheduleTaskRepository taskRepository
    @Mock
    RecommendService recommendService
    @Mock
    CheckService checkService
    @Mock
    ConfirmDspOrderService confirmDspOrderService
    @Mock
    MessageProviderService messageProducer
    @Mock
    DriverOrderFactory driverOrderFactory
    @InjectMocks
    SystemAssignTransportProcess systemAssignTransportProcess
    @Mock
    ScheduleTaskDO scheduleTaskDO
    @Mock
    DspOrderVO dspOrderVO;
    @Mock
    SubSkuVO subSkuVO
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModelVO
    @Mock
    DriverVO driverVO;
    @Mock
    TransportGroupVO transportGroupVO;
    @Mock
    SupplierVO supplierVO
    @Mock
    QueryDspOrderService queryDspOrderService;
    @Mock
     SelfTransportInventoryGateway selfTransportInventoryGateway;

    def setup() {
        MockitoAnnotations.initMocks(this)
        when(transportGroupVO.getTransportGroupMode()).thenReturn(TransportGroupMode.MANUAL_DISPATCH)
        when(transportGroupVO.getSupplierId()).thenReturn(2L)
        when(dspModelVO.getTransportGroup()).thenReturn(transportGroupVO)
        when(driverVO.getTransportGroups()).thenReturn([transportGroupVO])
        when(driverVO.getSupplier()).thenReturn(supplierVO)
        when(subSkuVO.getSubSkuId()).thenReturn(1)
        when(subSkuVO.getTakenType()).thenReturn(TakenType.ASSISTANT)
        when(subSkuVO.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        when(scheduleTaskDO.getTaskId()).thenReturn(1L)
        when(scheduleTaskDO.getScheduleId()).thenReturn(1L)
        when(scheduleTaskDO.getDspOrderId()).thenReturn("1")
        when(scheduleTaskDO.getRound()).thenReturn(1)
        when(checkModel.isPass()).thenReturn(true)
        when(checkModel.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(scheduleTaskDO.getSubSku()).thenReturn(subSkuVO)
        when(dspModelVO.getOrder()).thenReturn(dspOrderVO)
        when(checkService.check(any() as DspCheckCommand)).thenReturn([checkModel])
        when(recommendService.recommend(any(), any(), any())).thenReturn([new SortModel(dspModelVO)])
        when(queryDspOrderService.queryOrderDetail(anyString())).thenReturn(dspOrderVO)
        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.FROM_AIRPORT)
        when(dspOrderVO.getSkuId()).thenReturn(2)
        when(dspOrderVO.getUseDays()).thenReturn(new UseDays(BigDecimal.TEN))
        when(selfTransportInventoryGateway.useRatePlanInventory(any(), any(), any(),any(), any(), any(),any(), any())).thenReturn(null)

    }
    def "test execute_xiao"() {
        given:
        when:
        def result = systemAssignTransportProcess.execute(scheduleTaskDO, dspOrderVO)
        then:
        result == null
    }

    def "test execute_xiao1"() {
        given:
        when:
        def result = systemAssignTransportProcess.execute(scheduleTaskDO, dspOrderVO)
        then:
        result == null
    }


    def "test execute"() {
        given:
        def res = 1;
        when(checkService.check(any() as DspCheckCommand)).thenReturn([])

        when:
        systemAssignTransportProcess.execute(scheduleTaskDO, dspOrderVO)
        res++
        then:
        res  > 1
    }

    def "test execute 1"() {
        given:
        def res = 1;

        when:
        systemAssignTransportProcess.execute(scheduleTaskDO, dspOrderVO)
        res++

        then:
        res > 1
    }

    def "test execute 2"() {
        given:
        when(confirmDspOrderService.confirm(any() as DispatcherConfirmVO)).thenThrow(new OrderStatusException(ErrorCode.ORDER_STATUS_CHANGE_ILLEGAL_ERROR))
        def res = 1;

        when:
        systemAssignTransportProcess.execute(scheduleTaskDO, dspOrderVO)
        res++

        then:
        res > 1
    }

    def "test execute 3"() {
        given:
        int i = 0
        when(recommendService.recommend(any(), any(), any())).thenReturn([])
        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.DAY_RENTAL)
        when(dspOrderVO.getRushOrder()).thenReturn(i)

        when:
        systemAssignTransportProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        i>=0 == true
    }

    def "test execute 4"() {
        given:
        int i = 1
        when(recommendService.recommend(any(), any(), any())).thenReturn([])
        when(dspOrderVO.getCategoryCode()).thenReturn(CategoryCodeEnum.DAY_RENTAL)
        when(dspOrderVO.getRushOrder()).thenReturn(i)

        when:
        systemAssignTransportProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        i>=0 == true
    }

    def "test execute 5"() {
        given:
        int i = 1
        when(dspOrderVO.getRatePlanId()).thenReturn(i)

        when:
        systemAssignTransportProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        i>=0 == true
    }

    def "test execute 6"() {
        given:
        int i = 1
        when(dspOrderVO.getRatePlanId()).thenReturn(i)
        when(selfTransportInventoryGateway.useRatePlanInventory(any(), any(), any(),any(), any(), any(),any(), any())).thenReturn(2L)

        when:
        systemAssignTransportProcess.execute(scheduleTaskDO, dspOrderVO)

        then:
        i>=0 == true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme