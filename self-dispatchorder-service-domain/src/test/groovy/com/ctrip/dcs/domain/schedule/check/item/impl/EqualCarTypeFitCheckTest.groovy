package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CarTypeLevelRelation
import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.exception.CarTypeRelationsNotFoundException
import com.ctrip.dcs.domain.schedule.exception.CheckItemException
import com.ctrip.dcs.domain.schedule.value.CarTypeLevelRelationsVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class EqualCarTypeFitCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    CarTypeLevelRelationsVO carTypeLevelRelations
    @InjectMocks
    EqualCarTypeFitCheck equalCarTypeFitCheck = new EqualCarTypeFitCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(118L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
        Mockito.when(order.getCarTypeId()).thenReturn(118)
    }

    def "订单车型与司机车型是平派关系的"() {
        given:
        Map<Long, CarTypeLevelRelation> otherCarTypeRelationMap = Maps.newHashMap()
        otherCarTypeRelationMap.put(117L, CarTypeLevelRelation.BELOW)
        otherCarTypeRelationMap.put(118L, CarTypeLevelRelation.PARALLEL)
        otherCarTypeRelationMap.put(121L, CarTypeLevelRelation.HIGHER)
        CarTypeLevelRelationsVO carTypeLevelRelationsVO = new CarTypeLevelRelationsVO(otherCarTypeRelationMap)
        Mockito.when(context.carTypeRelations(Mockito.any())).thenReturn(carTypeLevelRelationsVO)
        Mockito.when(car.getCarTypeId()).thenReturn(118L)
        Mockito.when(order.getCarTypeId()).thenReturn(118)

        when:
        CheckCode result = equalCarTypeFitCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "订单车型与司机车型！=平派关系的"() {
        given:
        Map<Long, CarTypeLevelRelation> otherCarTypeRelationMap = Maps.newHashMap()
        otherCarTypeRelationMap.put(117L, CarTypeLevelRelation.BELOW)
        otherCarTypeRelationMap.put(118L, CarTypeLevelRelation.PARALLEL)
        otherCarTypeRelationMap.put(121L, CarTypeLevelRelation.HIGHER)
        CarTypeLevelRelationsVO carTypeLevelRelationsVO = new CarTypeLevelRelationsVO(otherCarTypeRelationMap)
        Mockito.when(context.carTypeRelations(Mockito.any())).thenReturn(carTypeLevelRelationsVO)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(order.getCarTypeId()).thenReturn(118)

        when:
        CheckCode result = equalCarTypeFitCheck.check(checkModel, context)

        then:
        result == CheckCode.DRV_CAR_TYPE_NOT_MATCH
    }

    @Unroll
    def "test check"() {
        given:
        CheckModel checkModel = new CheckModel(model: new DspModelVO(order: new DspOrderVO(carTypeId: 1), driver: new DriverVO(car: new CarVO(carTypeId: 2L))))
        CheckContext context = Mock()
        context.carTypeRelations(_) >> carTypeLevelRelations
        when:
        CheckCode result = equalCarTypeFitCheck.check(checkModel, context)
        then:
        result == r

        where:
        carTypeLevelRelations                                                   || r
        new CarTypeLevelRelationsVO(null)                                       || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        new CarTypeLevelRelationsVO([3L: CarTypeLevelRelation.getInstance(-1)]) || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        new CarTypeLevelRelationsVO([2L: CarTypeLevelRelation.getInstance(1)])  || CheckCode.DRV_CAR_TYPE_NOT_MATCH
        new CarTypeLevelRelationsVO([2L: CarTypeLevelRelation.getInstance(0)])  || CheckCode.PASS
        new CarTypeLevelRelationsVO([2L: CarTypeLevelRelation.getInstance(-1)]) || CheckCode.DRV_CAR_TYPE_NOT_MATCH
    }

    def "test downgrade"() {
        when:
        CheckCode result = equalCarTypeFitCheck.downgrade()

        then:
        result == CheckCode.DRV_CAR_TYPE_NOT_MATCH
    }

    @Unroll
    def "test check2"() {
        given:
        CheckContext context = Mock()
        context.carTypeRelations(_) >> carTypeRelations
        when:
        equalCarTypeFitCheck.check(checkModel, context)
        then:
        thrown(e)

        where:
        checkModel                                                                                                         | carTypeRelations                  || e
        new CheckModel()                                                                                                   | null                              || CarTypeRelationsNotFoundException
        new CheckModel(model: new DspModelVO())                                                                            | null                              || CarTypeRelationsNotFoundException
        new CheckModel(model: new DspModelVO(order: new DspOrderVO()))                                                     | null                              || CarTypeRelationsNotFoundException
        new CheckModel(model: new DspModelVO(order: new DspOrderVO(carTypeId: 1)))                                         | null                              || CarTypeRelationsNotFoundException
        new CheckModel(model: new DspModelVO(order: new DspOrderVO(carTypeId: 1)))                                         | new CarTypeLevelRelationsVO(null) || CheckItemException
        new CheckModel(model: new DspModelVO(order: new DspOrderVO(carTypeId: 1), driver: new DriverVO()))                 | new CarTypeLevelRelationsVO(null) || CheckItemException
        new CheckModel(model: new DspModelVO(order: new DspOrderVO(carTypeId: 1), driver: new DriverVO(car: new CarVO()))) | new CarTypeLevelRelationsVO(null) || CheckItemException
        new CheckModel(model: new DspModelVO(order: new DspOrderVO(carTypeId: 1), driver: new DriverVO(car: new CarVO()))) | new CarTypeLevelRelationsVO(null) || CheckItemException
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme