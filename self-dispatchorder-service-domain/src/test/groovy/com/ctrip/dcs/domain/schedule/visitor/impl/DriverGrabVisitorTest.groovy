package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.DuidVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.entity.GrabOrderDO
import com.ctrip.dcs.domain.schedule.repository.SelectGrabOrderRepository
import com.ctrip.dcs.domain.schedule.sort.SortContext
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.google.common.collect.Maps
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DriverGrabVisitorTest extends Specification {
    @Mock
    SelectGrabOrderRepository selectGrabOrderRepository
    @Mock
    GrabOrderDO grabOrderDO
    @Mock
    SortContext sortContext
    @Mock
    DuidVO duidVO
    @Mock
    SubSkuVO subSkuVO
    @Mock
    DspOrderVO dspOrder
    @InjectMocks
    DriverGrabVisitor driverGrabVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        Map<Long, GrabOrderDO> map = Maps.newHashMap()
        when(sortContext.getDriverGrabOrderMap()).thenReturn(map)
        when(sortContext.getDuid()).thenReturn(duidVO)
        when(sortContext.getDspOrder()).thenReturn(dspOrder)
        when(sortContext.getSubSku()).thenReturn(subSkuVO)
        when(duidVO.getDspOrderId()).thenReturn("1")
        when(duidVO.getSubSkuId()).thenReturn(1)
        when(duidVO.getDspType()).thenReturn(dspType)
        when(subSkuVO.getDspType()).thenReturn(DspType.of(dspType))
        when(grabOrderDO.getDriverId()).thenReturn(1L)
        when(selectGrabOrderRepository.find(any(), any())).thenReturn([grabOrderDO])

        when:
        driverGrabVisitor.visit(sortContext)

        then:
        map.size() == size

        where:
        dspType                          || size
        DspType.SYSTEM_ASSIGN.getCode()  || 0
        DspType.VBK_BROADCAST.getCode()  || 1
        DspType.VBK_GRAB_ORDER.getCode() || 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme