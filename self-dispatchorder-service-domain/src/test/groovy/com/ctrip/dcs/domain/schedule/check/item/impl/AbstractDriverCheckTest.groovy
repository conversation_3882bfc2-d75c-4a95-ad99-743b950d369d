package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.exception.CheckItemValidateException
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import org.mockito.InjectMocks
import spock.lang.Specification
import spock.lang.Unroll

class AbstractDriverCheckTest extends Specification {
    @InjectMocks
    AbstractDriverCheck abstractDriverCheck = new AbstractDriverCheck() {
        @Override
        protected CheckCode check(CheckModel checkModel, CheckContext context) {
            return null
        }

        @Override
        protected CheckCode downgrade() {
            return null
        }
    }

    @Unroll
    def "test validate"() {
        given:
        CheckContext context = Mock()

        when:
        abstractDriverCheck.validate(checkModel, context)

        then:
        thrown(CheckItemValidateException)

        where:
        checkModel                              | _
        null                                    | _
        new CheckModel()                        | _
        new CheckModel(model: new DspModelVO()) | _
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme