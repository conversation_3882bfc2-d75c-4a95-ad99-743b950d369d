package com.ctrip.dcs.domain.dsporder.value

import com.ctrip.dcs.domain.common.value.DriverWorkTimeVO
import spock.lang.Specification

class DriverWorkTimeVOTest extends Specification {

    DriverWorkTimeVO driverWorkTimeVO = new DriverWorkTimeVO()
    def "test contain1"() {
        when:
        driverWorkTimeVO.setStart(com.ctrip.dcs.domain.common.util.DateUtil.addHours(new Date(), -2))
        driverWorkTimeVO.setEnd(com.ctrip.dcs.domain.common.util.DateUtil.addHours(new Date(), 2))

        boolean result = driverWorkTimeVO.contain(new Date(), true)

        then:
        result == true
    }


    def "test contain2"() {
        when:
        driverWorkTimeVO.setStart(com.ctrip.dcs.domain.common.util.DateUtil.addHours(new Date(), 4))
        driverWorkTimeVO.setEnd(com.ctrip.dcs.domain.common.util.DateUtil.addHours(new Date(), 5))

        boolean result = driverWorkTimeVO.contain(new Date(), true)

        then:
        result != true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme