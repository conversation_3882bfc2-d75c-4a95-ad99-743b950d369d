package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.gateway.OrderDisLimitServiceGateway
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.OrderDisLimitVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class OrderDistanceLimitVisitorTest extends Specification {
    @Mock
    DriverVO driverVO
    @Mock
    SubSkuVO subSku
    @Mock
    CheckModel model
    @Mock
    DspModelVO dspModelVO
    @Mock
    DspOrderVO dspOrder
    @Mock
    CheckContext checkContext
    //Field dspStage of type DspStage - was not mocked since Mockito doesn't mock enums
    @Mock
    OrderDisLimitServiceGateway orderDisLimitServiceGateway
    @InjectMocks
    OrderDistanceLimitVisitor orderDistanceLimitVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(model.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        when(orderDisLimitServiceGateway.queryOrderDisLimitMap(any(), any(), any(), any(), any(), any())).thenReturn([(1l): new OrderDisLimitVO()])
        OrderDistanceLimitVisitor orderDistanceLimitVisitor = new OrderDistanceLimitVisitor(subSku, [model], dspOrder, DspStage.DSP, orderDisLimitServiceGateway)

        when:
        orderDistanceLimitVisitor.visit(checkContext)

        then:
        checkContext.getOrderDisLimitMap() != null
    }

    def "test visit 2"() {
        given:
        when(checkContext.getOrderDisLimitMap()).thenReturn([1L: new OrderDisLimitVO()])
        when(model.getModel()).thenReturn(dspModelVO)
        when(dspModelVO.getDriver()).thenReturn(driverVO)
        when(driverVO.getDriverId()).thenReturn(1L)
        when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        when(orderDisLimitServiceGateway.queryOrderDisLimitMap(any(), any(), any(), any(), any(), any())).thenReturn([(1l): new OrderDisLimitVO()])
        OrderDistanceLimitVisitor orderDistanceLimitVisitor = new OrderDistanceLimitVisitor(subSku, [model], dspOrder, DspStage.DSP, orderDisLimitServiceGateway)

        when:
        orderDistanceLimitVisitor.visit(checkContext)

        then:
        checkContext.getOrderDisLimitMap() != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
