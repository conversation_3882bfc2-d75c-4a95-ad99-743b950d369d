package com.ctrip.dcs.domain.dsporder.carconfig


import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import spock.lang.Specification

class ManualSubSkuConfigTest extends Specification {
    @InjectMocks
    ManualSubSkuConf manualSubSkuConf = new ManualSubSkuConf()

    def setup() {
        Map<String/*key.getKey*/, ManualSubSkuConf.StrategyValue/*value*/> configMap = new HashMap<>();

        ManualSubSkuConf.StrategyValue v1 = new ManualSubSkuConf.StrategyValue()
        ManualSubSkuConf.Key k1 = new ManualSubSkuConf.Key()
        ManualSubSkuConf.Value vv = new ManualSubSkuConf.Value()
        vv.setSubSkuId(11)

        k1.setCategoryCode("all")
        k1.setCityId(0)
        k1.setCountryId(0)
        k1.setSysRole("boss")
        k1.setTransportGroupMode(0)
        v1.setParams(k1)
        v1.setValues(Lists.newArrayList(vv))
        ManualSubSkuConf.StrategyValue v2 = new ManualSubSkuConf.StrategyValue()
        ManualSubSkuConf.Key k2 = new ManualSubSkuConf.Key()
        k2.setCategoryCode("all")
        k2.setCityId(0)
        k2.setCountryId(1)
        k2.setSysRole("supplier")
        k2.setTransportGroupMode(0)
        v2.setParams(k2)
        v2.setValues(Lists.newArrayList(vv))
        ManualSubSkuConf.StrategyValue v3 = new ManualSubSkuConf.StrategyValue()
        ManualSubSkuConf.Key k3 = new ManualSubSkuConf.Key()
        k3.setCategoryCode("all")
        k3.setCityId(0)
        k3.setCountryId(1)
        k3.setSysRole("supplier")
        k3.setTransportGroupMode(0)
        v3.setParams(k3)
        v3.setValues(Lists.newArrayList(vv))
        ManualSubSkuConf.StrategyValue v4 = new ManualSubSkuConf.StrategyValue()
        ManualSubSkuConf.Key k4 = new ManualSubSkuConf.Key()
        k4.setCategoryCode("all")
        k4.setCityId(0)
        k4.setCountryId(0)
        k4.setSysRole("supplier")
        k4.setTransportGroupMode(0)
        v4.setParams(k4)
        v4.setValues(Lists.newArrayList(vv))


        configMap.put("0#0#0#all#boss", v1);
        configMap.put("1#0#0#all#supplier", v2);
        configMap.put("1#0#0#all#boss", v3);
        configMap.put("0#0#0#all#supplier", v4);

        manualSubSkuConf.config = configMap
    }

    def "test "() {
        when:
        Integer result = manualSubSkuConf.matchSubSku(1, 1, "day_rental", 1101, "supplier")
        then:
        result == 11


    }

    def "test 1"() {
        when:
        Integer result = manualSubSkuConf.matchSubSku(1, 1, "day_rental", 1101, "boss")
        then:
        result == 11


    }

    def "dest 2"() {
        when:
        def result = manualSubSkuConf.queryManualSubSkuList()
        then:
        result.size() > 1
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme