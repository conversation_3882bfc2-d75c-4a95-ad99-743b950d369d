package com.ctrip.dcs.domain.common.value

import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2025/4/30 17:35
 */
class OrderVOTest extends Specification {
    def testObj = new OrderVO()
    def dspOrderVO = Mock(DspOrderVO)

    def setup() {
        testObj.dspOrderVO = dspOrderVO
    }

    @Unroll
    def "test"() {
        given:
        when:
        OrderVO orderVO = new OrderVO();
        orderVO.setDrvUdl("drvUdl");
        then:
        Assert.assertTrue(Objects.equals(orderVO.getDrvUdl(), "drvUdl"))
    }


}
