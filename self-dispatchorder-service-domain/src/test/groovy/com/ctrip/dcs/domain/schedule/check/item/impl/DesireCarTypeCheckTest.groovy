package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

class DesireCarTypeCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo

    @InjectMocks
    DesireCarTypeCheck desireCarTypeCheck = new DesireCarTypeCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
    }

    def "司机意愿车型 包含 订单车型"() {
        given:
        Mockito.when(car.getCarTypeId()).thenReturn(118L)
        Mockito.when(order.getCarTypeId()).thenReturn(117)
        Mockito.when(driverInfo.getIntendVehicleTypeId()).thenReturn("117,118")

        when:
        CheckCode result = desireCarTypeCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "司机意愿车型 不包含 订单车型"() {
        given:
        Mockito.when(car.getCarTypeId()).thenReturn(118L)
        Mockito.when(order.getCarTypeId()).thenReturn(117)
        Mockito.when(driverInfo.getIntendVehicleTypeId()).thenReturn("118")

        when:
        CheckCode result = desireCarTypeCheck.check(checkModel, context)

        then:
        result == CheckCode.DRV_DESIRE_LIMIT
    }

    @Unroll
    def "test check"() {
        given:
        DspOrderVO order = new DspOrderVO(
                carTypeId: 1
        )
        DriverVO driver = new DriverVO(
                car: car,
                intendVehicleTypeId: intendVehicleTypeId
        )
        DspModelVO model = new DspModelVO(
                order: order,
                driver: driver
        )
        CheckModel checkModel = new CheckModel(
                model: model
        )
        CheckContext context = Mock()
        when:
        CheckCode result = desireCarTypeCheck.check(checkModel, context)
        then:
        result == r

        where:
        car                      | intendVehicleTypeId || r
        null                     | null                || CheckCode.DRV_DESIRE_LIMIT
        new CarVO()              | null                || CheckCode.DRV_DESIRE_LIMIT
        new CarVO(carTypeId: 1L) | null                || CheckCode.PASS
        new CarVO(carTypeId: 2L) | null                || CheckCode.DRV_DESIRE_LIMIT
        new CarVO(carTypeId: 2L) | ""                  || CheckCode.DRV_DESIRE_LIMIT
        new CarVO(carTypeId: 2L) | ","                 || CheckCode.DRV_DESIRE_LIMIT
        new CarVO(carTypeId: 2L) | "2,"                || CheckCode.DRV_DESIRE_LIMIT
        new CarVO(carTypeId: 2L) | "1,2"               || CheckCode.PASS
    }

    def "test downgrade"() {
        when:
        CheckCode result = desireCarTypeCheck.downgrade()

        then:
        result == CheckCode.DRV_DESIRE_LIMIT
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme