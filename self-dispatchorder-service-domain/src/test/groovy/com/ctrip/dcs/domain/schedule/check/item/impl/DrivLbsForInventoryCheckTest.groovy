package com.ctrip.dcs.domain.schedule.check.item.impl


import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DrvInventoryCheckResultVO
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Maps
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class DrivLbsForInventoryCheckTest extends Specification {
    @Mock
    Logger logger
    @InjectMocks
    DrivLbsForInventoryCheck drivLbsForInventoryCheck = new DrivLbsForInventoryCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test check"() {
        given:
        def context = Mock(CheckContext)
        context.getDrvInventoryCheckResModelMap() >> resMap
        def checkModel = new CheckModel(model: new DspModelVO(new DspOrderVO(), new DriverVO(driverId: driverId)))

        when:
        CheckCode result = drivLbsForInventoryCheck.check(checkModel, context)
        then:
        result == checkRes

        where:
        resMap   | driverId || checkRes
        getMap() | 1L       || CheckCode.PASS
        getMap() | 8l       || CheckCode.BOOK_TIME_CONFLICT_STATUS
        getMap() | 3L       || CheckCode.BOOK_TIME_CONFLICT_STATUS
        null     | 1L       || CheckCode.BOOK_TIME_CONFLICT_STATUS

    }

    def getMap() {
        Map<Long, DrvInventoryCheckResultVO> map = Maps.newHashMap();
        map.put(1L, new DrvInventoryCheckResultVO(1L, "1", ""));
        map.put(2L, new DrvInventoryCheckResultVO(2L, "2", ""));
        map.put(8l, new DrvInventoryCheckResultVO(2L, "8", ""));
        return map
    }

    def "test downgrade"() {
        when:
        CheckCode result = drivLbsForInventoryCheck.downgrade()

        then:
        result == CheckCode.BOOK_TIME_CONFLICT_STATUS;
    }
    
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme