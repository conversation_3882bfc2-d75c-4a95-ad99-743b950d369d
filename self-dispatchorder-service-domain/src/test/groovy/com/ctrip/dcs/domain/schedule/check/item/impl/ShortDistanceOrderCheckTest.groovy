package com.ctrip.dcs.domain.schedule.check.item.impl


import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class ShortDistanceOrderCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    TransportGroupVO transportGroup
    @Mock
    Logger logger
    @InjectMocks
    ShortDistanceOrderCheck shortDistanceOrderCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getCar()).thenReturn(car)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getShortDisOrder()).thenReturn(1)
    }

    def "check1"() {
        given:

        when:
        CheckCode result = shortDistanceOrderCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "check2"() {
        given:
        Mockito.when(order.getShortDisOrder()).thenReturn(0)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(null)


        when:
        CheckCode result = shortDistanceOrderCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "check3"() {
        given:
        Mockito.when(order.getShortDisOrder()).thenReturn(0)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getShortDisSwitch()).thenReturn(1)


        when:
        CheckCode result = shortDistanceOrderCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "check4"() {
        given:
        Mockito.when(order.getShortDisOrder()).thenReturn(0)
        Mockito.when(dspModel.getTransportGroup()).thenReturn(transportGroup)
        Mockito.when(transportGroup.getShortDisSwitch()).thenReturn(0)


        when:
        CheckCode result = shortDistanceOrderCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme