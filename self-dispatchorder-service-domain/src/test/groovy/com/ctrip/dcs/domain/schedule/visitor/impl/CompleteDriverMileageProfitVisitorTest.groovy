package com.ctrip.dcs.domain.schedule.visitor.impl

import com.ctrip.dcs.domain.common.service.QueryDriverMileageProfitService
import com.ctrip.dcs.domain.common.value.DriverMileageProfitVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.context.DspContext
import com.ctrip.dcs.domain.schedule.sort.SortContext
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class CompleteDriverMileageProfitVisitorTest extends Specification {
    @Mock
    CheckContext checkContext
    @Mock
    SortContext sortContext
    @Mock
    DspContext dspContext
    @Mock
    DspOrderVO order
    @Mock
    DriverVO driverVO
    @Mock
    QueryDriverMileageProfitService queryDriverMileageProfitService
    @InjectMocks
    CompleteDriverMileageProfitVisitor completeDriverMileageProfitVisitor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test visit"() {
        given:
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 17, 16).getTime())
        when(queryDriverMileageProfitService.queryCompleteDriverMileageProfit(any(), any())).thenReturn([new DriverMileageProfitVO(1l, 1, 1, 1, 1D, 1D, 1D, 1D)])
        CompleteDriverMileageProfitVisitor completeDriverMileageProfitVisitor = new CompleteDriverMileageProfitVisitor(order, [driverVO], queryDriverMileageProfitService)

        when:
        completeDriverMileageProfitVisitor.visit(sortContext)

        then:
        true//todo - validate something
    }

    def "test visit 2"() {
        given:
        when(sortContext.getCompleteDriverMileageProfitMap()).thenReturn([1L: new DriverMileageProfitVO(1l, 1, 1, 1, 1D, 1D, 1D, 1D)])
        when(driverVO.getDriverId()).thenReturn(1L)
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 17, 16).getTime())
        when(queryDriverMileageProfitService.queryCompleteDriverMileageProfit(any(), any())).thenReturn([new DriverMileageProfitVO(1l, 1, 1, 1, 1D, 1D, 1D, 1D)])
        CompleteDriverMileageProfitVisitor completeDriverMileageProfitVisitor = new CompleteDriverMileageProfitVisitor(order, [driverVO], queryDriverMileageProfitService)

        when:
        completeDriverMileageProfitVisitor.visit(sortContext)

        then:
        true//todo - validate something
    }

    def "test visit 3"() {
        given:
        when(sortContext.getCompleteDriverMileageProfitMap()).thenReturn([1L: new DriverMileageProfitVO(1l, 1, 1, 1, 1D, 1D, 1D, 1D)])
        when(driverVO.getDriverId()).thenReturn(2L)
        when(order.getEstimatedUseTime()).thenReturn(new GregorianCalendar(2023, Calendar.JUNE, 14, 17, 16).getTime())
        when(queryDriverMileageProfitService.queryCompleteDriverMileageProfit(any(), any())).thenReturn([])
        CompleteDriverMileageProfitVisitor completeDriverMileageProfitVisitor = new CompleteDriverMileageProfitVisitor(order, [driverVO], queryDriverMileageProfitService)

        when:
        completeDriverMileageProfitVisitor.visit(sortContext)

        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme