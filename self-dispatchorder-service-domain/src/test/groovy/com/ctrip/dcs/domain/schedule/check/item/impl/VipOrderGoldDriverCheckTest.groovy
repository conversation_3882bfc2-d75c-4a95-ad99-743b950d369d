package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderExtendAttributeVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import spock.lang.Specification
import spock.lang.Unroll

class VipOrderGoldDriverCheckTest extends Specification {
    def testObj = new VipOrderGoldDriverCheck()

//    @Unroll
//    def "loadTest"() {
//        given: "设定相关方法入参"
//        when:
//        def result = testObj.load(models, context)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        models | context            || expectedResult
//        [null] | new CheckContext() || null
//    }

    @Unroll
    def "checkTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.check(checkModel, context)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        context                                                  | checkModel                                                                                                                                                                                   || expectedResult
        new CheckContext(highDriverMap: [(1234): Boolean.TRUE])  | new CheckModel(model: new DspModelVO(order: new DspOrderVO(orderExtendAttributeInfo: [new OrderExtendAttributeVO("is_vip_user_level", 1 as String)]), driver: new DriverVO(driverId: 1234))) || CheckCode.PASS
        new CheckContext(highDriverMap: [(1234): Boolean.FALSE]) | new CheckModel(model: new DspModelVO(order: new DspOrderVO(orderExtendAttributeInfo: [new OrderExtendAttributeVO("is_vip_user_level", 1 as String)]), driver: new DriverVO(driverId: 1234))) || CheckCode.HIGH_LEVEL_ORDER_NOT_GOLD_DRIVER_LIMIT
        new CheckContext(highDriverMap: [(1234): Boolean.FALSE]) | new CheckModel(model: new DspModelVO(order: new DspOrderVO(orderExtendAttributeInfo: [new OrderExtendAttributeVO("is_vip_user_level", 0 as String)]), driver: new DriverVO(driverId: 1234))) || CheckCode.PASS
        new CheckContext()                                       | new CheckModel(model: new DspModelVO(order: new DspOrderVO(orderExtendAttributeInfo: [new OrderExtendAttributeVO("is_vip_user_level", 0 as String)]), driver: new DriverVO(driverId: 1234))) || CheckCode.PASS
    }

    @Unroll
    def "downgradeTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.downgrade()

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << CheckCode.PASS
    }

//    @Unroll
//    def "checkTest"() {
//        given: "设定相关方法入参"
//        and: "Mock相关接口返回"
//
//        and: "Spy相关接口"
//        def spy = Spy(testObj)
//        spy.validate(_, _) >> {}
//        spy.check(_, _) >> CheckCode.PASS
//        spy.downgrade() >> CheckCode.PASS
//        spy.load(_, _) >> {}
//        spy.batchPost(_, _) >> {}
//        spy.post(_, _, _) >> {}
//        when:
//        def result = spy.check(models, context)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        models | context            || expectedResult
//        [null] | new CheckContext() || [null]
//    }
}
