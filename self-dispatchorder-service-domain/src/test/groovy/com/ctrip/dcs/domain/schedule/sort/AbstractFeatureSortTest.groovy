package com.ctrip.dcs.domain.schedule.sort

import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.EmptyDrivingVO
import com.ctrip.dcs.domain.schedule.sort.feature.Feature
import com.ctrip.dcs.domain.schedule.sort.feature.FeatureItemId
import com.ctrip.dcs.domain.schedule.sort.feature.Normalizer
import com.ctrip.dcs.domain.schedule.sort.feature.Value
import com.ctrip.dcs.domain.schedule.sort.feature.impl.AbstractFeature
import org.assertj.core.util.Lists
import org.mockito.InjectMocks
import spock.lang.Specification
import spock.lang.Unroll

class AbstractFeatureSortTest extends Specification {


    @InjectMocks
    AbstractFeature abstractFeature = new AbstractFeature() {

        @Override
        protected Value value(SortModel model, SortContext context) {
            return new Value(FeatureItemId.F15.name(), 1.1)
        }

        @Override
        protected Normalizer normalizer() {
            return Normalizer.NORMALIZE
        }
    }

    @Unroll
    def "test value"() {
        given:
        SortContext context = Mock()
        def sm1 = Mock(SortModel)
        def sm2 = Mock(SortModel)


        when:
        List<Value> res = abstractFeature.value(Lists.newArrayList(sm1, sm2), context)

        then:
        res.size() > 0


    }

    @InjectMocks
    AbstractFeature abstractFeature1 = new AbstractFeature() {

        @Override
        protected Value value(SortModel model, SortContext context) {
            throw new Exception()
        }

        @Override
        protected Normalizer normalizer() {
            return Normalizer.NORMALIZE
        }


    }


    @Unroll
    def "test value1"() {
        given:
        SortContext context = Mock()
        def sm1 = Mock(SortModel)
        def sm2 = Mock(SortModel)


        when:
        List<Value> res = abstractFeature1.value(Lists.newArrayList(sm1, sm2), context)

        then:
        res.size() == 0


    }

    @InjectMocks
    AbstractFeature abstractFeature2 = new AbstractFeature() {

        @Override
        protected Value value(SortModel model, SortContext context) {
            throw new Exception()
        }

        @Override
        protected Normalizer normalizer() {
            return Normalizer.NORMALIZE
        }

        @Override
        protected void load(List<SortModel> models, SortContext context) {
            throw new Exception()
        }


    }


    @Unroll
    def "test value2"() {
        given:
        SortContext context = Mock()
        def sm1 = Mock(SortModel)
        def sm2 = Mock(SortModel)


        when:
        List<Value> res = abstractFeature2.value(Lists.newArrayList(sm1, sm2), context)

        then:
        res.size() == 0


    }

    @Unroll
    def "test getRelateOrderValueDetail"() {
        given:
        SortContext context = Mock()
        def o1 = Mock(DspOrderVO)
        def o2 = Mock(DspOrderVO)
        def e1 = Mock(EmptyDrivingVO)
        def e2 = Mock(EmptyDrivingVO)
        def e3 = Mock(EmptyDrivingVO)
        o1.getPredicServiceStopTime() >> new Date()
        o1.getEstimatedMin() >> BigDecimal.ONE

        when:
        Map<String,Object> res = AbstractFeature.getRelateOrderValueDetail(o1, o2, e1, e2, e3)

        then:
        res["f"] == 35.0


    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme