package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.CategoryCodeEnum
import com.ctrip.dcs.domain.common.enums.TransportGroupMode
import com.ctrip.dcs.domain.common.enums.YesOrNo
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.common.value.OrderWayPointVO
import com.ctrip.dcs.domain.common.value.PeriodsVO
import com.ctrip.dcs.domain.common.value.RenaissanceActivityVO
import com.ctrip.dcs.domain.common.value.SopRecord
import com.ctrip.dcs.domain.common.value.SupplierBorneVO
import com.ctrip.dcs.domain.common.value.SupplierVO
import com.ctrip.dcs.domain.common.value.TradeCouponVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.common.value.XproductVO
import com.ctrip.dcs.domain.dsporder.value.UseDays
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.TakeOrderLimitTimeMinuteVO
import com.ctrip.dcs.domain.schedule.value.TransportGroupOrderConfig
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ShuntSupplierCheckTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DspOrderVO dspOrder
    @Mock
    DriverVO driver
    @Mock
    TransportGroupVO transportGroup
    @InjectMocks
    ShuntSupplierCheck shuntSupplierCheck

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test check"() {
        given:
        when(dspOrder.getShuntFlag()).thenReturn(shuntFlag)
        when(dspOrder.getShuntSupplierId()).thenReturn(shuntSupplierId)
        when(transportGroup.getSupplierId()).thenReturn(supplierId)

        when:
        CheckCode result = shuntSupplierCheck.check(new CheckModel(new DspModelVO(order: dspOrder, driver: driver, transportGroup: transportGroup), CheckCode.PASS), new CheckContext())

        then:
        result == code

        where:
        shuntFlag | shuntSupplierId | supplierId || code
        null      | null            | null       || CheckCode.PASS
        0         | null            | null       || CheckCode.PASS
        1         | null            | null       || CheckCode.PASS
        1         | 1L              | null       || CheckCode.SHUNT_SUPPLIER_LIMIT
        1         | 1L              | 0L         || CheckCode.SHUNT_SUPPLIER_LIMIT
        1         | 1L              | 1L         || CheckCode.PASS
    }

    def "test downgrade"() {
        when:
        CheckCode result = shuntSupplierCheck.downgrade()

        then:
        result == CheckCode.SHUNT_SUPPLIER_LIMIT
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme