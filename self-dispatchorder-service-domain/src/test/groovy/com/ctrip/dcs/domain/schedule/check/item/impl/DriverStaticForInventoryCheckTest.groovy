package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.enums.DspStage
import com.ctrip.dcs.domain.common.enums.DspType
import com.ctrip.dcs.domain.common.value.CarVO
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.SubSkuVO
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class DriverStaticForInventoryCheckTest extends Specification {

    @Mock
    CheckContext context
    @Mock
    CheckModel checkModel
    @Mock
    DspModelVO dspModel
    @Mock
    DspOrderVO order
    @Mock
    SubSkuVO subSku
    @Mock
    CarVO car
    @Mock
    DriverVO driverInfo
    @Mock
    Logger logger
    @InjectMocks
    DriverStaticForInventoryCheck driverStaticForInventoryCheck = new DriverStaticForInventoryCheck()

    def setup() {
        MockitoAnnotations.initMocks(this)
        Mockito.when(checkModel.getModel()).thenReturn(dspModel)
        Mockito.when(dspModel.getOrder()).thenReturn(order)
        Mockito.when(dspModel.getDriver()).thenReturn(driverInfo)
        Mockito.when(driverInfo.getDriverId()).thenReturn(1L)
        Mockito.when(driverInfo.isRegisterDriver()).thenReturn(true)
        Mockito.when(subSku.getDspType()).thenReturn(DspType.SYSTEM_ASSIGN)
        Mockito.when(context.getSubSku()).thenReturn(subSku)
        Mockito.when(driverInfo.getCityId()).thenReturn(1L)
        Mockito.when(driverInfo.getCar()).thenReturn(car)
        Mockito.when(car.getCarTypeId()).thenReturn(117L)
        Mockito.when(context.getDspStage()).thenReturn(DspStage.DSP)
        Mockito.when(order.getDspOrderId()).thenReturn("1")
    }

    def "司机静态运力库存已经被占用"() {
        given:
        Mockito.when(context.getDriverStaticInventory()).thenReturn(Lists.newArrayList())

        when:
        CheckCode result = driverStaticForInventoryCheck.check(checkModel, context)

        then:
        result == CheckCode.BOOK_TIME_CONFLICT_STATUS
    }

    def "司机静态运力库存没有被占用"() {
        given:
        Mockito.when(context.getDriverStaticInventory()).thenReturn(Lists.newArrayList(1L))

        when:
        CheckCode result = driverStaticForInventoryCheck.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check"() {
        given:
        def context = Mock(CheckContext)
        context.getDriverStaticInventory() >> getInventory()
        def checkModel = new CheckModel(model: new DspModelVO(new DspOrderVO(), new DriverVO(driverId: driverId)))

        when:
        CheckCode result = driverStaticForInventoryCheck.check(checkModel, context)

        then:
        result == res

        where:
        driverId || res
        1L       || CheckCode.PASS
        4L       || CheckCode.BOOK_TIME_CONFLICT_STATUS
    }

    def getInventory() {
        return Arrays.asList(1L, 2L, 3L)
    }

    def "test downgrade"() {
        when:
        CheckCode result = driverStaticForInventoryCheck.downgrade()

        then:
        result == CheckCode.BOOK_TIME_CONFLICT_STATUS
    }

}