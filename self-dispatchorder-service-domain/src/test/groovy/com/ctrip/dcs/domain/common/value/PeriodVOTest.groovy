package com.ctrip.dcs.domain.common.value

import spock.lang.Specification
import spock.lang.Unroll

class PeriodVOTest extends Specification {

    @Unroll
    def "test contains"() {

        given:
        def periodVO = new PeriodVO(period)

        when:
        boolean result = periodVO.contains(0)

        then:
        result == r

        where:
        period        || r
        "1:00 ~ 2:00" || false
        "1:00 ~ 0:00" || false
        "1:00 ~ 0:01" || true
    }

    @Unroll
    def "test new failed"() {
        when:
        new PeriodVO(period)

        then:
        thrown(IllegalArgumentException)

        where:
        period   || _
        null     || _
        "   "    || _
        ""       || _
        "1:00"   || _
        "1:00 ~" || _
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme