package com.ctrip.dcs.domain.schedule.check.item.impl

import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.DspOrderVO
import com.ctrip.dcs.domain.schedule.check.CheckCode
import com.ctrip.dcs.domain.schedule.check.CheckContext
import com.ctrip.dcs.domain.schedule.check.CheckModel
import com.ctrip.dcs.domain.schedule.value.DspModelVO
import com.ctrip.dcs.domain.schedule.value.carconfig.DelayDspConfigVO
import spock.lang.Specification

class DspOrderDisLimitWithJudgeValidTimeCheckTest extends Specification {
    def check = new DspOrderDisLimitWithJudgeValidTimeCheck()

    def context = Mock(CheckContext)
    def checkModel = Mock(CheckModel)
    def dspOrder = Mock(DspOrderVO)
    def driver = Mock(DriverVO)
    def model = Mock(DspModelVO)

    def setup() {
        context.getDspOrder() >> dspOrder
        checkModel.getModel() >> model
        model.getDriver() >> driver
    }

    def "test check method with no delay dsp config"() {
        given:
        context.getDelayDspConfigMap() >> [:]

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check method with non-first class driver"() {
        given:
        context.getDelayDspConfigMap() >> [(dspOrder.getDspOrderId()): new DelayDspConfigVO(taskDeadTime: "2023-12-31 23:59:59")]
        context.getFirstClassDriverList() >> []
        driver.isRegisterDriver() >> false

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check method with valid time"() {
        given:
        context.getDelayDspConfigMap() >> [(dspOrder.getDspOrderId()): new DelayDspConfigVO(taskDeadTime: "2023-12-31 23:59:59")]
        context.getFirstClassDriverList() >> [driver.getDriverId()]
        driver.isRegisterDriver() >> true

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }

    def "test check method with not valid time"() {
        given:
        context.getDelayDspConfigMap() >> [(dspOrder.getDspOrderId()): new DelayDspConfigVO(taskDeadTime: "2099-12-31 23:59:59")]
        context.getFirstClassDriverList() >> [driver.getDriverId()]
        driver.isRegisterDriver() >> true

        when:
        def result = check.check(checkModel, context)

        then:
        result == CheckCode.PASS
    }
}
