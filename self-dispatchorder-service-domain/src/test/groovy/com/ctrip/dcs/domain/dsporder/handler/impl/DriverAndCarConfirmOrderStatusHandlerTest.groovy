package com.ctrip.dcs.domain.dsporder.handler.impl

import com.ctrip.dcs.domain.common.enums.OrderStatusEnum
import com.ctrip.dcs.domain.common.service.ConfigService
import com.ctrip.dcs.domain.common.service.DistributedLockService
import com.ctrip.dcs.domain.common.service.MessageProviderService
import com.ctrip.dcs.domain.common.value.DriverVO
import com.ctrip.dcs.domain.common.value.ServiceProviderVO
import com.ctrip.dcs.domain.common.value.TransportGroupVO
import com.ctrip.dcs.domain.dsporder.entity.DspOrderDO
import com.ctrip.dcs.domain.dsporder.factory.DspOrderConfirmRecordFactory
import com.ctrip.dcs.domain.dsporder.gateway.PurchaseSupplyOrderGateway
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusContext
import com.ctrip.dcs.domain.dsporder.handler.OrderStatusEvent
import com.ctrip.dcs.domain.dsporder.repository.DspOrderConfirmRecordRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderDetailRepository
import com.ctrip.dcs.domain.dsporder.repository.DspOrderRepository
import com.ctrip.dcs.domain.dsporder.value.DspOrderConfirmRecordVO
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DriverAndCarConfirmOrderStatusHandlerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    MessageProviderService messageProducer
    @Mock
    DspOrderRepository dspOrderRepository
    @Mock
    DspOrderConfirmRecordRepository confirmRecordRepository
    @Mock
    DspOrderConfirmRecordFactory confirmRecordFactory
    @Mock
    PurchaseSupplyOrderGateway purchaseSupplyOrderGateway
    @Mock
    DistributedLockService distributedLockService
    @Mock
    OrderStatusContext orderStatusContext
    @Mock
    DistributedLockService.DistributedLock lock
    @Mock
    DspOrderDO dspOrderDO
    @Mock
    DspOrderConfirmRecordVO dspOrderConfirmRecordVO
    @Mock
    TransportGroupVO transportGroupVO
    @Mock
    DriverVO driverVO
    @Mock
    ConfigService commonConfConfig;
    @Mock
    DspOrderDetailRepository dspOrderDetailRepository;
    @InjectMocks
    DriverAndCarConfirmOrderStatusHandler driverAndCarConfirmOrderStatusHandler

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test handle"() {
        given:
        ServiceProviderVO serviceProviderVO = new ServiceProviderVO(1)
        when(dspOrderDO.getOrderStatus()).thenReturn(OrderStatusEnum.TO_BE_CONFIRMED.getCode())
        when(lock.tryLock()).thenReturn(true)
        when(orderStatusContext.getDspOrderId()).thenReturn("111")
        when(orderStatusContext.getFrom()).thenReturn(OrderStatusEnum.TO_BE_CONFIRMED)
        when(orderStatusContext.getTo()).thenReturn(OrderStatusEnum.DRIVER_CAR_CONFIRMED)
        when(orderStatusContext.getEvent()).thenReturn(OrderStatusEvent.SYSTEM_ASSIGN)
        when(orderStatusContext.getDspOrder()).thenReturn(dspOrderDO)
        when(orderStatusContext.getServiceProvider()).thenReturn(serviceProviderVO)
        when(orderStatusContext.getTransportGroup()).thenReturn(transportGroupVO)
        when(orderStatusContext.getDriver()).thenReturn(driverVO)
        when(dspOrderRepository.find(anyString())).thenReturn(dspOrderDO)
        when(distributedLockService.getLock(anyString())).thenReturn(lock)
        when(confirmRecordFactory.createDriverCarConfirmRecord(orderStatusContext)).thenReturn(dspOrderConfirmRecordVO)

        when:
        driverAndCarConfirmOrderStatusHandler.handle(orderStatusContext)

        then:
        serviceProviderVO.getServiceProviderId() == 1
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme