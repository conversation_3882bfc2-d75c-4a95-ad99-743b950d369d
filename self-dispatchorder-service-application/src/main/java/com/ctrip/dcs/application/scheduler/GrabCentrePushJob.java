package com.ctrip.dcs.application.scheduler;

import com.ctrip.dcs.application.command.SubmitBroadcastGrabExeCmd;
import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.event.*;
import com.ctrip.dcs.domain.schedule.process.impl.GrabCentreProcess;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统播报任务
 * <AUTHOR>
 */
@Component
public class GrabCentrePushJob extends BaseJob {

    private static final Logger logger = LoggerFactory.getLogger(GrabCentrePushJob.class);

    @Autowired
    private GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository;

    @Autowired
    private MessageProviderService messageProviderService;

    @Autowired
    private GrabCentreProcess grabCentreProcess;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Autowired
    private SubmitBroadcastGrabExeCmd submitBroadcastGrabExeCmd;

    @QSchedule("com.crtip.dcs.dsporder.grabcentre.push.job")
    public void execute(Parameter parameter) throws Exception {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        Integer interval = getProperty(parameter, "interval", 5);
        Integer size = getProperty(parameter, "size", 100);
        Long count = grabDspOrderDriverIndexRepository.queryCountGrabPushTime(DateUtil.addMinutes(new Date(), -interval), new Date());
        logger.info("GrabCentrePushJobInfo_TotalCount", "GrabCentrePushJobInfo_TotalCount:" + count);
        if (count <= 0L) {
            logger.info("GrabCentrePushJobInfo_EmptyIndex" + "No broadcast push order");
            return;
        }
        long page = Math.ceilDiv(count, size);
        for (int i = 1; i <= page; i++) {
            logger.info("GrabCentrePushJobInfo_PageCount", "GrabCentrePushJobInfo_PageCount:" + i);
            List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.queryGrabPushTime(DateUtil.addMinutes(new Date(), -interval), new Date(), i, size);
            logger.info("GrabCentrePushJobInfo_IndexCount", "GrabCentrePushJobInfo_IndexCount:" + indexes.size());
            if (CollectionUtils.isEmpty(indexes)) {
                logger.info("GrabCentrePushJobInfo_EmptyIndex" + "No broadcast push order");
                return;
            }
            sendGrabCentreMessage(indexes);
        }
    }

    public void sendGrabCentreMessage(List<GrabDspOrderDriverIndexDO> indexes) {
        long delay = 0L;
        Map<String, List<GrabDspOrderDriverIndexDO>> map = indexes.stream().collect(Collectors.groupingBy(GrabDspOrderDriverIndexDO::getDspOrderId));
        for (Map.Entry<String, List<GrabDspOrderDriverIndexDO>> entry : map.entrySet()) {
            String dspOrderId = entry.getKey();
            List<Long> driverIds = entry.getValue().stream().map(GrabDspOrderDriverIndexDO::getDriverId).distinct().toList();
            messageProviderService.send(new DspOrderGrabCentrePushEvent(dspOrderId, driverIds, delay));
            delay += 500L;
        }
    }
}
