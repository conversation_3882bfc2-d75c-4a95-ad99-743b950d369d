package com.ctrip.dcs.application.command.grabOrderSnapshot;

import com.ctrip.dcs.application.service.DspOrderRewardStrategyService;
import com.ctrip.dcs.domain.common.constants.MetricsConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.*;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.GrabDspOrderSnapshotRecordService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.util.DateUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckConfig;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway;
import com.ctrip.dcs.domain.schedule.gateway.DriverPushConfigGateway;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.dcs.infrastructure.common.config.CheckGrabDriverIndexConfig;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 */
@Component
public class AbstractGrabOrderSnapshotExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(AbstractGrabOrderSnapshotExeCmd.class);

    protected static final String GRAB_BROADCAST_DISTRIBUTE_KEY_PREFIX = SysConstants.APP_ID + "_GRAB_BROADCAST_PROCESS_%s";

    @Autowired
    protected QueryDspOrderService queryDspOrderService;

    @Autowired
    protected GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository;

    @Autowired
    protected GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository;

    @Autowired
    protected SubSkuRepository subSkuRepository;

    @Autowired
    protected QueryDriverService queryDriverService;

    @Autowired
    protected DriverPushConfigGateway driverPushConfigGateway;

    @Autowired
    protected CheckService checkService;

    @Autowired
    protected CheckGrabDriverIndexConfig checkConfig;

    @Autowired
    protected DistributedLockService distributedLockService;

    @Autowired
    protected GrabDspOrderSnapshotRecordService grabDspOrderSnapshotRecordService;

    @Autowired
    protected DspOrderRewardStrategyService dspOrderRewardStrategyService;

    @Autowired
    @Qualifier("grabDspOrderDriverIndexThreadPool")
    protected ExecutorService executorService;

    public boolean validate(DspOrderVO dspOrder, GrabDspOrderSnapshotDO snapshot) {
        if (snapshot.getGrabType().isSystem()) {
            return OrderStatusEnum.isDispatching(dspOrder.getOrderStatus());
        }
        if (snapshot.getGrabType().isVBK()) {
            return OrderStatusEnum.isDispatcherConfirm(dspOrder.getOrderStatus());
        }
        return false;
    }

    public List<DriverPushConfigVO> queryDriverPushConfigs(List<Long> drivers, String category) {
        return driverPushConfigGateway.queryDriverPushConfigs(drivers, category);
    }

    public List<DriverVO> queryDrivers(DspOrderVO dspOrder, List<Long> driverIds) {
        Long supplierId = Optional.of(dspOrder).map(DspOrderVO::getSupplierId).map(Integer::longValue).orElse(null);
        return queryDriverService.queryDriver(Sets.newHashSet(driverIds), CategoryUtils.selfGetParentType(dspOrder), supplierId);
    }

    public SubSkuVO querySubSku(Integer subSkuId) {
        return subSkuRepository.find(subSkuId);
    }

    public DuidVO queryDuid(String duid) {
        return DuidVO.of(duid);
    }

    public void saveOrUpdate(GrabDspOrderSnapshotDO snapshot, List<GrabDspOrderDriverIndexDO> indexes) {
        logger.info("GrabDspOrderSnapshotInfo_" + snapshot.getDspOrderId(), JacksonSerializer.INSTANCE().serialize(snapshot));
        logger.info("GrabDspOrderDriverIndexInfo_" + snapshot.getDspOrderId(), JacksonSerializer.INSTANCE().serialize(indexes));
        grabDspOrderSnapshotRepository.saveOrUpdate(snapshot);
        grabDspOrderDriverIndexRepository.update(indexes);
        grabDspOrderSnapshotRecordService.sendVBKOperationRecord(snapshot);
    }

    @Deprecated
    public CheckConfig queryCheckConfig(Integer bizAreaType, GrabDspOrderSnapshotEventEnum event) {
        List<String> checkItems = checkConfig.getCheckItems(bizAreaType, event.getCode());
        if (CollectionUtils.isEmpty(checkItems)) {
            logger.error("QueryCheckConfigError", "checkItems is empty, bizAreaType: {}, event: {}", bizAreaType, event.getCode());
            MetricsUtil.recordValue(MetricsConstants.NO_GRAB_ORDER_CHECK_ITEMS_ERROR);
            throw ErrorCode.GRAB_ORDER_CHECK_ITEM_ERROR.getBizException();
        }
        return CheckConfig.builder()
                .checkId("0")
                .checkSourceId("S1")
                .dspCheckItem(checkItems)
                .takenCheckItem(checkItems)
                .build();
    }

    public List<CheckModel> check(DspOrderVO dspOrder, String duidStr, List<Long> driverIds) {
        // 解析duid
        DuidVO duid = queryDuid(duidStr);
        // 查询子产品
        SubSkuVO subSku = querySubSku(duid.getSubSkuId());
        // 查询司机信息
        List<DriverVO> drivers = queryDrivers(dspOrder, driverIds);
        return checkService.check(new DspCheckCommand(dspOrder, subSku, drivers, duid, subSku.getCheck()));
    }

    public void updateIndexesCheckCode(List<GrabDspOrderDriverIndexDO> indexes, List<CheckModel> check) {
        Map<Long /*driverId*/, CheckCode> driverCheckMap = Maps.newHashMap();
        for (CheckModel model : check) {
            Long driverId = Optional.ofNullable(model).map(CheckModel::getModel).map(DspModelVO::getDriver).map(DriverVO::getDriverId).orElse(0L);
            CheckCode checkCode = Optional.ofNullable(model).map(CheckModel::getCheckCode).orElse(CheckCode.NULL);
            driverCheckMap.put(driverId, checkCode);
        }
        for (GrabDspOrderDriverIndexDO index : indexes) {
            CheckCode checkCode = (CheckCode) MapUtils.getObject(driverCheckMap, index.getDriverId(), CheckCode.NULL);
            index.updateValid(checkCode.isPass() ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode());
        }
    }

    public void updateIndexesBroadcast(List<GrabDspOrderDriverIndexDO> indexes, List<CheckModel> check, List<DriverPushConfigVO> driverPushConfigs) {
        Map<Long /*driverId*/, DriverPushConfigVO> driverPushConfigMap = Maps.newHashMap();
        for (DriverPushConfigVO config : driverPushConfigs) {
            Long driverId = Optional.ofNullable(config).map(DriverPushConfigVO::getDriverId).orElse(0L);
            driverPushConfigMap.put(driverId, config);
        }
        Map<String, Integer> bizAreaTypeMap = Maps.newHashMap();
        for (CheckModel checkModel : check) {
            String dspOrderId = Optional.ofNullable(checkModel).map(CheckModel::getModel).map(DspModelVO::getOrder).map(DspOrderVO::getDspOrderId).orElse("");
            Integer bizAreaType = Optional.ofNullable(checkModel).map(CheckModel::getModel).map(DspModelVO::getOrder).map(DspOrderVO::getBizAreaType).orElse(BizAreaTypeEnum.CHF.getCtripCode());
            bizAreaTypeMap.put(dspOrderId, bizAreaType);
        }
        for (GrabDspOrderDriverIndexDO index : indexes) {
            DriverPushConfigVO config = (DriverPushConfigVO) MapUtils.getObject(driverPushConfigMap, index.getDriverId(), null);
            // 是否开启播报开关
            Boolean isBroadcast = Optional.ofNullable(config).map(DriverPushConfigVO::getOrderPushStatus).orElse(false);
            Integer bizAreaType = MapUtils.getInteger(bizAreaTypeMap, index.getDspOrderId(), BizAreaTypeEnum.CHF.getCtripCode());
            if (Objects.equals(index.getCategoryCode(), ParentCategoryEnum.JNT) && Objects.equals(bizAreaType, BizAreaTypeEnum.IGT.getCtripCode())) {
                // 接送机境外不播报
                isBroadcast = false;
            }
            index.updateBroadcast(isBroadcast);
        }
    }

    public void updateIndexes(List<GrabDspOrderDriverIndexDO> indexes, List<CheckModel> check, List<DriverPushConfigVO> driverPushConfigs) {
        updateIndexesCheckCode(indexes, check);
        updateIndexesBroadcast(indexes, check, driverPushConfigs);
    }

    public void updateOtherGrabDriverIndex(DspOrderVO dspOrder) {
        if (dspOrder == null || dspOrder.getOrderConfirmRecordDriverId() == null || dspOrder.getOrderConfirmRecordDriverId() == 0) {
            return;
        }
        List<DriverPushConfigVO> configs = queryDriverPushConfigs(Lists.newArrayList(dspOrder.getOrderConfirmRecordDriverId()), dspOrder.getCategoryCode().getParentType());
        Date beginTime = DateUtil.addHours(dspOrder.getEstimatedUseTimeBj(), -4);
        Date endTime = DateUtil.addHours(dspOrder.getPredicServiceStopTimeBj(), 4);
        List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.query(dspOrder.getOrderConfirmRecordDriverId(), beginTime, endTime, dspOrder.getCategoryCode().getParentType());
        for (GrabDspOrderDriverIndexDO index :indexes) {
            if (!Objects.equals(index.getDspOrderId(), dspOrder.getDspOrderId())) {
                executorService.execute(() -> updateOtherGrabDriverIndex(index, configs));
            }
        }
    }

    public void updateOtherGrabDriverIndex(GrabDspOrderDriverIndexDO index, List<DriverPushConfigVO> configs) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(String.format(GRAB_BROADCAST_DISTRIBUTE_KEY_PREFIX, index.getDspOrderId()));
        try {
            if (!lock.tryLock()) {
                return;
            }
            GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(index.getDspOrderId());
            // 查询检查配置
            DspOrderVO otherOrder = queryDspOrderService.queryOrderDetail(index.getDspOrderId());
            boolean ok = validate(otherOrder, snapshot);
            if (!ok) {
                // 状态不合法， 取消抢单快照
                index.updateValid(2);
            } else {
                // 过检查
                List<CheckModel> check = check(otherOrder, snapshot.getDuid(), Lists.newArrayList(index.getDriverId()));
                updateIndexes(Lists.newArrayList(index), check, configs);
            }
            logger.info("GrabDspOrderDriverIndexInfo_" + index.getDspOrderId(), JacksonSerializer.INSTANCE().serialize(index));
            grabDspOrderDriverIndexRepository.update(Lists.newArrayList(index));
        } catch (Exception e) {
            logger.warn("UpdateOtherGrabDriverIndex", "UpdateOtherGrabDriverIndex", e);
        } finally {
            lock.unlock();
        }
    }
}
