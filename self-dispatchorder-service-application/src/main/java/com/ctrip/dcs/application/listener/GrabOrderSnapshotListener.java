package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.api.*;
import com.ctrip.dcs.application.command.grabOrderSnapshot.*;
import com.ctrip.dcs.application.listener.converter.MessageConverter;
import com.ctrip.dcs.application.listener.driver.handler.TourDriverQMQHandler;
import com.ctrip.dcs.application.service.DspOrderRewardStrategyService;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.constants.SysConstants;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotEventEnum;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.base.Splitter;
import credis.java.client.CacheProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.TagType;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;
import java.util.UUID;


/**
 * <AUTHOR>
 */
@Component
public class GrabOrderSnapshotListener {

    private static final Logger logger = LoggerFactory.getLogger(GrabOrderSnapshotListener.class);

    @Autowired
    private CreateGrabOrderDriverIndexExeCmd createGrabOrderDriverIndexExeCmd;

    @Autowired
    private ConfirmGrabOrderSnapshotExeCmd confirmGrabOrderSnapshotExeCmd;

    @Autowired
    private CancelGrabOrderSnapshotExeCmd cancelGrabOrderSnapshotExeCmd;

    @Autowired
    private UpdateGrabOrderSnapshotExeCmd changeGrabOrderSnapshotUseTimeExeCmd;

    @Autowired
    private UpdateGrabOrderRuleExeCmd updateGrabOrderRuleExeCmd;

    @Autowired
    private UpdateGrabDriverIndexExeCmd updateGrabDriverIndexExeCmd;

    @Autowired
    private DspOrderRewardStrategyService dspOrderRewardStrategyService;

    @Autowired
    @Qualifier("RorCacheProvider")
    private CacheProvider trocksCacheProvider;

    /**
     * 抢单映射创建
     * @param message
     */
    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.CREATE_GRAB_DRIVER_ORDER_INDEX, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void createGrabOrderDriverIndex(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            CreateGrabOrderDriverIndexCommand command = MessageConverter.INSTANCE.toCreateGrabOrderDriverIndexCommand(message);
            createGrabOrderDriverIndexExeCmd.execute(command);
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机车辆确认消息-新流程
     * @param message
     */
    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void confirmDriverAndCar(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            ConfirmGrabOrderSnapshotCommand command = MessageConverter.INSTANCE.toConfirmGrabOrderSnapshotCommand(message);
            confirmGrabOrderSnapshotExeCmd.execute(command);
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机确认消息-新流程
     * @param message
     */
    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DRIVER_CONFIRM_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void confirmDriver(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            ConfirmGrabOrderSnapshotCommand command = MessageConverter.INSTANCE.toConfirmGrabOrderSnapshotCommand(message);
            confirmGrabOrderSnapshotExeCmd.execute(command);
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 调度确认消息-新流程
     * @param message
     */
    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_DISPATCHER_CONFIRM_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void confirmDispatcher(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            ConfirmGrabOrderSnapshotCommand command = MessageConverter.INSTANCE.toConfirmGrabOrderSnapshotCommand(message);
            confirmGrabOrderSnapshotExeCmd.execute(command);
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机车辆确认消息-老流程
     * @param message
     */
    @QmqLogTag(tagKeys = {"supplyOrderId"})
    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_TAKEN, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void orderTaken(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            Integer ota = message.getIntProperty("ota");
            if (YesOrNo.isYes(ota)) {
                // OTA订单不处理
                return;
            }
            String dspOrderId = message.getStringProperty("supplyOrderIds");
            confirmGrabOrderSnapshotExeCmd.execute(new ConfirmGrabOrderSnapshotCommand(dspOrderId));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 确认消息-老流程
     * @param message
     */
    @QmqLogTag(tagKeys = {"supplyOrderId"})
    @QmqConsumer(prefix = EventConstants.CAR_QBEST_ORDER_ORDERSTATE_ORDER_DISPATCHER_CONFIRM, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void confirm(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            String dspOrderId = message.getStringProperty("supplyOrderId");
            confirmGrabOrderSnapshotExeCmd.execute(new ConfirmGrabOrderSnapshotCommand(dspOrderId));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 取消消息-新流程
     * @param message
     */
    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DSP_ORDER_CANCEL_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void cancel(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            String dspOrderId = message.getStringProperty("dspOrderId");
            cancelGrabOrderSnapshotExeCmd.execute(new CancelGrabOrderSnapshotCommand(dspOrderId));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 取消消息-老流程
     * @param message
     */
    @QmqLogTag(tagKeys = {"orderId"})
    @QmqConsumer(prefix = EventConstants.OLD_DSP_ORDER_CANCEL_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void cancelOld(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            String dspOrderIds = message.getStringProperty("supplyOrderIds");
            List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(dspOrderIds);
            for (String dspOrderId : list) {
                cancelGrabOrderSnapshotExeCmd.execute(new CancelGrabOrderSnapshotCommand(dspOrderId));
            }
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 用车时间变更消息-新流程
     * @param message
     */
    @QmqLogTag(tagKeys = { "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_ORDER_ESTIMATE_TIME_CHANGE, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeEstimatedUseTime(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            String dspOrderId = message.getStringProperty("dspOrderId");
            changeGrabOrderSnapshotUseTimeExeCmd.execute(new UpdateGrabOrderSnapshotCommand(dspOrderId, GrabDspOrderSnapshotEventEnum.CHANGE_USE_TIME));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 用车时间变更消息-老流程
     * @param message
     */
    @QmqLogTag(tagKeys = { "orderId"})
    @QmqConsumer(prefix = EventConstants.QMQ_ORDER_SYS_EXPECT_BOOK_TIME_CHANGE, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeExpectBookTime(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            String dspOrderIds = message.getStringProperty("supplyOrderIds");
            List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(dspOrderIds);
            for (String dspOrderId : list) {
                changeGrabOrderSnapshotUseTimeExeCmd.execute(new UpdateGrabOrderSnapshotCommand(dspOrderId, GrabDspOrderSnapshotEventEnum.CHANGE_USE_TIME));
            }
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 发单规则变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "supplierId"})
    @QmqConsumer(prefix = EventConstants.CHANGE_GRAB_PUSH_RULE, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeGrabRule(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            Long supplierId = message.getLongProperty("supplierId");
            Long cityId = message.getLongProperty("cityId");
            String categoryCode = message.getStringProperty("categoryCode");
            updateGrabOrderRuleExeCmd.execute(new UpdateGrabOrderRuleCommand(supplierId, cityId, categoryCode));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机抢单配置变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "driverId"})
    @QmqConsumer(prefix = EventConstants.CHANGE_DRIVER_PUSH_CONFIG_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeDriverPushConfig(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            Long driverId = message.getLongProperty("driverId");
            // 清除缓存
            trocksCacheProvider.del("pushConfig-" + driverId);
            updateGrabDriverIndexExeCmd.execute(new UpdateGrabDriverIndexCommand(driverId, GrabDspOrderSnapshotEventEnum.CHANGE_DRIVER_PUSH_CONFIG, ParentCategoryEnum.JNT));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机抢单配置变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "driverId"})
    @QmqConsumer(prefix = EventConstants.CHANGE_GUIDE_PUSH_CONFIG_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeGuidePushConfig(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            Long driverId = message.getLongProperty("driverId");
            // 清除缓存
            trocksCacheProvider.del("pushConfig-" + driverId);
            updateGrabDriverIndexExeCmd.execute(new UpdateGrabDriverIndexCommand(driverId, GrabDspOrderSnapshotEventEnum.CHANGE_DRIVER_PUSH_CONFIG, ParentCategoryEnum.DAY));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机信息变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "drvId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_MODIFY_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeDriverInfo(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            Long driverId = message.getLongProperty("drvId");
            updateGrabDriverIndexExeCmd.execute(new UpdateGrabDriverIndexCommand(driverId, GrabDspOrderSnapshotEventEnum.CHANGE_DRIVER_INFO, ParentCategoryEnum.JNT));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机信息变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "driverId"})
    @QmqConsumer(prefix = EventConstants.TOUR_DRIVER_PLATFORM_DRIVER_INFO_CHANGED,
            tagType = TagType.OR,
            tags = {"tag_driver_supplier_change", "tag_driver_geoid_change", "tag_driver_info_change", "tag_driver_vehicle_change"},
            consumerGroup = "100041593_grab_order_snapshot",
            idempotentChecker = "redisIdempotentChecker")
    public void changeGuideInfo(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            // 公共参数获取
            Long driverId = message.getLongProperty("driverId");
            if (driverId <= 0) {
                logger.info("handleDrvGuideTransportChgMessage", String.format("driverId[%s] is invalid", driverId+"" ));
                return;
            }
            String strDriverProductLineList = message.getStringProperty("driverProductLineList"); // 司机所属产线列表，多个产线以逗号分割
            // 检查司机产线是否含有包车，没有则不处理直接返回
            if (!TourDriverQMQHandler.isCharteredDriverCarChanged(strDriverProductLineList)) {
                logger.info(message.getSubject() + "_" + message.getMessageId(), "driverProductLineList don't contain charteredCar, return. driverProductLineList: " + strDriverProductLineList);
                return;
            }
            updateGrabDriverIndexExeCmd.execute(new UpdateGrabDriverIndexCommand(driverId, GrabDspOrderSnapshotEventEnum.CHANGE_DRIVER_INFO, ParentCategoryEnum.DAY));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机状态变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "drvId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_STATUS_MODIFY_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeDriverState(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            Long driverId = message.getLongProperty("drvId");
            updateGrabDriverIndexExeCmd.execute(new UpdateGrabDriverIndexCommand(driverId, GrabDspOrderSnapshotEventEnum.CHANGE_DRIVER_INFO, ParentCategoryEnum.JNT));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机信息变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "driverId"})
    @QmqConsumer(prefix = EventConstants.TOUR_DRIVER_PLATFORM_DRIVER_STATUS_CHANGED,
            tagType = TagType.OR,
            tags = {"tag_driver_offline","tag_driver_freeze"},
            consumerGroup = "100041593_grab_order_snapshot",
            idempotentChecker = "redisIdempotentChecker")
    public void changeGuideState(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            // 公共参数获取
            Long driverId = message.getLongProperty("driverId");
            if (driverId <= 0) {
                logger.info("handleDrvGuideTransportChgMessage", String.format("driverId[%s] is invalid", driverId+"" ));
                return;
            }
            String strDriverProductLineList = message.getStringProperty("driverProductLineList"); // 司机所属产线列表，多个产线以逗号分割
            // 检查司机产线是否含有包车，没有则不处理直接返回
            if (!TourDriverQMQHandler.isCharteredDriverCarChanged(strDriverProductLineList)) {
                logger.info(message.getSubject() + "_" + message.getMessageId(), "driverProductLineList don't contain charteredCar, return. driverProductLineList: " + strDriverProductLineList);
                return;
            }
            updateGrabDriverIndexExeCmd.execute(new UpdateGrabDriverIndexCommand(driverId, GrabDspOrderSnapshotEventEnum.CHANGE_DRIVER_INFO, ParentCategoryEnum.DAY));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }


    /**
     * 司机车辆变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "drvId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_VEHICLE_MODIFY_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeCarInfo(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            Long driverId = message.getLongProperty("drvId");
            updateGrabDriverIndexExeCmd.execute(new UpdateGrabDriverIndexCommand(driverId, GrabDspOrderSnapshotEventEnum.CHANGE_CAR_INFO, ParentCategoryEnum.JNT));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机车辆变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "driverId"})
    @QmqConsumer(prefix = EventConstants.TOUR_DRIVER_PLATFORM_VEHICLE_INFO_CHANGED,
            consumerGroup = "100041593_grab_order_snapshot",
            idempotentChecker = "redisIdempotentChecker")
    public void changeGuideCarInfo(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            Long driverId = message.getLongProperty("driverId");
            if (driverId <= 0) {
                logger.info("handleDrvGuideTransportChgMessage", String.format("driverId[%s] is invalid", driverId+"" ));
                return;
            }
            updateGrabDriverIndexExeCmd.execute(new UpdateGrabDriverIndexCommand(driverId, GrabDspOrderSnapshotEventEnum.CHANGE_CAR_INFO, ParentCategoryEnum.DAY));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 司机车辆变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "driverId"})
    @QmqConsumer(prefix = EventConstants.DRIVER_LEAVE_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeDriverLeave(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            Long driverId = message.getLongProperty("driverId");
            updateGrabDriverIndexExeCmd.execute(new UpdateGrabDriverIndexCommand(driverId, GrabDspOrderSnapshotEventEnum.CHANGE_DRIVER_LEAVE, ParentCategoryEnum.JNT));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 包车派发单信息变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.CHANGE_CHARTERED_LINE_DSP_ORDER_TOPIC, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeCharteredDspOrder(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            String dspOrderId = message.getStringProperty("dspOrderId");
            changeGrabOrderSnapshotUseTimeExeCmd.execute(new UpdateGrabOrderSnapshotCommand(dspOrderId, GrabDspOrderSnapshotEventEnum.CHANGE_USE_TIME));
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }

    /**
     * 包车派发单信息变更
     * @param message
     */
    @QmqLogTag(tagKeys = { "dspOrderId"})
    @QmqConsumer(prefix = EventConstants.CHANGE_GRAB_DSP_ORDER_SNAPSHOT, consumerGroup = "100041593_grab_order_snapshot", idempotentChecker = "redisIdempotentChecker")
    public void changeGrabDspOrderSnapshot(Message message) {
        try {
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            String dspOrderId = message.getStringProperty("dspOrderId");
            dspOrderRewardStrategyService.rebuildRewardStrategy(dspOrderId);
        } catch (Exception e) {
            logger.warn("ChangeGrabOrderSnapshotListenerError", e);
            throw new NeedRetryException(System.currentTimeMillis() + SysConstants.RETRY_MILLISECONDS, "ChangeGrabOrderSnapshotListenerError");
        }
    }
}
