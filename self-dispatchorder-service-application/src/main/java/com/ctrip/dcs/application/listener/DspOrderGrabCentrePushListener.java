package com.ctrip.dcs.application.listener;

import com.ctrip.dcs.application.command.SubmitBroadcastGrabExeCmd;
import com.ctrip.dcs.application.scheduler.GrabCentrePushJob;
import com.ctrip.dcs.domain.common.constants.ConfigKey;
import com.ctrip.dcs.domain.common.constants.EventConstants;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum;
import com.ctrip.dcs.domain.common.enums.UdlEnum;
import com.ctrip.dcs.domain.common.enums.YesOrNo;
import com.ctrip.dcs.domain.common.service.ConfigService;
import com.ctrip.dcs.domain.common.service.MessageProviderService;
import com.ctrip.dcs.domain.common.util.CatUtil;
import com.ctrip.dcs.domain.common.util.JsonUtil;
import com.ctrip.dcs.domain.common.util.MetricsUtil;
import com.ctrip.dcs.domain.common.value.DriverUdlVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.event.BroadcastEvent;
import com.ctrip.dcs.domain.schedule.event.GrabCentreEvent;
import com.ctrip.dcs.domain.schedule.event.GuideBroadcastEvent;
import com.ctrip.dcs.domain.schedule.event.GuideGrabCentreEvent;
import com.ctrip.dcs.domain.schedule.gateway.DriverInServiceGateway;
import com.ctrip.dcs.domain.schedule.gateway.SysSwitchConfigGateway;
import com.ctrip.dcs.domain.schedule.process.impl.GrabCentreProcess;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CommonService;
import com.ctrip.dcs.infrastructure.adapter.trocks.TRocksProviderAdapter;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.dianping.cat.utils.Pair;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;


/**
 * <AUTHOR>
 */
@Component
public class DspOrderGrabCentrePushListener {

    private static final Logger logger = LoggerFactory.getLogger(DspOrderGrabCentrePushListener.class);

    @Autowired
    private GrabDspOrderDriverIndexRepository grabDspOrderDriverIndexRepository;

    @Autowired
    private GrabDspOrderSnapshotRepository grabDspOrderSnapshotRepository;

    @Autowired
    private MessageProviderService messageProviderService;

    @Autowired
    private GrabCentreProcess grabCentreProcess;

    @Autowired
    @Qualifier("broadcastGrabConfig")
    private ConfigService broadcastGrabConfig;

    @Autowired
    private SubmitBroadcastGrabExeCmd submitBroadcastGrabExeCmd;
    @Resource
    SysSwitchConfigGateway sysSwitchConfigGateway;
    @Resource
    CommonService commonService;

    @QmqLogTag(tagKeys = {"dspOrderId"})
    @QmqConsumer(prefix = EventConstants.DCS_DSP_ORDER_GRAB_CENTRE_PUSH_EXECUTE, consumerGroup = "100041593", idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        String dspOrderId = message.getStringProperty("dspOrderId");
        String driverIds = message.getStringProperty("driverIds");
        if (StringUtils.isBlank(dspOrderId) || StringUtils.isBlank(driverIds)) {
            return;
        }
        List<Long> driverIdList = Splitter.on(",").splitToList(driverIds).stream().map(Long::parseLong).toList();
        GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(dspOrderId);
        logger.info("DspOrderGrabCentrePushListenerInfo_Snapshot", JacksonSerializer.INSTANCE().serialize(snapshot));
        if (Objects.isNull(snapshot) || !Objects.equals(snapshot.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.GRAB)) {
            return;
        }
        List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.query(dspOrderId, driverIdList);
        logger.info("DspOrderGrabCentrePushListenerInfo_Index", JacksonSerializer.INSTANCE().serialize(indexes));
        List<GrabDspOrderDriverIndexDO> list = Optional.ofNullable(indexes)
                .orElse(Collections.emptyList())
                .stream()
                .filter(this::filter)
                .toList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        sendGrabCentreMessage(snapshot, list);
    }

    private void sendGrabCentreMessage(GrabDspOrderSnapshotDO snapshot, List<GrabDspOrderDriverIndexDO> indexes) {
        if (!sysSwitchConfigGateway.getDriverQmqAddUcsSwitch()) {
            sendGrabCentreMsg(indexes, snapshot.getDuid(), 0L);
            return;
        }
        final AtomicLong delay = new AtomicLong(0L);
        Map<UdlEnum, Pair<DriverUdlVO, List<GrabDspOrderDriverIndexDO>>> groupMap = commonService.getGroupGrabDspOrderDriverIndex(indexes);
        for (Map.Entry<UdlEnum, Pair<DriverUdlVO, List<GrabDspOrderDriverIndexDO>>> entry : groupMap.entrySet()) {
            Pair<DriverUdlVO, List<GrabDspOrderDriverIndexDO>> pair = entry.getValue();
            if (Objects.isNull(pair.getKey())) {
                logger.warn("dspOrderGrabCentrePushListener sendGrabCentreMessage", "driverUdlVO is null." + JsonUtil.toJson(pair));
                continue;
            }
            logger.info("dspOrderGrabCentrePushListener sendGrabCentreMessage", "driverUdlVO:" + JsonUtil.toJson(pair.getKey()));
            long delayVal = CatUtil.doWithUdlOverride(() -> sendGrabCentreMsg(pair.getValue(), snapshot.getDuid(), delay.get()), pair.getKey().getUdl(), pair.getKey().getRequestFrom());
            delay.set(delayVal);
        }
    }
    
    
    public long sendGrabCentreMsg(List<GrabDspOrderDriverIndexDO> indexes, String duid, long delay) {
        List<List<GrabDspOrderDriverIndexDO>> partition = Lists.partition(indexes, 100);
        for (List<GrabDspOrderDriverIndexDO> list : partition) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            ParentCategoryEnum categoryCode = list.getFirst().getCategoryCode();
            DuidVO duidVO = DuidVO.of(duid);
            MetricsUtil.recordValue("dsp_type_" + duidVO.getDspType() + "_" + "success", list.size());
            if (Objects.equals(ParentCategoryEnum.DAY, categoryCode)) {
                // 包车
                messageProviderService.send(new GuideGrabCentreEvent(duidVO.getDspOrderId(), duid, list, delay));
            } else {
                // 接送机
                messageProviderService.send(new GrabCentreEvent(duid, list, delay));
            }
            delay += broadcastGrabConfig.getLong(ConfigKey.BROADCAST_DRIVER_DELAY_KEY, NumberUtils.LONG_ZERO);
        }
        return delay;
    }

    public boolean filter(GrabDspOrderDriverIndexDO item) {
        return Objects.nonNull(item)
                && submitBroadcastGrabExeCmd.isGrayscaleCity(item.getCityId())
                && grabCentreProcess.notPushDriverByOrder(item.getDriverId(), item.getDspOrderId())
                && grabCentreProcess.notPushDriverByDuration(item.getDriverId());
    }
}
