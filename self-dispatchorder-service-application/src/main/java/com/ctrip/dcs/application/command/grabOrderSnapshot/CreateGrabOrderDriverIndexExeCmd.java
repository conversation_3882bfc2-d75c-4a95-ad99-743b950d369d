package com.ctrip.dcs.application.command.grabOrderSnapshot;

import com.ctrip.dcs.application.command.api.CreateGrabOrderDriverIndexCommand;
import com.ctrip.dcs.domain.common.enums.ErrorCode;
import com.ctrip.dcs.domain.common.enums.GrabDspOrderSnapshotStatusEnum;
import com.ctrip.dcs.domain.common.enums.OrderStatusEnum;
import com.ctrip.dcs.domain.common.exception.DistributedLockRejectedException;
import com.ctrip.dcs.domain.common.service.DistributedLockService;
import com.ctrip.dcs.domain.common.service.QueryDriverService;
import com.ctrip.dcs.domain.common.service.QueryDspOrderService;
import com.ctrip.dcs.domain.common.util.CategoryUtils;
import com.ctrip.dcs.domain.common.value.DriverVO;
import com.ctrip.dcs.domain.common.value.DspOrderVO;
import com.ctrip.dcs.domain.common.value.DuidVO;
import com.ctrip.dcs.domain.schedule.check.CheckCode;
import com.ctrip.dcs.domain.schedule.check.CheckModel;
import com.ctrip.dcs.domain.schedule.check.command.DspCheckCommand;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderDriverIndexDO;
import com.ctrip.dcs.domain.schedule.entity.GrabDspOrderSnapshotDO;
import com.ctrip.dcs.domain.schedule.gateway.DriverDomainServiceGateway;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderDriverIndexRepository;
import com.ctrip.dcs.domain.schedule.repository.GrabDspOrderSnapshotRepository;
import com.ctrip.dcs.domain.schedule.repository.SubSkuRepository;
import com.ctrip.dcs.domain.schedule.service.CheckService;
import com.ctrip.dcs.domain.schedule.value.DriverPushConfigVO;
import com.ctrip.dcs.domain.schedule.value.DspModelVO;
import com.ctrip.dcs.domain.schedule.value.SubSkuVO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class CreateGrabOrderDriverIndexExeCmd extends AbstractGrabOrderSnapshotExeCmd {

    private static final Logger logger = LoggerFactory.getLogger(CreateGrabOrderDriverIndexExeCmd.class);


    public void execute(CreateGrabOrderDriverIndexCommand command) {
        DistributedLockService.DistributedLock lock = distributedLockService.getLock(String.format(GRAB_BROADCAST_DISTRIBUTE_KEY_PREFIX, command.getDspOrderId()));
        try {
            if (!lock.tryLock()) {
                throw ErrorCode.UPDATE_GRAB_ORDER_SNAPSHOT_ERROR.getBizException();
            }
            DspOrderVO dspOrder = queryDspOrderService.queryOrderDetail(command.getDspOrderId());
            GrabDspOrderSnapshotDO snapshot = grabDspOrderSnapshotRepository.query(command.getDspOrderId());
            logger.info("GrabDspOrderSnapshotInfo", JacksonSerializer.INSTANCE().serialize(snapshot));
            boolean ok = validate(dspOrder, snapshot);
            if (!ok) {
                logger.info("CreateGrabOrderDriverIndexExeCmdInfo", "order status illegal! dspOrderId: {}", command.getDspOrderId());
                return;
            }
            if (Objects.equals(snapshot.getGrabStatus(), GrabDspOrderSnapshotStatusEnum.INIT)) {
                snapshot.setGrabStatus(GrabDspOrderSnapshotStatusEnum.GRAB);
                int count = grabDspOrderSnapshotRepository.updateGrabStatus(snapshot, GrabDspOrderSnapshotStatusEnum.GRAB, GrabDspOrderSnapshotStatusEnum.INIT);
                if (count == 0) {
                    // 更新失败，抢单快照状态已经被变更
                    logger.info("CreateGrabOrderDriverIndexExeCmdInfo", "grab status change! dspOrderId: {}", command.getDspOrderId());
                    throw new BizException("GrabDspOrderSnapshotStatusChange");
                }
            }
            // 派单检查
            List<CheckModel> check = check(dspOrder, snapshot.getDuid(), command.getDriverIds());
            // 司机抢单配置
            List<DriverPushConfigVO> driverPushConfigs = queryDriverPushConfigs(command.getDriverIds(), dspOrder.getCategoryCode().getParentType());
            // 查询映射
            List<GrabDspOrderDriverIndexDO> indexes = grabDspOrderDriverIndexRepository.query(command.getDspOrderId(), command.getDriverIds());
            // 更新映射
            updateIndexes(indexes, check, driverPushConfigs);
            logger.info("GrabDspOrderDriverIndexInfo_" + dspOrder.getDspOrderId(), JacksonSerializer.INSTANCE().serialize(indexes));
            grabDspOrderDriverIndexRepository.update(indexes);
        } catch (Exception e) {
            logger.warn("CreateGrabOrderDriverIndexError", e);
            throw ErrorCode.UPDATE_GRAB_ORDER_SNAPSHOT_ERROR.getBizException();
        } finally {
            lock.unlock();
        }
    }
}
